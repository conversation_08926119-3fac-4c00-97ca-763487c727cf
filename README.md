# background_v4

## 背景

相关依赖：
[vue(2.x)](https://v2.cn.vuejs.org/v2/guide/) | 
[element-ui(2.x)](https://element.eleme.cn/#/zh-CN/component/installation) | 
[vue-router(3.x)](https://v3.router.vuejs.org/zh/) |
[vuex(3.x)](https://vuex.vuejs.org/zh/)

更多依赖请看`package.json` dependencies


### 项目结构

```js
my-project-name/
  // 源码目录
|- docs           // 项目的细化文档目录（可选）
|- deploy         // 部署配置脚本
|- lib            // 通用工具包目录（目前没用到）
|- node_modules   // 下载的依赖包
|- environments   // 动态代理地址配置目录（修过无需重启服务，直接刷新页面即可）
    |- proxy-config.json // 代理地址
|- plop-templates // 命令行run new模板配置目录
    |- default    // 默认模板
        |- index.hbs   // 模板
        |- prompt.js   // 默认模板配置
|- public         // 静态页面目录（打包时public文件夹中的文件会原封不动的放到dist文件夹中）
    |- index.html // 项目入口
    |- favicon.ico
|- tests          // 测试
    |- unit       // 单元测试
        |- example.spec.js // 测试样例
|- src            // 源码目录
    |- api        // http 请求api封装目录
        |- modules  // api模块
            |- asyncapi.js // 后端生成的api请求接口 （http://cashier-v4.debug.packertec.com/api/client_codegenerator/jscode）
        |- public   // api模块的另一种封装方式
        |- index.js // 向外入口文件
    |- assets     // 静态资源目录，这里的资源会被wabpack构建
        |- data   // 
        |- img    // 图片存放目录
        |- js     // 公共 js 文件目录
    |- components     // 公共组件 大写驼峰命名（PascalCase）
      |- global       // 自动注册组件目录
      |- UserGroupSelect       // 用户分组组件
      |- UserDepartmentSelect       // 用户部门组件
      |- LazySelect       // 懒加载select组件
      |- OrganizationConsumeList       // 消费点tree
      |- OrganizationSelect       // 组织select
      |- SelectTree       // 
    	|- TreeSelect
    		|- index.vue 
      ...
    |- directives     // 自定义指令
    |- filters        // 自定义过滤器
    |- mixins         // 混入
    |- plugins        // 插件
    |- icons          // 图标（自动引入，使用方式 <svg-icon icon-class={icon}/> icon即svg的名）
        |- svg        // 图标svg文件
    |- lang           // 国际化语言配置
    |- layout         // 整体框架布局
    |- router         // 路由配置
        |- merchantRouter.js  // 商户路由表
        |- superRouter.js  // 超管路由表
        |- asyncRoutes.js  // 动态路由汇总
    |- store          // 全局状态管理
        |- modules    // 模块 
            |- navTabs.js    // 顶部navTab数据配置
            |- permission.js    // 权限
            |- user.js    // 用户信息
    |- utils          // 工具存放目录
        |- request.js // 公共请求工具
        |- i18n.js       // 国际化工具
        |- constants.js  // 公共常量文件
        |- form-validata.js  // 表单校验工具
        |- validata.js  // 表单校验正则
        |- layout-tab.js   // ...
        |- pagination.js   // 分页工具
    |- style          // 样式
    |- views          // 页面存放目录
        |- account    // 账号设置（当前登录的用户，右上角）
        |- error      // 通用出错页面
        |- excel      // 通用导出excel页面
        |- print      // 通用打印页面
        |- login      // 登录页面
        |- merchant   // 商户端页面
        |- super      // 超管端页面
        |- public     // 公共页面
        |- redirect   // 重定向页面（目前没用起来）
    	|- settings.js // 公共设置（暂时没用起来）
    	|- permission.js // 路由权限配置文件
    |- App.vue        // 根组件
    |- main.js        // 入口文件
    |- tests          // 测试用例
    |- .env.prod      // 生成环境配置
    |- .env.test      // 测试环境配置
    |- .browserslistrc// 浏览器兼容配置文件
    |- .editorconfig  // 编辑器配置文件
    |- .eslintignore  // eslint 忽略规则
    |- .eslintrc.js   // eslint 规则
    |- .gitignore     // git 忽略规则
    |- .prettierrc    // prettier 格式化配置
    |- .jsbeautifyrc  // jsbeautify 格式化配置
    |- .gitlab-ci.yml // gitlab ci 配置
    |- babel.config.js // babel 规则
    |- plopfile.js // 命令行run new 配置
    |- docker-compose.yml // Docker 部署文件
    |- jest.config.js // 单元测试配置
    |- postcss.config.js // 
    |- babel.config.js // babel 配置
    |- jsconfig.json
    |- package-lock.json
    |- package.json // 依赖
    |- README.md // 项目 README
    |- vue.config.js // webpack 配置
    ...
    
```



### 配置

#### 1、引入cdn文件（或外部文件）
请适用vue.config copy.js配置文件

```js
// cdn
public index.html 中使用
    <% if (process.env.NODE_ENV === 'production') { %>
      <% for(var css of htmlWebpackPlugin.options.cdn.css) { %>
        <link href="<%=css%>" rel="preload" as="style">
        <link rel="stylesheet" href="<%=css%>" as="style">
      <% } %>
      <% for(var js of htmlWebpackPlugin.options.cdn.js) { %>
        <link href="<%=js%>" rel="preload" as="script">
        <script src="<%=js%>"></script>
      <% } %>
    <% } %>
```

### 启动和打包

*线上使用ci/cd自动打包，无需运行打包命令*

```bash
#安装依赖
npm install
#or
yarn install

# 本地启动
npm run serve

# 打包（测试版）
npm run build:test

# 打包（生成环境）
npm run build:prod

```



*注意*
* 组件的封装不要和element的出发同一个emit事件
```js
// 适当使用  
  model: {
    prop: 'showDialog',
    event: 'changeShow'
  }
```