<template>
  <div class="abnormal-deduction-drawer">
    <custom-drawer
      :show.sync="showDrawer"
      :size="size"
      :title="title"
      v-bind="$attrs"
      v-on="$listeners"
      :confirmText="type === 'deduction' ? '确认并扣款' : '确认'"
      @confirm="submitHandle"
      @cancel="closeHandle"
      :loading="isLoading"
    >
      <div class="abnormal-deduction-drawer-wrapp">
        <!-- 标签页 -->
        <div class="tab-section">
          <div class="tab-buttons">
            <el-button
              v-if="type === 'deduction' && dialogInfo.card_info_id"
              :class="['tab-btn', { active: activeTab === 'scan' }]"
              @click="activeTab = 'scan'"
            >
              扣款
            </el-button>
            <el-button :class="['tab-btn', { active: activeTab === 'searchUser' }]" @click="activeTab = 'searchUser'">
              查找用户
            </el-button>
            <el-button :class="['tab-btn', { active: activeTab === 'faceCheck' }]" @click="activeTab = 'faceCheck'">
              人脸校验
            </el-button>
          </div>
        </div>

        <!-- 搜索结果区域 -->
        <div v-if="activeTab === 'scan'">
          <div class="search-results-section">
            <div class="section-title">搜索结果</div>

            <div class="user-item">
              <div class="user-avatar">
                <img v-if="dialogInfo.face_url" :src="dialogInfo.face_url" />
                <div v-else class="selection-avatar-placeholder">暂无人脸</div>
              </div>
              <div class="user-info">
                <div class="user-name">{{ dialogInfo.name }} ({{ dialogInfo.person_no }})</div>
                <div class="user-phone">{{ dialogInfo.phone }}</div>
                <div class="user-group">{{ dialogInfo.group_name }}</div>
              </div>
              <div class="user-balance">
                <div class="balance-row">
                  <div class="balance-label">储值余额</div>
                  <div class="balance-value">¥ {{ dialogInfo.balance | formatMoney }}</div>
                </div>
                <div class="balance-row">
                  <div class="balance-label">补贴余额</div>
                  <div class="balance-value">¥ {{ dialogInfo.subsidy_balance | formatMoney }}</div>
                </div>
                <div class="balance-row">
                  <div class="balance-label">赠送余额</div>
                  <div class="balance-value">¥ {{ dialogInfo.complimentary_balance | formatMoney }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 查找用户 -->
        <div v-if="activeTab === 'searchUser'">
          <!-- 查询条件 -->
          <div class="search-conditions-section">
            <div class="section-title">查询条件</div>
            <el-form
              :model="searchForm"
              label-width="80px"
              size="small"
              class="search-form"
              ref="searchForm"
              :rules="searchFormRules"
            >
              <el-form-item label="动账组织" prop="organization">
                <el-select v-model="searchForm.organization" placeholder="请选择动账组织" class="w-250">
                  <el-option
                    v-for="item in organizationList"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  ></el-option>
                </el-select>
              </el-form-item>

              <el-form-item label="用户姓名" prop="person_name">
                <el-input v-model="searchForm.person_name" class="w-250" maxlength="20" placeholder="请输入"></el-input>
              </el-form-item>

              <el-form-item label="手机号" prop="phone">
                <el-input v-model="searchForm.phone" class="w-250" maxlength="13" placeholder="请输入"></el-input>
              </el-form-item>

              <el-form-item label="人员编号" prop="person_no">
                <el-input v-model="searchForm.person_no" class="w-250" maxlength="20" placeholder="请输入"></el-input>
              </el-form-item>

              <el-form-item>
                <el-button
                  type="primary"
                  style="width: 100px"
                  :disabled="!searchForm.person_name && !searchForm.phone && !searchForm.person_no"
                  @click="searchUsers"
                >
                  查询
                </el-button>
              </el-form-item>
            </el-form>
          </div>

          <!-- 搜索结果 -->
          <div class="search-results-section">
            <div class="section-title">搜索结果</div>
            <div class="user-list-container">
              <!-- 暂无搜索结果 -->
              <div v-if="searchResults.length === 0" class="no-search-results">暂无搜索结果</div>
              <!-- 搜索结果列表 -->
              <div
                v-for="user in searchResults"
                :key="user.id"
                class="user-item"
                :class="{ selected: selectedSearchUser && selectedSearchUser.id === user.id }"
                @click="selectSearchUser(user)"
              >
                <div class="user-avatar">
                  <img v-if="user.face_url" :src="user.face_url" />
                  <div v-else class="selection-avatar-placeholder">暂无人脸</div>
                </div>
                <div class="user-info">
                  <div class="user-name">{{ user.name }} ({{ user.person_no }})</div>
                  <div class="user-phone">{{ user.phone }}</div>
                  <div class="user-group">{{ user.group_name }}</div>
                </div>
                <div class="user-balance">
                  <div class="balance-row">
                    <div class="balance-label">储值余额</div>
                    <div class="balance-value">¥ {{ user.balance | formatMoney }}</div>
                  </div>
                  <div class="balance-row">
                    <div class="balance-label">补贴余额</div>
                    <div class="balance-value">¥ {{ user.subsidy_balance | formatMoney }}</div>
                  </div>
                  <div class="balance-row">
                    <div class="balance-label">赠送余额</div>
                    <div class="balance-value">¥ {{ user.complimentary_balance | formatMoney }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 人脸校验 -->
        <div v-if="activeTab === 'faceCheck'">
          <!-- 拍摄人脸区域 -->
          <div v-if="dialogInfo.face_url">
            <div class="face-capture-section">
              <div class="capture-title">抓拍人脸</div>
              <div class="face-capture-area">
                <div class="face-image-container" v-if="dialogInfo.face_url">
                  <img :src="dialogInfo.face_url" class="captured-face-img" />
                </div>
                <el-button type="primary" class="capture-btn" @click="captureFace">在线校验</el-button>
              </div>
            </div>

            <!-- 当前选择区域 -->
            <div class="current-selection-section">
              <div class="selection-title">当前选择</div>
              <div class="selection-table" v-if="Object.keys(selectedFaceUser).length">
                <table class="selection-table-content">
                  <tr>
                    <td class="selection-avatar-cell">
                      <img v-if="selectedFaceUser.face_url" :src="selectedFaceUser.face_url" class="selection-avatar" />
                      <div v-else class="selection-avatar-placeholder">暂无人脸</div>
                    </td>
                    <td class="selection-name">{{ selectedFaceUser.name }}</td>
                    <td class="selection-id">{{ selectedFaceUser.person_no }}</td>
                    <td class="selection-phone">{{ selectedFaceUser.phone }}</td>
                    <td class="selection-score">{{ selectedFaceUser.score }}</td>
                  </tr>
                </table>
              </div>
              <div v-else class="no-selection-placeholder">当前订单未匹配人脸</div>
            </div>

            <!-- 校验结果表格 -->
            <div class="verification-results-section">
              <div class="results-title">校验结果</div>
              <el-table
                :data="verificationResults"
                style="width: 100%"
                stripe
                header-row-class-name="ps-table-header-row"
                max-height="400"
                border
              >
                <el-table-column type="index" label="排序" width="80" align="center"></el-table-column>
                <el-table-column prop="face_url" label="近似人脸" width="100" align="center">
                  <template slot-scope="scope">
                    <img v-if="scope.row.face_url" :src="scope.row.face_url" class="table-face-img" />
                    <div v-else class="selection-avatar-placeholder">暂无人脸</div>
                  </template>
                </el-table-column>
                <el-table-column prop="name" label="姓名" align="center"></el-table-column>
                <el-table-column prop="person_no" label="人员编号" align="center"></el-table-column>
                <el-table-column prop="phone" label="手机号" align="center"></el-table-column>
                <el-table-column prop="score" label="识别分数" align="center"></el-table-column>
                <el-table-column label="操作" width="100" align="center">
                  <template slot-scope="scope">
                    <el-button
                      type="text"
                      size="small"
                      :disabled="scope.row.user_id === selectedFaceUser.user_id"
                      @click="selectFaceUser(scope.row)"
                    >
                      选择
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
          <div v-else class="no-face-capture">当前订单无抓拍人脸</div>
        </div>

        <!-- 扣款方式 -->
        <div
          class="payment-method-section"
          v-if="(activeTab !== 'faceCheck' || dialogInfo.face_url) && type === 'deduction'"
        >
          <div class="section-title">扣款方式</div>
          <el-radio-group v-model="paymentMethod" class="payment-options">
            <el-radio label="1">重新扣款</el-radio>
            <el-radio label="2">原价扣款</el-radio>
          </el-radio-group>
        </div>
      </div>
    </custom-drawer>
  </div>
</template>

<script>
// import OrganizationSelect from '@/components/OrganizationSelect'
import { mapGetters } from 'vuex'
export default {
  name: 'AbnormalDeductionDrawer',
  props: {
    isshow: Boolean,
    loading: Boolean,
    type: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: '基础设置'
    },
    size: {
      type: String,
      default: '700px'
    },
    dialogInfo: {
      type: Object,
      default() {
        return {}
      }
    },
    drawerTabs: {
      type: String,
      default: ''
    }
  },
  components: {
    // OrganizationSelect
  },
  data() {
    return {
      isLoading: false,
      confirmText: '确认',
      activeTab: 'searchUser',
      paymentMethod: '1',
      searchForm: {
        organization: '',
        person_name: '',
        phone: '',
        person_no: ''
      },
      searchFormRules: {
        organization: [{ required: true, message: '请选择动账组织', trigger: 'change' }],
        person_name: [{ validator: this.validateAtLeastOne, trigger: 'blur' }],
        phone: [{ validator: this.validateAtLeastOne, trigger: 'blur' }],
        person_no: [{ validator: this.validateAtLeastOne, trigger: 'blur' }]
      },
      organizationList: [],
      selectedSearchUser: {},
      searchResults: [], // 默认为空数组，显示"暂无搜索结果"
      selectedFaceUser: {},
      verificationResults: []
    }
  },
  computed: {
    showDrawer: {
      get() {
        return this.isshow
      },
      set(val) {
        this.$emit('update:isshow', val)
      }
    },
    ...mapGetters(['userInfo'])
  },
  // watch: {
  //   isshow: {
  //     handler(newVal) {
  //       if (newVal) {
  //         this.activeTab = this.drawerTabs
  //         this.organizationList = this.userInfo.organizationList
  //       }
  //     },
  //     immediate: true
  //   }
  // },
  created() {
    this.activeTab = this.drawerTabs
    this.organizationList = this.userInfo.organizationList
  },
  mounted() {},
  methods: {
    // 自定义验证器：至少填写一个字段（用户姓名、手机号、人员编号）
    // eslint-disable-next-line no-unused-vars
    validateAtLeastOne(rule, value, callback) {
      // eslint-disable-next-line camelcase
      const { person_name, phone, person_no } = this.searchForm
      // eslint-disable-next-line camelcase
      if (!person_name && !phone && !person_no) {
        callback(new Error('请至少填写用户姓名、手机号或人员编号中的一项'))
      } else {
        callback()
      }
    },
    async getOperationData() {
      this.isLoading = true
      const [err, res] = await this.$to(
        this.$apis.apiBackgroundOrderOrderOfflineGetOrderUserInfoPost({
          org_ids: [this.searchForm.organization],
          person_name: this.searchForm.person_name,
          phone: this.searchForm.phone,
          person_no: this.searchForm.person_no
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        // 清空选择数据
        this.selectedSearchUser = {}
        this.searchResults = res.data
      } else {
        this.$message.error(res.msg)
      }
    },
    async getOrderFace() {
      this.isLoading = true
      const [err, res] = await this.$to(
        this.$apis.apiBackgroundOrderOrderOfflineGetOrderFacePost({
          company_id: this.dialogInfo.company,
          face_url: this.dialogInfo.face_url,
          count: 10
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        // 清空选择数据
        this.selectedFaceUser = {}
        this.verificationResults = res.data.map(item => {
          item.score = item.score.toFixed(2)
          return item
        })
      } else {
        this.$message.error(res.msg)
      }
    },

    closeHandle() {
      this.showDrawer = false
    },
    submitHandle() {
      if (this.activeTab === 'faceCheck') {
        if (!this.dialogInfo.face_url) return this.$message.error('暂无人脸')
        // 人脸校验
        if (!Object.keys(this.selectedFaceUser).length) return this.$message.error('请选择用户')
      }
      if (this.activeTab === 'searchUser') {
        // 查找用户
        if (!Object.keys(this.selectedSearchUser).length) return this.$message.error('请选择用户')
      }
      this.$confirm(`确定该操作吗？`, '提示', {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            if (this.activeTab === 'faceCheck') {
              this.setOrderBindUserInfo(this.selectedFaceUser.user_id)
            }
            if (this.activeTab === 'searchUser') {
              this.setOrderBindUserInfo(this.selectedSearchUser.id)
            }
            if (this.activeTab === 'scan') {
              // 扫描
              this.repayOrder(this.dialogInfo.p_id, this.paymentMethod === '2')
            }
            done()
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    // 用户信息绑定
    async setOrderBindUserInfo(id) {
      this.isLoading = true
      const [err, res] = await this.$to(
        this.$apis.apiBackgroundOrderOrderOfflineSetOrderBindUserInfoPost({
          card_info_id: id,
          order_payment_id: this.dialogInfo.p_id
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        // 只有扣款 才调这个接口
        if (this.type === 'deduction') {
          this.repayOrder(this.dialogInfo.p_id, this.paymentMethod === '2')
        } else {
          this.showDrawer = false
          this.$emit('confirm', 'drawer')
        }
        // this.verificationResults = res.data
      } else {
        this.$message.error(res.msg)
      }
    },

    // 重新发起订单支付, isOrigin表示是否原价扣款
    async repayOrder(id, isOrigin) {
      if (this.isLoading) {
        return this.$message.error('请勿重复提交！')
      }
      this.isLoading = true
      let params = {
        order_payment_id: id
      }
      if (isOrigin) params.is_original_price = isOrigin
      const [err, res] = await this.$to(this.$apis.apiBackgroundOrderOrderOfflineOrderPayPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.showDrawer = false
        this.$emit('confirm', 'drawer')
      } else {
        this.$message.error(res.msg)
      }
    },

    // 拍摄人脸
    captureFace() {
      this.getOrderFace()
    },
    // 选择用户
    selectFaceUser(user) {
      this.selectedFaceUser = user
      this.$message.success(`已选择用户：${user.name}`)
    },
    // 搜索用户
    searchUsers() {
      // 先检查至少有一个字段填写了值
      // eslint-disable-next-line camelcase
      const { person_name, phone, person_no } = this.searchForm
      // eslint-disable-next-line camelcase
      if (!person_name && !phone && !person_no) {
        this.$message.error('请至少填写用户姓名、手机号或人员编号中的一项')
        return
      }

      this.$refs.searchForm.validate(valid => {
        if (valid) {
          this.getOperationData()
        }
      })
    },
    // 选择搜索结果中的用户
    selectSearchUser(user) {
      this.selectedSearchUser = user
      this.$message.success(`已选择用户：${user.name}`)
    }
  }
}
</script>

<style scoped lang="scss">
.abnormal-deduction-drawer {
  .abnormal-deduction-drawer-wrapp {
    padding: 0 20px;

    .drawer-header {
      margin-bottom: 15px;

      .header-title {
        font-size: 16px;
        font-weight: 500;
        color: #333;
      }
    }

    .tab-section {
      margin-bottom: 20px;

      .tab-buttons {
        display: flex;
        gap: 0;

        .tab-btn {
          border-radius: 0;
          border: 1px solid #ff9b45;
          background: #fff;
          color: #ff9b45;
          padding: 8px 20px;
          font-size: 14px;
          margin-right: -1px;
          border-radius: 4px;

          &:first-child {
            border-top-left-radius: 4px;
            border-bottom-left-radius: 4px;
          }

          &:last-child {
            border-top-right-radius: 4px;
            border-bottom-right-radius: 4px;
          }

          &.active {
            background: #ff9b45;
            color: #fff;
            border-color: #ff9b45;
            z-index: 1;
          }
        }
      }
    }

    .payment-method-section {
      margin-bottom: 20px;

      .section-title {
        font-size: 14px;
        color: #333;
        margin-bottom: 15px;
        font-weight: 500;
      }
    }

    // 查找用户相关样式
    .search-conditions-section {
      margin-bottom: 20px;

      .section-title {
        font-size: 14px;
        color: #333;
        margin-bottom: 15px;
        font-weight: 500;
      }
    }

    .search-results-section {
      margin-bottom: 20px;

      .section-title {
        font-size: 14px;
        color: #333;
        margin-bottom: 15px;
        font-weight: 500;
      }

      .user-item {
        display: flex;
        align-items: flex-start;
        gap: 15px;
        padding: 15px;
        border: 1px solid #ddd;
        border-radius: 4px;
        margin-bottom: 10px;
        cursor: pointer;
        transition: all 0.3s;
        background: #fff;

        &:hover {
          border-color: #ff9b45;
          background-color: #fff9f5;
        }

        &.selected {
          border-color: #ff9b45;
          background-color: #fff9f5;
        }

        .user-avatar {
          flex-shrink: 0;

          img {
            width: 60px;
            height: 60px;
            border-radius: 4px;
            object-fit: cover;
          }
        }

        .user-info {
          flex: 1;

          .user-name {
            font-size: 14px;
            font-weight: 500;
            color: #333;
            margin-bottom: 5px;
          }

          .user-phone {
            font-size: 13px;
            color: #666;
            margin-bottom: 5px;
          }

          .user-group {
            font-size: 13px;
            color: #666;
          }
        }

        .user-balance {
          display: flex;
          align-items: center;
          justify-content: center;

          .balance-row {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-bottom: 5px;
            min-width: 120px;

            .balance-label {
              padding-bottom: 20px;
              font-size: 13px;
              color: #666;
            }

            .balance-value {
              font-size: 13px;
              color: #333;
              font-weight: 500;
            }
          }
        }
      }

      .user-list-container {
        max-height: 290px;
        overflow-y: auto;
        border: 1px solid #e4e7ed;
        border-radius: 4px;
        padding: 10px;

        &::-webkit-scrollbar {
          width: 6px;
        }

        &::-webkit-scrollbar-track {
          background: #f1f1f1;
          border-radius: 3px;
        }

        &::-webkit-scrollbar-thumb {
          background: #c1c1c1;
          border-radius: 3px;

          &:hover {
            background: #a8a8a8;
          }
        }
      }

      .no-search-results {
        padding: 40px 20px;
        text-align: center;
        color: #909399;
        background-color: #f5f7fa;
        border-radius: 4px;
        font-size: 14px;
      }
      // }
    }
    // 人脸校验相关样式
    .face-capture-section {
      margin-bottom: 20px;

      .capture-title {
        font-size: 14px;
        color: #333;
        margin-bottom: 15px;
        font-weight: 500;
      }

      .face-capture-area {
        display: flex;
        align-items: flex-end;
        gap: 15px;

        .face-image-container {
          width: 150px;
          height: 150px;
          border-radius: 4px;
          overflow: hidden;
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: #f5f7fa;

          .captured-face-img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }

        .capture-btn {
          color: #fff;
          border: none;
          padding: 8px 15px;
          border-radius: 4px;
          width: 100px;
        }
      }
    }
    .no-face-capture {
      height: 100px;
      text-align: center;
      background: #e7e9ee;
      line-height: 100px;
      margin-bottom: 20px;
    }
    .current-selection-section {
      margin-bottom: 20px;

      .selection-title {
        font-size: 14px;
        color: #333;
        margin-bottom: 15px;
        font-weight: 500;
      }

      .selection-table {
        border: 1px solid #ddd;
        border-radius: 4px;
        overflow: hidden;

        .selection-table-content {
          width: 100%;
          border-collapse: collapse;

          tr {
            td {
              padding: 12px;
              border-right: 1px solid #ddd;
              text-align: center;
              font-size: 14px;
              color: #333;

              &:last-child {
                border-right: none;
              }

              &.selection-label {
                background-color: #f5f7fa;
                font-weight: 500;
                width: 100px;
              }

              &.selection-avatar-cell {
                width: 100px;
                padding: 8px;

                .selection-avatar {
                  width: 60px;
                  height: 60px;
                  border-radius: 4px;
                  object-fit: cover;
                }
              }

              &.selection-name {
                font-weight: 500;
                width: 25%;
              }

              &.selection-id {
                color: #666;
                width: 25%;
              }

              &.selection-phone {
                color: #666;
                width: 30%;
              }

              &.selection-score {
                color: #666;
                width: 20%;
              }
            }
          }
        }
      }

      .no-selection-placeholder {
        padding: 20px;
        text-align: center;
        color: #909399;
        background-color: #f5f7fa;
        border-radius: 4px;
      }
    }

    .verification-results-section {
      margin-bottom: 20px;

      .results-title {
        font-size: 14px;
        color: #333;
        margin-bottom: 15px;
        font-weight: 500;
      }

      .table-face-img {
        width: 60px;
        height: 60px;
        border-radius: 4px;
        object-fit: cover;
      }
    }
  }
  .selection-avatar-placeholder {
    width: 60px;
    height: 60px;
    border-radius: 4px;
    background-color: #f5f7fa;
    border: 1px solid #e4e7ed;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: #909399;
    text-align: center;
  }
}
</style>
