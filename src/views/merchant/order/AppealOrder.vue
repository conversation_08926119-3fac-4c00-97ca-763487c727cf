<template>
<!-- eslint-disable vue/no-unused-vars -->
  <div class="appeal-order container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form ref="searchRef" label-width="105px" :loading="isLoading" :form-setting="searchSetting" @search="searchHandle" :autoSearch="false">
      <template slot="perv">
        <div style="margin-bottom: 20px;">
          <el-radio-group v-model="tabType" :disabled="isLoading" @change="changeTabHandle" class="ps-radio-btn">
            <el-radio-button :label="1" v-permission="['background_order.order_appeal.pend_list']">待处理</el-radio-button>
            <el-radio-button :label="2" v-permission="['background_order.order_appeal.deal_list']">已处理</el-radio-button>
          </el-radio-group>
        </div>
      </template>
    </search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="plain" @click="openPrintSetting">报表设置</button-icon>
          <button-icon v-if="tabType === 1" color="plain" type="export" @click="handleExport" v-permission="['background_order.order_appeal.pend_list_export']">导出报表</button-icon>
          <button-icon v-if="tabType === 2" color="plain" type="export" @click="handleExport" v-permission="['background_order.order_appeal.deal_list_export']">导出报表</button-icon>
        </div>
      </div>
      <!-- table start -->
      <div class="table-content">
        <el-table :data="tableData" v-loading="isLoading" stripe header-row-class-name="ps-table-header-row" :empty-text="isFirstSearch ? '暂无数据，请查询' : ''">
          <table-column :index="indexMethod" v-for="item in currentTableSetting" :key="item.key" :col="item">
            <template #residual="{ row }">
              <count-down v-if="row.deal_status !=='HANG_OUT'" :time="row.residual_time" />
            </template>
            <template #operation="{ row }">
              <el-button v-if="tabType === 1" type="text" size="small" class="ps-text" @click="gotoDetail('deal', row)" v-permission="['background_order.order_appeal.detail_list']">处理</el-button>
              <el-button v-else type="text" size="small" class="ps-text" @click="gotoDetail('detail', row)">详情</el-button>
            </template>
          </table-column>
        </el-table>
      </div>
      <div style="margin-left: 20px; font-size: 14px;">
        <span><span>{{ tabType === 1 ? '待处理合计：' : '已处理合计：' }}</span>{{ totalCount }}</span>
        <span class="m-l-20" v-if="tabType === 2">退款金额合计：{{ total_refund_fee | formatMoney }}</span>
        <span class="m-l-20" v-if="tabType === 2">手续费合计：{{ total_rate_fee | formatMoney }}</span>
      </div>
      <!-- 分页 start -->
      <pagination
        :onPaginationChange="onPaginationChange"
        :current-page.sync="currentPage"
        :page-size.sync="pageSize"
        :layout="'total, prev, pager, next, jumper'"
        :total="totalCount"
      ></pagination>
      <!-- 分页 end -->
    </div>
    <print-setting
      v-if="dialogPrintVisible"
      :extraParams="{ printType: printType }"
      :tableSetting="tableSetting"
      :defaultCheckedSetting="currentTableSetting"
      :show.sync="dialogPrintVisible"
      @confirm="confirmPrintDialog"
    ></print-setting>
  </div>
</template>

<script>
import { debounce, to, divide, deepClone } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import {
  APPEAL_ORDER_SEARCH_PENDING,
  APPEAL_ORDER_SEARCH_PROCESSED,
  APPEAL_PENDING_TABLE,
  APPEAL_PROCESSED_TABLE
} from './constants'
import CountDown from '@/components/CountDown'
import report from '@/mixins/report' // 混入

export default {
  name: 'AppealOrder',
  // mixins: [activatedLoadData],
  mixins: [exportExcel, report],
  components: {
    CountDown
  },
  data() {
    return {
      isLoading: false,
      tabType: 1,
      searchSetting: deepClone(APPEAL_ORDER_SEARCH_PENDING),
      // 数据列表
      tableData: [],

      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      walletOrgsList: [], // 动账钱包
      dealStatusAlias: { // 处理状态
        PENDING: '待处理',
        HANG_OUT: '挂起中',
        TIMEOUT: '已逾期',
        CHANGE_FEE: '修改金额',
        ORDER_FREE: '整单不扣费',
        ORDER_REJECT: '整单驳回',
        FOOD_REFUND: '退款'
      },
      levelList: [],

      // 报表设置相关
      tableSetting: [],
      pendingTableSetting: [],
      processedTableSetting: [],
      currentTableSetting: [],
      dialogPrintVisible: false,
      printType: 'AppealPendingOrder',
      total_refund_fee: 0,
      total_rate_fee: 0,
      isFirstSearch: true
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {
  },
  computed: {
  },
  methods: {
    initLoad() {
      this.getpayList()
      // this.getWalletList()
      // this.getOrgWallet()
      this.getLevelName()
      if (this.tabType === 1) {
        // this.getAppealPendList()
      } else {
        // this.getAppealEealList()
      }
    },
    // 节下流咯
    searchHandle: debounce(function(e) {
      if (e && e === 'search') {
        this.currentPage = 1
        if (this.tabType === 1) {
          this.getAppealPendList()
        } else {
          this.getAppealEealList()
        }
        this.isFirstSearch = false
      }
    }, 300),
    // 刷新页面
    refreshHandle() {
      this.$refs.searchRef.resetForm()
      this.tableData = []
      this.currentPage = 1
      this.initLoad()
      this.isFirstSearch = true
    },
    indexMethod(index) {
      return (this.currentPage - 1) * this.pageSize + (index + 1)
    },
    changeTabHandle(e) {
      if (e === 1) {
        this.searchSetting = deepClone(APPEAL_ORDER_SEARCH_PENDING)
        this.printType = 'AppealPendingOrder'
        this.tableSetting = this.pendingTableSetting
      } else {
        this.searchSetting = deepClone(APPEAL_ORDER_SEARCH_PROCESSED)
        this.printType = 'AppealProcessedOrder'
        this.tableSetting = this.processedTableSetting
      }
      this.initPrintSetting()
      this.searchHandle('search')
      this.isFirstSearch = true
    },
    // 获取动账钱包
    async getWalletList() {
      const res = await this.$apis.apiBackgroundReportCenterDataReportWalletListPost()
      if (res.code === 0) {
        const result = []
        res.data.result.forEach(d => {
          Object.keys(d).forEach(key => result.push({ label: d[key], value: key }))
        })
        APPEAL_ORDER_SEARCH_PENDING.wallet_org.dataList = result
        APPEAL_ORDER_SEARCH_PROCESSED.wallet_org.dataList = result
      } else {
        this.$message.error(res.msg)
      }
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '') {
          if (key !== 'select_time') {
            if (data[key].value instanceof Array) {
              if (data[key].value.length > 0) {
                params[key] = data[key].value
              }
            } else {
              params[key] = data[key].value
            }
          } else if (data[key].value && data[key].value.length > 0) {
            params.start_create_time = data[key].value[0]
            params.end_create_time = data[key].value[1]
          }
        }
      }
      return params
    },
    // 获取待处理订单列表
    async getAppealPendList() {
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundOrderOrderAppealPendListPost({
        page: this.currentPage,
        page_size: this.pageSize,
        ...this.formatQueryParams(this.searchSetting)
      }))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      console.log('getAppealPendList', res)
      if (res.code === 0) {
        this.totalCount = res.data.count
        this.tableData = res.data.results.map(v => {
          v.origin_fee = '￥' + divide(v.origin_fee)
          v.pay_fee = '￥' + divide(v.pay_fee)
          v.watting_rate_fee = '￥' + divide(v.watting_rate_fee)
          v.subsidy_fee = '￥' + divide(v.subsidy_fee)
          v.wallet_fee = '￥' + divide(v.wallet_fee)
          v.complimentary_fee = '￥' + divide(v.complimentary_fee)
          v.subsidy_balance = '￥' + divide(v.subsidy_balance)
          v.wallet_balance = '￥' + divide(v.wallet_balance)
          v.complimentary_balance = '￥' + divide(v.complimentary_balance)
          v.refund_fee = '￥' + divide(v.refund_fee)
          return v
        })
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取已处理订单列表
    async getAppealEealList() {
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundOrderOrderAppealDealListPost({
        page: this.currentPage,
        page_size: this.pageSize,
        ...this.formatQueryParams(this.searchSetting)
      }))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      console.log('getAppealPendList', res)
      if (res.code === 0) {
        this.totalCount = res.data.count
        this.total_rate_fee = res.data.collect.total_rate_fee
        this.total_refund_fee = res.data.collect.total_refund_fee
        this.tableData = res.data.results.map(v => {
          v.origin_fee = '￥' + divide(v.origin_fee)
          v.pay_fee = '￥' + divide(v.pay_fee)
          v.deal_rate_fee = '￥' + divide(v.deal_rate_fee)
          v.subsidy_fee = '￥' + divide(v.subsidy_fee)
          v.wallet_fee = '￥' + divide(v.wallet_fee)
          v.complimentary_fee = '￥' + divide(v.complimentary_fee)
          v.subsidy_balance = '￥' + divide(v.subsidy_balance)
          v.wallet_balance = '￥' + divide(v.wallet_balance)
          v.complimentary_balance = '￥' + divide(v.complimentary_balance)
          v.refund_fee = '￥' + divide(v.refund_fee)
          return v
        })
      } else {
        this.$message.error(res.msg)
      }
    },
    // level name
    async getLevelName() {
      const res = await this.$apis.apiBackgroundReportCenterDataReportGetLevelNamePost()
      if (res.code === 0) {
        if (res.data) {
          this.levelList = res.data.map(v => {
            return {
              key: v.level,
              label: v.name
            }
          })
          // 初始化每个tableSetting
          this.pendingTableSetting = deepClone(APPEAL_PENDING_TABLE)
          this.processedTableSetting = deepClone(APPEAL_PROCESSED_TABLE)
          this.pendingTableSetting.splice(22, 0, ...this.levelList)
          this.processedTableSetting.splice(25, 0, ...this.levelList)
          if (this.tabType === 1) {
            this.tableSetting = this.pendingTableSetting
          } else if (this.tabType === 2) {
            this.tableSetting = this.processedTableSetting
          }
          this.initPrintSetting()
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取组织下的钱包
    async getOrgWallet() {
      const res = await this.$apis.apiBackgroundReportCenterDataReportGetOrgWalletPost()
      if (res.code === 0) {
        // res.data.wallet.map(v => {
        //   this.columns.forEach(item => {
        //     if (this.showWalletList[v].includes(item.column)) {
        //       item.show = true
        //     }
        //   })
        // })
        console.log(this.columns)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      if (this.tabType === 1) {
        this.getAppealPendList()
      } else {
        this.getAppealEealList()
      }
    },
    gotoDetail(type, row) {
      this.$router.push({
        name: 'MerchantAppealOrderDetail',
        query: {
          type: type,
          id: row.id
        }
      })
    },
    // 导出报表
    handleExport() {
      let type
      if (this.tabType === 1) {
        type = 'AppealOrderPending'
      } else {
        type = 'AppealOrderProcessed'
      }
      const option = {
        type,
        params: {
          page: this.currentPage,
          page_size: 999999,
          ...this.formatQueryParams(this.searchSetting)
        }
      }
      this.exportHandle(option)
    },
    // 获取支付方式 / 支付类型
    async getpayList() {
      const res = await this.$apis.apiBackgroundReportCenterDataReportGetPayInfoPost()
      if (res.code === 0) {
        const result = []
        const result2 = []
        res.data.result.payways.forEach(d => {
          Object.keys(d).forEach(key => result.push({ label: d[key], value: key }))
        })
        res.data.result.sub_payways.forEach(d => {
          Object.keys(d).forEach(key => result2.push({ label: d[key], value: key }))
        })
        this.searchSetting.payway.dataList = result
        this.searchSetting.sub_payway.dataList = result2
      } else {
        this.$message.error(res.msg)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.appeal-order {
  .sumWrapper{
    padding-left: 20px;
    padding-bottom: 20px;
    ul, li { list-style: none; }
    li{
      display: inline-block;
      margin-right: 20px;
      font-size: 13px;
    }
  }
}
</style>
