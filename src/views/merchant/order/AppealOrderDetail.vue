<template>
  <!-- eslint-disable vue/no-unused-vars -->
  <div class="appeal-order-detail container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <!-- 基本信息 -->
    <div class="box-wrapper">
      <div class="box-header">
        <div class="box-title">基本信息</div>
      </div>
      <!-- table start -->
      <div class="box-content">
        <div class="user-info-wrapper">
        <div class="user-img">
          <!-- <el-image :src="appealOrderDetail.img_url" lazy></el-image> -->
          <el-avatar v-if="appealOrderDetail.img_url" :size="60" fit="cover" shape="square" :src="appealOrderDetail.img_url"></el-avatar>
          <el-avatar v-else :size="60" fit="cover" shape="square" :src="require('@/assets/img/account-img.png')"></el-avatar>
        </div>
        <div class="user-info-r clearfix" style="min-width: 700px;">
          <!-- 左右布局吧，上下宽度没保障 -->
          <div class="float-l clearfix">
            <div class="info-item">
              <div class="label">姓名：</div>
              <div class="value">{{ appealOrderDetail.name ? appealOrderDetail.name : '--' }}</div>
            </div>
            <div class="info-item">
              <div class="label">卡号：</div>
              <div class="value">{{ appealOrderDetail.card_no ? appealOrderDetail.card_no : '--' }}</div>
            </div>
          </div>
          <div class="float-l">
            <div class="info-item">
              <div class="label">手机号：</div>
              <div class="value">{{ appealOrderDetail.phone ? appealOrderDetail.phone : '--' }}</div>
            </div>
            <div class="info-item">
              <div class="label">部门：</div>
              <div class="value">{{ appealOrderDetail.payer_department_group_name ? appealOrderDetail.payer_department_group_name : '--' }}</div>
            </div>
          </div>
          <div class="float-l">
            <div class="info-item">
              <div class="label">有关分组：</div>
              <div class="value">{{ appealOrderDetail.payer_group_name ? appealOrderDetail.payer_group_name : '--' }}</div>
            </div>
          </div>
          <div class="float-l">
            <div class="info-item">
              <div class="label">人员编号：</div>
              <div class="value">{{ appealOrderDetail.person_no ? appealOrderDetail.person_no : '--' }}</div>
            </div>
          </div>
        </div>
      </div>
      </div>
    </div>
    <!-- 订单信息 -->
    <div class="box-wrapper margin-t-20">
      <div class="box-header">
        <div class="box-title">订单信息</div>
      </div>
      <!-- table start -->
      <div class="box-content">
        <div class="user-info-wrapper">
          <div class="user-info-r clearfix">
            <!-- 左右布局吧，上下宽度没保障 -->
            <div class="float-l clearfix">
              <div class="info-item">
                <div class="label">总单号：</div>
                <div class="value">{{ appealOrderDetail.order_trade_no ? appealOrderDetail.order_trade_no : '--' }}</div>
              </div>
              <div class="info-item">
                <div class="label">支付时间：</div>
                <div class="value">{{ appealOrderDetail.pay_time ? appealOrderDetail.pay_time : '--' }}</div>
              </div>
              <!-- <div class="info-item">
                <div class="label">优惠金额：</div>
                <div class="value">{{ appealOrderDetail.payer_department_group_name }}</div>
              </div> -->
              <div class="info-item">
                <div class="label">储值动账：</div>
                <div class="value">￥{{ appealOrderDetail.wallet_fee }}</div>
              </div>
              <div class="info-item">
                <div class="label">赠送钱包余额：</div>
                <div class="value">￥{{ appealOrderDetail.complimentary_balance }}</div>
              </div>
              <div class="info-item">
                <div class="label">支付类型：</div>
                <div class="value">{{ appealOrderDetail.sub_payway_alias }}</div>
              </div>
            </div>
            <div class="float-l">
              <div class="info-item">
                <div class="label">订单号：</div>
                <div class="value">{{ appealOrderDetail.trade_no ? appealOrderDetail.trade_no : '--' }}</div>
              </div>
              <div class="info-item">
                <div class="label">消费点：</div>
                <div class="value">{{ appealOrderDetail.stall ? appealOrderDetail.stall : '--' }}</div>
              </div>
              <!-- <div class="info-item">
                <div class="label">服务费：</div>
                <div class="value">{{ appealOrderDetail.fuwu_fee }}</div>
              </div> -->
              <div class="info-item">
                <div class="label">赠送动账：</div>
                <div class="value">￥{{ appealOrderDetail.complimentary_fee }}</div>
              </div>
              <div class="info-item">
                <div class="label">动账组织：</div>
                <div class="value">{{ appealOrderDetail.wallet_org ? appealOrderDetail.wallet_org : '--' }}</div>
              </div>
              <div class="info-item">
                <div class="label">支付状态：</div>
                <div class="value">{{ appealOrderDetail.order_status_alias ? appealOrderDetail.order_status_alias : '--' }}</div>
              </div>
            </div>
            <div class="float-l">
              <div class="info-item">
                <div class="label">第三方订单号：</div>
                <div class="value">{{ appealOrderDetail.out_trade_no ? appealOrderDetail.out_trade_no : '--' }}</div>
              </div>
              <div class="info-item">
                <div class="label">餐段:</div>
                <div class="value">{{ appealOrderDetail.meal_type_alias ? appealOrderDetail.meal_type_alias : '--' }}</div>
              </div>
              <!-- <div class="info-item">
                <div class="label">实收金额:</div>
                <div class="value">{{ appealOrderDetail.pay_fee }}</div>
              </div> -->
              <div class="info-item">
                <div class="label">补贴钱包余额:</div>
                <div class="value">￥{{ appealOrderDetail.subsidy_balance }}</div>
              </div>
              <div class="info-item">
                <div class="label">优惠类型:</div>
                <div class="value">{{ appealOrderDetail.discount_type_alias ? appealOrderDetail.discount_type_alias : '--' }}</div>
              </div>
              <div class="info-item">
                <div class="label">对账状态:</div>
                <div class="value">{{ appealOrderDetail.settle_status_alias ? appealOrderDetail.settle_status_alias : '--' }}</div>
              </div>
            </div>
            <div class="float-l">
              <div class="info-item">
                <div class="label">创建时间：</div>
                <div class="value">{{ appealOrderDetail.order_create_time ? appealOrderDetail.order_create_time : '--' }}</div>
              </div>
              <!-- <div class="info-item">
                <div class="label">订单金额：</div>
                <div class="value">{{ appealOrderDetail.origin_fee }}</div>
              </div> -->
              <div class="info-item">
                <div class="label">补贴动账：</div>
                <div class="value">￥{{ appealOrderDetail.subsidy_fee }}</div>
              </div>
              <!-- <div class="info-item">
                <div class="label">动账钱包余额：</div>
                <div class="value">{{ appealOrderDetail.subsidy_fee }}</div>
              </div> -->
              <div class="info-item">
                <div class="label">支付方式：</div>
                <div class="value">{{ appealOrderDetail.payway_alias ? appealOrderDetail.payway_alias : '--' }}</div>
              </div>
            </div>
          </div>
        </div>
        <div class="order-price-box clearfix">
          <div class="order-p-item">
            <p class="order-label">订单金额：</p>
            <p class="order-price">￥{{ appealOrderDetail.origin_fee }}</p>
          </div>
          <div class="order-p-item">
            <p class="order-label">优惠金额：</p>
            <p class="order-price red">￥{{ appealOrderDetail.discount_fee }}</p>
          </div>
          <div class="order-p-item">
            <p class="order-label">补贴消费：</p>
            <p class="order-price">￥{{ appealOrderDetail.subsidy_fee }}</p>
          </div>
          <div class="order-p-item">
            <p class="order-label">服务费：</p>
            <p class="order-price">￥{{ appealOrderDetail.fuwu_fee }}</p>
          </div>
          <div class="order-p-item">
            <p class="order-label">实收金额：</p>
            <p class="order-price">￥{{ appealOrderDetail.pay_fee }}</p>
          </div>
        </div>
        <!-- 查看菜品信息-->
        <div class="btn-origin-detail">
          <el-button size="small" type="primary" @click="clickShowDishDialog" class="ps-origin-btn" > {{appealOrderDetail.payment_order_type !== 'goods_cashier'?'查看菜品信息':'查看商品信息'}} </el-button>
        </div>
      </div>
    </div>
    <!-- 设备信息 -->
    <div class="box-wrapper margin-t-20">
      <div class="box-header">
        <div class="box-title">设备信息</div>
      </div>
      <!-- table start -->
      <div class="box-content">
        <el-table
          :data="deviceList"
          stripe
          border
          header-row-class-name="ps-table-header-row"
          style="width: 100%"
        >
          <el-table-column label="设备类型" prop="device_type" align="center"></el-table-column>
          <el-table-column label="设备号" prop="device_number" align="center"></el-table-column>
          <el-table-column label="设备名" prop="device_name" align="center"></el-table-column>
          <el-table-column label="操作员" prop="controller" align="center"></el-table-column>
        </el-table>
      </div>
    </div>
    <!-- 申诉信息 -->
    <div class="box-wrapper margin-t-20">
      <div class="box-header">
        <div class="box-title">
          申诉信息
          <span v-if="timeDown && type === 'deal'" class="float-r" style="margin-right: 25px;color: #fd594e; font-size: 16px;">剩余处理时间：{{ timeDown }}</span>
        </div>
      </div>
      <!-- table start -->
      <div class="box-content">
        <div class="appeal-item">
          <span class="appeal-item-label">申诉时间：</span>
          <span>{{ appealOrderDetail.create_time }}</span>
        </div>
        <div class="appeal-item">
          <span class="appeal-item-label">申诉状态：</span>
          <span>{{ appealOrderDetail.appeal_status_alias }}</span>
          <span style="margin-left: 25px;">
            <span class="appeal-item-label">处理结果：</span>
            <span>{{ appealOrderDetail.act_deal_status_alias }}</span>
          </span>
          <span v-if="appealOrderDetail.act_deal_status === 'ORDER_REJECT'" style="margin-left: 25px;">
            <span class="appeal-item-label">驳回原因：</span>
            <span>{{ appealOrderDetail.act_reject_reason }}</span>
          </span>
        </div>
        <div class="appeal-item">
          <span class="appeal-item-label">申诉原因：</span>
          <span>{{ appealOrderDetail.appeal_reason }}</span>
        </div>
        <div class="appeal-item">
          <span class="appeal-item-label">补充说明：</span>
          <span style="margin-top: 10px;">
            {{ appealOrderDetail.extra_reason }}
          </span>
        </div>
        <div class="appeal-item">
          <div class="appeal-item-label">申诉图片：</div>
          <div style="margin-top: 10px;">
            <el-image
              v-for="(img, i) in appealOrderDetail.appeal_img_list"
              :key="img + i"
              style="width: 100px; margin-right: 10px;"
              :src="img"
              fit="contain"
              :preview-src-list="appealOrderDetail.appeal_img_list"
            ></el-image>
          </div>
        </div>
        <div style="color:#ff9b45;">如选择商品进行退款，库存会对应递增，若未实际入库，请及时至‘商品库存’中操作出库。</div>
      </div>
    </div>
    <!-- 菜品信息 -->
    <div class="box-wrapper margin-t-20">
      <div class="box-header">
        <div class="box-title">申述菜品信息</div>
        <div class="box-right">退款金额合计：￥{{appealOrderDetail.total_refund_fee}}</div>
      </div>
      <!-- table start -->
      <div class="table-content">
        <el-table
          v-loading="isLoading"
          :data="foodData"
          stripe
          border
          header-row-class-name="ps-table-header-row"
          style="width: 100%"
        >
          <el-table-column label="菜品名称" prop="name" align="center"></el-table-column>
          <el-table-column label="图片" prop="trade_no" width="110px" align="center">
            <template slot-scope="scope">
              <el-image
                style="width: 100px; height: 60px"
                :src="scope.row.image"
                fit="contain"
                v-if="scope.row.image"
                :preview-src-list="[scope.row.image]"
              ></el-image>
              <span v-else>--</span>
            </template>
          </el-table-column>
          <el-table-column label="识别图片" width="110px" prop="image" align="center">
            <template slot-scope="scope">
              <el-image
                style="width: 100px; height: 60px"
                :src="scope.row.image"
                fit="contain"
                v-if="scope.row.image"
                :preview-src-list="[scope.row.image]"
              ></el-image>
              <span v-else>--</span>
            </template>
          </el-table-column>
          <el-table-column label="菜品价格" prop="food_price" align="center">
            <template slot-scope="scope">
              ￥{{ scope.row.food_price }}
            </template>
          </el-table-column>
          <el-table-column label="销售价格" prop="food_price" align="center">
            <template slot-scope="scope">
              ￥{{ scope.row.food_price }}
            </template>
          </el-table-column>
          <el-table-column label="数量" prop="count" align="center"></el-table-column>
          <el-table-column label="重量" prop="weight" align="center"></el-table-column>
          <el-table-column label="消费金额" prop="real_fee" align="center">
            <template slot-scope="scope">
              ￥{{ scope.row.real_fee }}
            </template>
          </el-table-column>
          <!-- <el-table-column label="营养" prop="payer_group_name" align="center">
            <template slot-scope="scope">
              <el-button type="text" size="small" @click="openDialogHandle('nutrition', scope.row)">查看详情</el-button>
            </template>
          </el-table-column> -->
          <el-table-column label="处理结果" prop="payer_group_name" align="center">
            <template slot-scope="scope">
              {{ showDialogTableResult(scope.row) }}
            </template>
          </el-table-column>
          <el-table-column label="退款金额" prop="count" align="center">
            <template slot-scope="scope">
              ￥{{ scope.row.refund_fee }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="260px" align="center" v-if="type === 'deal' && appealOrderDetail.display_food_success_button">
            <template slot-scope="scope">
              <!-- 支付中 -->
              <template v-if="appealOrderDetail.order_status === 'ORDER_PAYING'">
                <el-button v-if="scope.row.deal_status === 'ORDER_REFUND'" type="text" size="small"  @click="openDialogHandle('REVOKE_ORDER_REFUND', scope.row)" :disabled='appealOrderDetail.appeal_status=="FINISH"'>取消退款<span style="margin:0 10px; color: #e2e8f0;">|</span></el-button>
                <el-button type="text" size="small"  @click="openDialogHandle('CHANGE_FEE', scope.row)" :disabled='appealOrderDetail.appeal_status=="FINISH"'>修改金额<span style="margin:0 10px; color: #e2e8f0;">|</span></el-button>
                <el-button type="text" size="small" class="ps-text" @click="openDialogHandle('ORDER_FREE', scope.row)" :disabled='appealOrderDetail.appeal_status=="FINISH"'>不扣费<span style="margin:0 10px; color: #e2e8f0;">|</span></el-button>
              </template>
              <!-- 支付完成 -->
              <template v-else>
                <!-- <el-button v-if="scope.row.deal_status === 'ORDER_REFUND'" type="text" size="small"  @click="openDialogHandle('REVOKE_ORDER_REFUND', scope.row)" :disabled='appealOrderDetail.appeal_status=="FINISH"'>取消退款<span style="margin:0 10px; color: #e2e8f0;">|</span></el-button> -->
                <!-- <el-button v-if="scope.row.deal_status !== 'ORDER_REFUND'" type="text" size="small"  @click="openDialogHandle('CHANGE_FEE_SOME', scope.row)" :disabled='appealOrderDetail.appeal_status=="FINISH"'>部分退款<span style="margin:0 10px; color: #e2e8f0;">|</span></el-button> -->
                <el-button v-if="scope.row.deal_status !== 'ORDER_REFUND' && !scope.row.is_consume_rule" type="text" size="small" class="ps-text" @click="openDialogHandle('ORDER_REFUND', scope.row)" :disabled='appealOrderDetail.appeal_status=="FINISH"'>退款<span style="margin:0 10px; color: #e2e8f0;">|</span></el-button>
              </template>
              <el-button type="text" size="small" class="ps-text" @click="openDialogHandle('ORDER_REJECT', scope.row)" :disabled='appealOrderDetail.appeal_status=="FINISH"'>驳回</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <div class="appeal-footer" v-if="type === 'deal'">
      <el-button size="small" v-if="appealOrderDetail.act_deal_status === 'HANG_OUT'" @click="openDialogHandle('RM_HANG_OUT')" :disabled='appealOrderDetail.appeal_status=="FINISH"'>解除挂起</el-button>
      <el-button size="small" v-else @click="openDialogHandle('HANG_OUT')" :disabled='appealOrderDetail.appeal_status=="FINISH"'>暂时挂起</el-button>
      <!-- <el-button size="small" @click="openDialogHandle('HANG_OUT')" :disabled='appealOrderDetail.appeal_status=="FINISH"'>暂时挂起</el-button> -->
      <el-button size="small" type="primary" class="ps-red-btn" @click="openDialogHandle('ORDER_REJECT')" :disabled='appealOrderDetail.appeal_status=="FINISH"'>整单驳回</el-button>
      <el-button size="small" v-if="appealOrderDetail.order_status === 'ORDER_PAYING'" @click="openDialogHandle('ORDER_FREE')" :disabled='appealOrderDetail.appeal_status=="FINISH"'>整单不扣费</el-button>
      <el-button size="small" v-else @click="openDialogHandle('ALL_ORDER_REFUND')" :disabled='appealOrderDetail.appeal_status=="FINISH"'>整单退款</el-button>
      <el-button size="small" type="primary" @click="openDialogHandle('FINISH')" class="ps-origin-btn" :disabled='appealOrderDetail.appeal_status=="FINISH"'>完成处理</el-button>
    </div>
    <!-- 弹窗 -->
    <el-dialog :visible.sync="dialogVisible" :width="dialogWidth" :top="dialogTop" :title="dialogTitle" :close-on-press-escape="false" :close-on-click-modal="false" @closed="closeDialogHandle" custom-class="appeal-dialog">
      <!-- text start -->
      <div class="dialog-content-text" v-if="dialogContentTextKey.includes(dialogType)">
        {{ dialogContentText }}
      </div>
      <!-- text end -->
      <div class="" v-if="dialogType === 'ORDER_REJECT'">
        <el-input
          type="textarea"
          :rows="4"
          :maxlength="100"
          show-word-limit
          placeholder="请输入内容"
          v-model="dialogForm.rejectReason">
        </el-input>
      </div>
      <div class="" v-if="dialogType === 'ORDER_REFUND' || dialogType === 'CHANGE_FEE' || dialogType === 'CHANGE_FEE_SOME'">
        <el-table
          v-loading="isLoading"
          :data="dialogFoodData"
          stripe
          border
          header-row-class-name="ps-table-header-row"
          style="width: 100%"
        >
          <!-- <el-table-column type="selection" width="55"></el-table-column> -->
          <el-table-column label="图片" prop="trade_no" width="110px" align="center">
            <template slot-scope="scope">
              <el-image
                style="width: 100px; height: 60px"
                :src="scope.row.image"
                fit="contain"
                v-if="scope.row.image"
                :preview-src-list="[scope.row.image]"
              ></el-image>
              <span v-else>--</span>
            </template>
          </el-table-column>
          <el-table-column label="菜品名称" prop="name" align="center"></el-table-column>
          <el-table-column label="菜品价格" prop="food_price" align="center">
            <template slot-scope="scope">
              ￥{{ scope.row.food_price }}
            </template>
          </el-table-column>
          <el-table-column label="销售价格" prop="food_price" align="center">
            <template slot-scope="scope">
              ￥{{ scope.row.food_price }}
            </template>
          </el-table-column>
          <el-table-column label="数量" prop="count" align="center"></el-table-column>
          <el-table-column label="重量" prop="weight" align="center"></el-table-column>
          <el-table-column label="消费金额" prop="real_fee" align="center">
            <template slot-scope="scope">
              ￥{{ scope.row.real_fee }}
            </template>
          </el-table-column>
          <!-- <el-table-column label="退款状态" prop="payer_group_name" align="center"></el-table-column> -->
        </el-table>
        <div v-if="dialogType === 'ORDER_REFUND'" class="price-box"  style="margin: 20px 0 30px;">
          <p class="price-p">
            <span class="price-label">可退余额：</span>
            <span class="price">{{ dialogFoodData[0].real_fee }}</span>
          </p>
          <p class="price-p">
            <span class="price-label">退款金额：</span>
            <!-- <span class="price">{{ dialogFoodData[0].real_fee }}</span> -->
            <el-input
              placeholder="请输入金额"
              class="ps-input"
              style="width:100px;"
              size="mini"
              v-model="dialogForm.refundPrice">
            </el-input>
          </p>
        </div>
        <div v-if="dialogType === 'CHANGE_FEE'" class="price-box" style="margin: 20px 0 30px;">
          <div class="clearfix">
            <p class="price-tip float-l">修改金额：</p>
            <p class="price-tip float-r">可修改金额：￥0.00 - {{ dialogFoodData[0].raw_fee }}</p>
          </div>
          <div>
            <el-input
              show-word-limit
              placeholder="请输入金额"
              class="ps-input"
              v-model="dialogForm.refundPrice">
            </el-input>
          </div>
        </div>
        <div v-if="dialogType === 'CHANGE_FEE_SOME'" class="price-box" style="margin: 20px 0 30px;">
          <div class="clearfix">
            <p class="price-tip float-l">部分退款金额：</p>
            <p class="price-tip float-r">可退款金额：￥0.00 - {{ dialogFoodData[0].raw_fee }}</p>
          </div>
          <div>
            <el-input
              show-word-limit
              placeholder="请输入金额"
              class="ps-input"
              v-model="dialogForm.refundPrice">
            </el-input>
          </div>
        </div>
      </div>
      <div class="" v-if="dialogType === 'ALL_ORDER_REFUND'">
        <div class="price-box"  style="margin: 20px 0 30px;">
          <p class="price-p">
            <span class="price-label">可退余额：</span>
            <span class="price">{{ appealOrderDetail.pay_fee }}</span>
          </p>
          <p class="price-p">
            <span class="price-label">退款金额：</span>
            <el-input
              placeholder="请输入金额"
              class="ps-input"
              style="width:100px;"
              size="mini"
              v-model="dialogForm.refundPrice">
            </el-input>
          </p>
        </div>
      </div>
      <!-- 营养 start -->
      <el-form
        :model="dialogForm"
        class=""
        size="small"
      >
        <div v-if="dialogType === 'nutrition'" >
          <template v-for="nutrition in nutritionList">
            <div class="nutrition-item" :key="nutrition.key">
              <div class="nutrition-label">{{nutrition.name+'：'}}</div>
              <el-form-item :prop="nutrition.key">
                <el-input style="width: 120px;" readonly v-model="dialogForm[nutrition.key]" class="ps-input"></el-input><span style="margin-left: 10px;">{{nutrition.unit}}</span>
              </el-form-item>
            </div>
          </template>
        </div>
      </el-form>
      <!-- 营养 end -->
      <div v-if="dialogType !== 'nutrition'" slot="footer" class="dialog-footer" style="text-align: right;">
        <el-button size="small" class="ps-cancel-btn" :disabled="isLoading" @click="cancelDialogHandle">取 消</el-button>
        <el-button size="small" type="primary" class="ps-origin-btn" :disabled="isLoading" @click="submitDialogHaldle">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 原有菜品信息弹窗-->
     <el-dialog :visible.sync="isShowOldDishs" :width="dialogWidthFood" :top="dialogTop" :title="appealOrderDetail.payment_order_type !== 'goods_cashier'?'原菜品信息':'原商品信息'" :close-on-press-escape="false" :close-on-click-modal="false" @closed="closeDialogHandle" custom-class="appeal-dialog">
      <div class="">
        <el-table
          v-loading="isLoading"
          :data="dialogOldFoodData"
          stripe
          border
          header-row-class-name="ps-table-header-row"
          style="width: 100%"
          max-height="500"
        >
        <el-table-column :label="appealOrderDetail.payment_order_type !== 'goods_cashier'?'菜品名称':'商品名称'" prop="name" align="center"></el-table-column>
          <el-table-column label="图片" prop="trade_no" width="110px" align="center">
            <template slot-scope="scope">
              <el-image
                style="width: 100px; height: 60px"
                :src="scope.row.image"
                fit="contain"
                v-if="scope.row.image"
                :preview-src-list="[scope.row.image]"
              ></el-image>
              <span v-else>--</span>
            </template>
          </el-table-column>
          <el-table-column label="识别图片" width="110px" prop="image" align="center" v-if="appealOrderDetail.payment_order_type !== 'goods_cashier'" >
            <template slot-scope="scope">
              <el-image
                style="width: 100px; height: 60px"
                :src="scope.row.sceneImage"
                fit="contain"
                v-if="scope.row.sceneImage"
                :preview-src-list="[scope.row.sceneImage]"
              ></el-image>
              <span v-else>--</span>
            </template>
          </el-table-column>
          <el-table-column :label="appealOrderDetail.payment_order_type !== 'goods_cashier'?'菜品价格':'商品价格'" prop="price" align="center">
            <template slot-scope="scope">
              ￥{{ scope.row.price }}
            </template>
          </el-table-column>
          <el-table-column label="销售价格" prop="price" align="center">
            <template slot-scope="scope">
              ￥{{ scope.row.price }}
            </template>
          </el-table-column>
          <el-table-column label="数量" prop="count" align="center"></el-table-column>
          <el-table-column label="重量" prop="weight" align="center" v-if="appealOrderDetail.payment_order_type !== 'goods_cashier'" ></el-table-column>
          <el-table-column label="消费金额" prop="real_fee" align="center">
            <template slot-scope="scope">
              ￥{{ scope.row.real_fee }}
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div  slot="footer" class="dialog-footer" style="text-align: right;">
        <el-button size="small" type="primary" class="ps-origin-btn" :disabled="isLoading" @click="isShowOldDishs =false">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { debounce, to, times, divide, replaceDate, deepClone } from '@/utils'
import { NUTRITION_LIST } from './constants'
export default {
  name: 'AppealOrder',
  // mixins: [activatedLoadData],
  data() {
    return {
      isLoading: false,
      type: '',
      tabType: 1,
      // 数据列表
      foodData: [],
      dialogFoodData: [],
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      walletOrgsList: [], // 动账钱包
      dealStatusAlias: { // 处理状态
        PENDING: '待处理',
        HANG_OUT: '挂起中',
        TIMEOUT: '已逾期',
        CHANGE_FEE: '修改金额',
        ORDER_FREE: '整单不扣费',
        ORDER_REJECT: '整单驳回',
        ORDER_REFUND: '退款'
      },
      appealOrderDetail: {},
      deviceList: [], // 设备信息
      dialogVisible: false,
      dialogTitle: '提示',
      dialogTop: '30vh',
      dialogWidth: '400px',
      dialogWidthFood: "800px",
      dialogType: '',
      dialogContentTextKey: ['ORDER_FREE', 'FINISH', 'HANG_OUT', 'RM_HANG_OUT', 'ALL_ORDER_REFUND'],
      dialogContentText: '',
      dialogForm: {
        refundPrice: '',
        rejectReason: '',
        dealAction: ''
      },
      dialogTableData: {},
      timeHandle: null,
      timeDown: 0,
      nutritionList: NUTRITION_LIST,
      isShowOldDishs: false, // 是否显示原有菜品信息
      dialogOldFoodData: []
    }
  },
  created() {
    this.type = this.$route.query.type
    this.initLoad()
  },
  mounted() {
  },
  computed: {
  },
  methods: {
    initLoad() {
      this.getAppealDetail()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.initLoad()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '') {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value && data[key].value.length > 0) {
            params.start_create_time = data[key].value[0]
            params.end_create_time = data[key].value[1]
          }
        }
      }
      return params
    },
    // 获取详情
    async getAppealDetail() {
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundOrderOrderAppealDetailListPost({
        order_appeal_id: this.$route.query.id
      }))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      console.log('getAppealDetail', res)
      if (res.code === 0) {
        res.data.wallet_fee = divide(res.data.wallet_fee)
        res.data.complimentary_balance = divide(res.data.complimentary_balance)
        res.data.fuwu_fee = divide(res.data.fuwu_fee)
        res.data.complimentary_fee = divide(res.data.complimentary_fee)
        res.data.pay_fee = divide(res.data.pay_fee)
        res.data.subsidy_balance = divide(res.data.subsidy_balance)
        res.data.origin_fee = divide(res.data.origin_fee)
        res.data.subsidy_fee = divide(res.data.subsidy_fee)
        res.data.discount_fee = divide(res.data.discount_fee)
        res.data.total_refund_fee = res.data.total_refund_fee ? divide(res.data.total_refund_fee) : "0.00"
        this.appealOrderDetail = res.data
        // let actFoodId = res.data.act_food_list.map(v => {

        // })
        if (res.data.payment_order_type === 'goods_cashier') {
          this.foodData = res.data.goods_info.map(v => {
            v.raw_fee = divide(v.raw_fee)
            v.net_fee = divide(v.net_fee)
            v.real_fee = divide(v.real_fee)
            v.food_price = divide(v.food_price)
            v.refund_fee = divide(v.refund_fee)
            v.image = v.goods_img
            v.name = v.goods_name
            v.payment_data_id = v.payment_goods_id
            v.orderType = 'goods_cashier'
            let isDeal = false
            res.data.act_goods_list.forEach(item => {
              if (item.payment_goods_id === v.payment_goods_id) {
                isDeal = true
                v.appeal_results = item
              }
            })
            if (!isDeal) {
              v.appeal_results = {}
            }
            return v
          })
          let originFoodList = Reflect.has(res.data, 'origin_goods_list') ? res.data.origin_goods_list : []
          if (originFoodList && Array.isArray(originFoodList)) {
            originFoodList = originFoodList.map(item => {
              let obj = {
                name: item.name,
                image: item.goods_extra.goods_img,
                sceneImage: '',
                count: item.count,
                weight: item.weight,
                raw_fee: divide(item.raw_fee),
                net_fee: divide(item.net_fee),
                real_fee: divide(item.real_fee),
                price: divide(item.goods_price)
              }
              return obj
            })
            console.log(" this.dialogOldFoodData", originFoodList);
            this.dialogOldFoodData = deepClone(originFoodList)
          }
        } else {
          this.foodData = res.data.food_info.map(v => {
            v.raw_fee = divide(v.raw_fee)
            v.net_fee = divide(v.net_fee)
            v.real_fee = divide(v.real_fee)
            v.food_price = divide(v.food_price)
            v.refund_fee = divide(v.refund_fee)
            v.image = v.food_img
            v.name = v.food_name
            v.payment_data_id = v.payment_food_id
            let isDeal = false
            res.data.act_food_list.forEach(item => {
              if (item.payment_food_id === v.payment_food_id) {
                isDeal = true
                v.appeal_results = item
              }
            })
            if (!isDeal) {
              v.appeal_results = {}
            }
            return v
          })
          let originFoodList = Reflect.has(res.data, 'origin_food_list') ? res.data.origin_food_list : []
          console.log("originFoodList", originFoodList);
          if (originFoodList && Array.isArray(originFoodList)) {
            originFoodList = originFoodList.map(item => {
              let obj = {
                name: item.name,
                image: item.food_extra.food_img,
                sceneImage: item.food_scene_image,
                count: item.count,
                weight: item.weight,
                raw_fee: divide(item.raw_fee),
                net_fee: divide(item.net_fee),
                real_fee: divide(item.real_fee),
                price: divide(item.food_price)
              }
              return obj
            })
            console.log(" this.dialogOldFoodData", originFoodList);
            this.dialogOldFoodData = deepClone(originFoodList)
          }
        }
        this.deviceList = [{
          device_type: res.data.device_type,
          device_number: res.data.device_number,
          device_name: res.data.device_name,
          controller: res.data.controller
        }]

        this.timeout(res.data.residual_time, new Date())
      } else {
        this.$message.error(res.msg)
      }
    },
    timeout(end, date) {
      if (!end) {
        this.timeDown = 0
        return
      }
      if (typeof end === 'string') {
        end = replaceDate(end)
      }
      if (this.appealOrderDetail.act_deal_status === 'HANG_OUT') {
        return
      }
      let setTime = new Date(end).getTime() // 结束时间
      this.timeHandle = setInterval(() => {
        // let setTime = new Date().getTime() + 15 * 60 * 1000; //结束时间
        let nowTime = new Date();
        let timeDiff = setTime - nowTime.getTime();
        if (timeDiff <= 0) {
          this.timeDown = ''
          return clearInterval(this.timeHandle);
        }
        // let day = parseInt(timeDiff / (60 * 60 * 24 * 1000)); //  天数
        let hour = parseInt((timeDiff / (60 * 60 * 1000)) % 24); //  小时
        let minute = parseInt((timeDiff / (60 * 1000)) % 60); //  分钟
        let second = parseInt((timeDiff / 1000) % 60); //  秒数
        this.timeDown = hour.toString().padStart(2, '0') + ":" + minute.toString().padStart(2, '0') + ":" + second.toString().padStart(2, '0');
        this.timeDiff--;
        if (timeDiff === 0) {
          clearInterval(this.timeHandle);
        }
      }, 1000);
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getBusinessList()
    },
    // 打开弹窗
    openDialogHandle(type, data) {
      // {
      //   RM_HANG_OUT: "解除挂起",
      //   FINISH: "完成",
      //   HANG_OUT: "挂起",
      //   ORDER_REJECT: "整单驳回",
      //   ORDER_REJECT: "单个菜驳回",
      //   ORDER_FREE: "整单不扣费",
      //   CHANGE_FEE: "修改金额",
      //   ORDER_REFUND: "单个菜品退款"
      // }
      // new
      // RM_HANG_OUT = 'RM_HANG_OUT', '解除挂起'
      // FINISH = 'FINISH', '完成'
      // HANG_OUT = 'HANG_OUT', '挂起'
      // ORDER_REJECT = 'ORDER_REJECT', '驳回'
      // ORDER_FREE = 'ORDER_FREE', '不扣费'
      // CHANGE_FEE = 'CHANGE_FEE', '修改金额'
      // ORDER_REFUND = 'ORDER_REFUND', '退款'
      // REVOKE_ORDER_REFUND = 'REVOKE_ORDER_REFUND', '取消退款'
      // ALL_ORDER_REFUND = 'ALL_ORDER_REFUND', '整单退款'
      if (data) {
        this.dialogFoodData = [data]
      }
      this.dialogType = type
      switch (type) {
        case 'RM_HANG_OUT':
          this.dialogTitle = '解除挂起'
          this.dialogWidth = '400px'
          this.dialogTop = '20vh'
          this.dialogContentText = '确定解除挂起吗？'
          break;
        case 'HANG_OUT':
          this.dialogTitle = '挂起'
          this.dialogWidth = '400px'
          this.dialogTop = '20vh'
          this.dialogContentText = '确定挂起吗？'
          break;
        case 'ORDER_REJECT':
          this.dialogTitle = '请填写驳回原因'
          this.dialogWidth = '400px'
          this.dialogTop = '20vh'
          break;
        case 'ORDER_FREE':
          this.dialogTitle = '整单不扣费'
          this.dialogWidth = '400px'
          this.dialogTop = '30vh'
          this.dialogContentText = '是否进行整单不扣费操作？'
          break;
        case 'FINISH':
          this.dialogTitle = '完成处理'
          this.dialogWidth = '400px'
          this.dialogTop = '30vh'
          this.dialogContentText = '是否进行此完成操作？'
          break;
        case 'CHANGE_FEE':
          this.dialogTitle = '请输入金额'
          this.dialogWidth = '50%'
          this.dialogTop = '30vh'
          break;
        case 'CHANGE_FEE_SOME':
          this.dialogTitle = '部分退款'
          this.dialogWidth = '50%'
          this.dialogTop = '30vh'
          break;
        case 'ALL_ORDER_REFUND':
          this.dialogTitle = '整单退款'
          this.dialogWidth = '400px'
          this.dialogTop = '20vh'
          this.dialogContentText = '是否进行整单退款操作？'
          this.dialogForm.refundPrice = this.appealOrderDetail.pay_fee
          break;
        case 'ORDER_REFUND':
          this.dialogTitle = '退款'
          this.dialogWidth = '50%'
          this.dialogTop = '30vh'
          this.dialogForm.refundPrice = this.dialogFoodData[0].real_fee
          break;
        case 'nutrition':
          this.dialogTitle = '营养'
          this.dialogWidth = '50%'
          this.dialogTop = '10vh'
          this.setDialogNutriton(data)
          break;
        default:
          break;
      }
      this.dialogVisible = true
    },
    showDialogTableResult(data) {
      let text = '未处理'
      switch (data.appeal_results.deal_status) {
        case 'ORDER_REJECT':
          text = !data.appeal_results.reject_reason ? '驳回' : data.appeal_results.reject_reason
          break;
        case 'ORDER_REFUND':
          text = '退款'
          break;
        case 'CHANGE_FEE':
          if (data.orderType === 'goods_cashier') {
            text = '修改后金额为￥' + divide(data.appeal_results.payment_goods_fee)
          } else {
            text = '修改后金额为￥' + divide(data.appeal_results.payment_food_fee)
          }
          break;
        case 'REVOKE_ORDER_REFUND':
          text = '取消退款'
          break;
        default:
          break;
      }
      return text
    },
    // 设置弹窗营养的数据
    setDialogNutriton(row) {
      console.log(row)
      this.formData = {}
      let element = row.element ? row.element : {}
      let vitamin = row.vitamin ? row.vitamin : {}
      NUTRITION_LIST.forEach(nutrition => {
        if (nutrition.type === 'default') {
          this.$set(this.dialogForm, nutrition.key, row[nutrition.key] ? row[nutrition.key] : 0)
        }
        if (nutrition.type === 'element') {
          this.$set(this.dialogForm, nutrition.key, element[nutrition.key] ? element[nutrition.key] : 0)
        }
        if (nutrition.type === 'vitamin') {
          this.$set(this.dialogForm, nutrition.key, vitamin[nutrition.key] ? vitamin[nutrition.key] : 0)
        }
      })
    },
    cancelDialogHandle() {
      this.dialogVisible = false
    },
    closeDialogHandle() {
      this.dialogTitle = ''
      this.dialogContentText = ''
      this.dialogType = ''
      this.dialogForm = {
        refundPrice: '',
        rejectReason: '',
        dealAction: ''
      }
      this.dialogFoodData = []
    },
    submitDialogHaldle() {
      let params = {
        trade_no: this.appealOrderDetail.trade_no,
        order_appeal_id: this.appealOrderDetail.order_appeal_id,
        // reject_reason: '',
        // payment_food_id: '',
        // payment_food_fee: '',
        deal_action: this.dialogType
      }
      if (this.dialogType === 'CHANGE_FEE_SOME') { // 这是前端自定义的字段，要还原下
        params.deal_action = 'CHANGE_FEE'
      }
      console.log(this.dialogFoodData, 777)
      if (this.dialogFoodData.length > 0) {
        // 兼容D2商品
        if (this.appealOrderDetail.payment_order_type === 'goods_cashier') {
          params.payment_goods_id = this.dialogFoodData[0].payment_data_id
        } else {
          params.payment_food_id = this.dialogFoodData[0].payment_data_id
        }
        params.payment_food_fee = times(this.dialogFoodData[0].real_fee)
      }
      let showDialog = false
      let nextStep = true
      let reg = /^(([0]{1,1})|([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;
      switch (this.dialogType) {
        case 'ORDER_REJECT':
          if (!this.dialogForm.rejectReason) {
            this.$message.error('请输入驳回原因！')
            nextStep = false
          }
          params.reject_reason = this.dialogForm.rejectReason
          break;
        case 'ORDER_FREE':
          this.dialogTitle = '整单不扣费'
          break;
        case 'FINISH':
          this.dialogTitle = '完成处理'
          break;
        case 'CHANGE_FEE':
          if (!reg.test(this.dialogForm.refundPrice)) {
            this.$message.error('退款金额格式错误！')
            nextStep = false
          }
          // params.payment_food_id = this.dialogFoodData[0].payment_food_id
          params.payment_food_fee = times(this.dialogForm.refundPrice)
          showDialog = true
          break;
        case 'ORDER_REFUND':
          if (!reg.test(this.dialogForm.refundPrice)) {
            this.$message.error('退款金额格式错误！')
            nextStep = false
          }
          if (Number(this.dialogForm.refundPrice) > Number(this.dialogFoodData[0].real_fee)) {
            this.$message.error(`可退余额${this.dialogFoodData[0].real_fee}元`)
            nextStep = false
          }
          params.payment_food_fee = times(this.dialogForm.refundPrice)
          // params.payment_food_id = this.dialogFoodData[0].payment_food_id
          // params.payment_food_fee = times(this.dialogFoodData[0].real_fee.replace('￥', ''))
          // showDialog = true
          break;
        case 'ALL_ORDER_REFUND':
          if (!reg.test(this.dialogForm.refundPrice)) {
            this.$message.error('退款金额格式错误！')
            nextStep = false
          }
          if (Number(this.dialogForm.refundPrice) > Number(this.appealOrderDetail.pay_fee)) {
            this.$message.error(`可退余额${this.appealOrderDetail.pay_fee}元`)
            nextStep = false
          }
          params.payment_food_fee = times(this.dialogForm.refundPrice)
          break;
      }
      if (!nextStep) {
        return
      }
      if (showDialog) {
        this.$confirm('<p>确定对该订单进行操作吗？</p><p style="color: red;">确定后不可撤销</p>', '提示', {
          dangerouslyUseHTMLString: true,
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          closeOnClickModal: false,
          customClass: 'ps-confirm',
          cancelButtonClass: 'ps-cancel-btn',
          confirmButtonClass: 'ps-btn',
          center: true,
          beforeClose: async (action, instance, done) => {
            if (action === 'confirm') {
              instance.confirmButtonLoading = true
              await this.sendRequestHandle(params)
              done()
              instance.confirmButtonLoading = false
            } else {
              if (!instance.confirmButtonLoading) {
                done()
              }
            }
          }
        })
          .then(e => {})
          .catch(e => {})
      } else {
        this.sendRequestHandle(params)
      }
    },
    async sendRequestHandle(params) {
      if (this.isLoading) {
        return this.$message.error('请勿重复提交！')
      }
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundOrderOrderAppealDealOrderAppealPost(params))
      this.isLoading = false
      this.timeDown = ''
      clearInterval(this.timeHandle);
      if (err) {
        this.$message.error(err.message)
        return
      }
      console.log('sendRequestHandle', res)
      if (res.code === 0) {
        this.dialogVisible = false
        this.$message.success(res.msg)
        this.getAppealDetail()
        if (this.dialogType === 'FINISH') {
          this.$closeCurrentTab(this.$route.path)
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    //  显示或者隐藏原菜品信息
    clickShowDishDialog() {
      this.isShowOldDishs = true
    }
  },
  beforeDestroy() {
    if (this.timeHandle) {
      clearInterval(this.timeHandle);
    }
  }
}
</script>

<style lang="scss" scoped>
.appeal-order-detail {
  margin-bottom: 60px;
  .box-content{
    position: relative;
    .btn-origin-detail {
      position: absolute;
      bottom: 23px;
      left: 780px;
    }
  }
  .user-info-wrapper {
    overflow-x: auto;
    display: flex;
    align-items: center;
    font-size: 14px;
    .user-img {
      width: 70px;
      margin-right: 20px;
    }
    .user-info-r {
      min-width: 1050px;
      .float-l {
        float: left;
      }
      .info-item {
        display: flex;
        margin-right: 40px;
        padding: 5px 0;
      }
    }
  }
  .order-price-box{
    margin-top: 10px;
    display: inline-block;
    border: 1px solid #e2e8f0;
    min-width: 750px;
    .order-p-item{
      position: relative;
      padding: 5px 20px;
      min-width: 140px;
      float: left;
      text-align: center;
      color: #23282d;
      &:not(:last-child) {
        &:after{
         position: absolute;
          content: '';
          top: 20px;
          right: 0;
          bottom: 20px;
          width: 1px;
          background-color: #e2e8f0;
        }
      }
      .order-label{
        font-size: 14px;
        opacity: .7;
      }
    }
    .red{
      color: red;
    }
  }
  .appeal-item{
    margin-bottom: 10px;
    .appeal-item-label{
      font-size: #23282d;
      opacity: .7;
    }
  }
  .appeal-footer{
    width: 100%;
    padding: 15px;
    padding-right: 60px;
    position: fixed;
    bottom: 0;
    right: 0;
    background-color: #fff;
    text-align: right;
    z-index: 9999;
  }
}
</style>
<style lang="scss">
.appeal-dialog {
  border-radius: 5px;
  .el-dialog__body{
    padding: 10px 20px;
    max-height: 82vh;
    overflow-y: auto;
    .price-box{
      .price-p{
        margin-bottom: 10px;
        .price-label{
          margin-right: 15px;
        }
      }
    }
    .nutrition-item{
      // display: flex;
      // justify-content: space-around;
      // flex-wrap: wrap;
      display: inline-block;
      width: 200px;
      .nutrition-label{
        margin-bottom: 3px;
        font-size: 14px;
        letter-spacing: 1px;
        color: #23282d;
      }
    }
  }
  .dialog-content-text{
    margin-bottom: 10px;
  }
}
.box-right{
  position: absolute;
  right: 30px;
  top: 15px;
}
</style>
