/* eslint-disable quote-props */
import { getDateRang } from "@/utils"
import * as dayjs from 'dayjs'

export const recentSevenDay = [
  dayjs()
    .subtract(7, 'day')
    .format('YYYY-MM-DD'),
  dayjs().format('YYYY-MM-DD')
]

// 营养价签相关
export const TableSetting = [
  { label: '设备名称', key: 'device_name' },
  { label: '图片1', key: '1', type: "slot", slotName: "default" },
  { label: '图片2', key: '2', type: "slot", slotName: "default" },
  { label: '图片3', key: '3', type: "slot", slotName: "default" },
  { label: '操作', key: 'operation', type: "slot", slotName: "operation" }
]
export const TabTypeList = [
  {
    name: '显示设置',
    key: 'showSetting'
  },
  {
    name: '图片设置',
    key: 'imgSetting'
  }
]
export const TableButtonList = [
  {
    name: '一键清空',
    key: 'cleanImg'
  },
  {
    name: '导入图片',
    key: 'importImg'
  }
]
export const DishPatternTableButton = [
  {
    name: '一键清空',
    key: 'cleanDish'
  },
  {
    name: '复制',
    key: 'copy'
  }
]

export const WeekList = [
  {
    label: '全部',
    key: 0
  },
  {
    label: '周一',
    key: 1
  },
  {
    label: '周二',
    key: 2
  },
  {
    label: '周三',
    key: 3
  },
  {
    label: '周四',
    key: 4
  },
  {
    label: '周五',
    key: 5
  },
  {
    label: '周六',
    key: 6
  },
  {
    label: '周日',
    key: 7
  }
]

export const SwitchDate = (index) => {
  let str = ''
  switch (index) {
    case 0:
      str = '周一'
      break
    case 1:
      str = '周二'
      break
    case 2:
      str = '周三'
      break
    case 3:
      str = '周四'
      break
    case 4:
      str = '周五'
      break
    case 5:
      str = '周六'
      break
    case 6:
      str = '周日'
      break
  }
  return str
}

// 设备型号对应的图片
export const DEVICE_IMG = {
  'PS-502': 'PS-502.png',
  'PS-503': 'PS-503.png',
  'PS-1050': 'PS-1050.png',
  'PS-1314': 'PS-1314.png',
  'PS-1500': 'PS-1500.png',
  'PS-1516': 'PS-1516.png',
  'PS-BOX32': 'PS-BOX32.png',
  'PS-BOX-BX10': 'PS-BOX-BX10.png',
  'PS-BOX-BX20': 'PS-BOX-BX20.png',
  'PS-C1050': 'PS-C1050.png',
  'PS-C1050-1': 'PS-C1050-1.png',
  'PS-C1050-2': 'PS-C1050-2.png',
  'PS-C1051': 'PS-C1051.png',
  'PS-D2': 'PS-D2.png',
  'PS-HY11S': 'PS-HY11S.png',
  'PS-HY11W': 'PS-HY11W.png',
  'PS-K1 02': 'PS-K1 02.png',
  'PS-k1': 'PS-k1.png',
  'PS-KW001': 'PS-KW001.png',
  'PS-M2': 'PS-M2.png',
  'PS-P2': 'PS-P2.png',
  'PS-TP': 'PS-TP.png',
  'PS-ZJ001': 'PS-ZJ001.png',
  'PS-ZY001': 'PS-ZY001.png',
  'PS-ZY002右': 'PS-ZY002右.png',
  'PS-ZY002左': 'PS-ZY002左.png',
  'PS-ZZKWJ': 'PS-ZZKWJ.png',
  'PS-H8': 'PS-H8.png',
  'PS-LY01': 'PS-LYY.png',
  'PS-D2D': 'PS-D2D.png',
  'PS-K1-TP': 'PS-K1-TP.png',
  'PS-TPJ': 'PS-TPJ.png',
  'CXP': 'CXP.png',
  'PS-KB01': 'PS-KB01.png',
  'PS-KC001': 'PS-KC001.png',
  'PS-D2mini': 'PS-D2.png',
  'CJY': 'CJY.png',
  'LYG': 'LYG.png'
}

export const PRINT_ADMIN_SEARCH = {
  org_ids: {
    type: 'organizationSelect',
    value: [],
    label: '适用组织',
    checkStrictly: true,
    isLazy: false,
    collapseTags: true,
    multiple: true
  },
  type: {
    type: 'select',
    label: '类型',
    value: '',
    placeholder: '请选择类型',
    dataList: [{
      label: '全部',
      value: ''
    }, {
      label: '热敏纸打印机',
      value: 'thermal_paper'
    }, {
      label: '标签打印机',
      value: 'label'
    }]
  },
  sn: {
    type: 'input',
    label: '序列号',
    value: '',
    placeholder: '请输入序列号'
  },
  name: {
    type: 'input',
    label: '名称',
    value: '',
    placeholder: '请输入名称'
  }
}

export const PRINT_LIST_SEARCH = {
  create_time: {
    timeRange: true,
    type: 'daterange',
    label: '创建时间',
    clearable: true,
    value: getDateRang(0, { format: '{y}-{m}-{d}' })
  },
  print_time: {
    timeRange: true,
    type: 'daterange',
    label: '打印时间',
    clearable: true,
    value: []
  },
  print_no: {
    type: 'input',
    label: '打印单号',
    value: '',
    placeholder: '请输入打印单号'
  },
  task_status: {
    type: 'select',
    label: '打印状态',
    value: '',
    multiple: true,
    clearable: true,
    collapseTags: true,
    placeholder: '请选择打印状态',
    dataList: [{
      label: '未开始',
      value: 'not_started'
    }, {
      label: '打印中',
      value: 'printing'
    }, {
      label: '打印成功',
      value: 'success'
    }, {
      label: '打印异常',
      value: 'terminated'
    }, {
      label: '打印终止',
      value: 'abnormal'
    }]
  }
}

export const PRINT_ADMIN_TABLE = [
  { label: '打印机品牌', key: 'brand_alias' },
  { label: '序列号/编号', key: 'sn', width: 160 },
  { label: '名称', key: 'name' },
  { label: '类型', key: 'type_alias' },
  { label: '设备状态', key: 'printer_status_alias' },
  { label: '适用组织', key: 'org' },
  { label: '运行状态', key: 'running_status_alias' },
  { label: '操作', key: 'operation', type: "slot", slotName: "operation", fixed: "right", width: "220" }
]

export const PRINT_LIST_TABLE = [
  { label: '打印单号', key: 'print_no' },
  { label: '创建时间', key: 'create_time' },
  { label: '打印时间', key: 'print_time' },
  { label: '打印进度/张', key: 'progress', type: "slot", slotName: "progress" },
  { label: '打印份数', key: 'print_num' },
  { label: '打印状态', key: 'task_status_alias' },
  { label: '打印设备', key: 'printer_name' },
  { label: '操作', key: 'operation', type: "slot", slotName: "operation", fixed: "right", width: "140" }
]

export const getRequestParams = (searchFormSetting, page, pageSize) => {
  const searchData = {}
  Object.keys(searchFormSetting).forEach(key => {
    if (searchFormSetting[key].timeRange && searchFormSetting[key].value?.length > 0) {
      searchData['start_' + key] = searchFormSetting[key].value[0]
      searchData['end_' + key] = searchFormSetting[key].value[1]
    } else if (
      searchFormSetting[key].value !== '' &&
      searchFormSetting[key].value &&
      searchFormSetting[key].value?.length > 0
    ) {
      searchData[key] = searchFormSetting[key].value
    }
  })
  const params = {
    page,
    page_size: pageSize,
    ...searchData
  }

  return params
}
