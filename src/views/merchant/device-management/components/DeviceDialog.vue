<template>
  <dialog-message
    :show.sync="visible"
    :title="title"
    :loading.sync="isLoading"
    @close="handleClose"
    customClass="ps-dialog"
    :width="width"
  >
    <el-form
      :model="deviceForm"
      @submit.native.prevent
      status-icon
      ref="deviceForm"
      :rules="deviceFormRules"
      label-width="120px"
      inline
      class="dialog-form"
      v-loading="isLoading"
      v-if="visible"
    >
      <div v-if="type==='group'">
        <!-- <el-form-item label="修改后的分组" prop="group" label-width="120px">
          <user-group-select
            class="search-item-w ps-input w-180"
            v-model="deviceForm.group"
            :multiple="true"
            collapseTags
            clearable
            placeholder="请下拉选择"
          ></user-group-select>
        </el-form-item> -->
        <el-form-item label="" label-width="0" prop="groupType">
          <el-select v-model="deviceForm.groupType" class="ps-select">
            <el-option
              v-for="item in groupTypeList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <div class="red m-b-10">{{ deviceForm.groupType === 'all' ? '全天所有餐段仅适用分组可使用该设备' : '可按餐段设置设备的适用分组' }}</div>
        <div v-if="deviceForm.groupType==='meal'">
          <el-form-item v-for="meal in mealList" :key="meal.value" :label="meal.label" label-width="90px" :prop="'allowUserGroup.'+meal.value" class="text-align-left">
            <user-group-select
              class="search-item-w ps-input w-180"
              v-model="deviceForm.allowUserGroup[meal.value]"
              :multiple="true"
              collapseTags
              clearable
              placeholder="请下拉选择"
              :option-data="groupList"
              :show-other="true"
            ></user-group-select>
          </el-form-item>
        </div>
        <div v-if="deviceForm.groupType==='all'">
          <el-form-item  label="全天" label-width="90px" prop="allowUserGroup.all" class="text-align-left">
            <user-group-select
              class="search-item-w ps-input w-180"
              v-model="deviceForm.allowUserGroup.all"
              :multiple="true"
              collapseTags
              clearable
              placeholder="请下拉选择"
              :option-data="groupList"
            ></user-group-select>
          </el-form-item>
        </div>
      </div>
      <div v-if="type==='name'">
        <el-form-item label="设备名：" prop="deviceName">
          <el-input
            v-model="deviceForm.deviceName"
            placeholder="请输入设备名"
            class="ps-input w-250"
          ></el-input>
        </el-form-item>
      </div>
      <div v-if="type==='setting'">
        <el-form-item label="菜谱设置：" v-if="deviceInfo.device_type !== 'QCG'">
          <el-radio-group class="ps-radio" v-model="deviceForm.menuListType" @change="changeMenuList">
            <el-radio label="week">周菜谱</el-radio>
            <el-radio label="month">月菜谱</el-radio>
          </el-radio-group>
          <!-- <div>
            <el-form-item :label="deviceForm.menuListType === 'week'?'请选择周菜谱':'请选择月菜谱'" label-width="80" prop="menuId">
              <el-select v-model="deviceForm.menuId" class="ps-select w-180">
                <el-option
                  v-for="item in menuList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </div> -->
        </el-form-item>
        <el-form-item label="入口密码：" prop="password">
          <el-input
            v-model="deviceForm.password"
            placeholder="请输入入口密码"
            class="ps-input w-250"
          ></el-input>
        </el-form-item>
        <el-form-item label="打印密码：" prop="printPwd">
          <el-input
            v-model="deviceForm.printPwd"
            placeholder="不设置则打印无需密码"
            class="ps-input w-250"
          ></el-input>
        </el-form-item>
         <!-- v-if="deviceInfo.device_type == 'QCG' || deviceInfo.device_type == 'SCJ'" -->
        <el-form-item v-if="!noShowuseOrganizationsType.includes(deviceInfo.device_type) && deviceInfo.device_model !=='PS-k1'" label="可消费组织：" prop="payUseOrganizations">
          <organization-select
            class="search-item-w ps-input"
            placeholder="请选择组织"
            v-model="deviceForm.payUseOrganizations"
            :isLazy="false"
            :multiple="true"
            :check-strictly="true"
            role="merchant"
            :append-to-body="true"
            :filterable="false"
            :disabled-list="[deviceInfo.organization]"
            :check-disabled-and-parents="true"
            :disabled-same-level="true"
            :allow-disabled-same-child="true"
            >
            <!-- :only-child="true" -->
          </organization-select>
        </el-form-item>
        <!-- 取餐柜的只有适用组织 看板程序只有适用组织-->
        <el-form-item v-if="deviceInfo.device_type === 'QCG' || deviceInfo.device_type === 'KBCX'" label="适用组织：" prop="useOrganizations">
          <organization-select
            class="search-item-w ps-input"
            placeholder="请选择组织"
            v-model="deviceForm.useOrganizations"
            :isLazy="false"
            :multiple="true"
            :check-strictly="true"
            role="merchant"
            :append-to-body="true"
            :filterable="false"
            :org-id="deviceInfo.organization"
            :only-show-org-id-tree="true"
            >
            <!-- :only-child="true" -->
          </organization-select>
        </el-form-item>
        <!-- 智能称没有核销一说 -->
        <el-form-item v-if="!noShowuseOrganizationsType.includes(deviceInfo.device_type) && deviceInfo.device_type !== 'JST' && deviceInfo.device_model !=='PS-k1'" label="可核销组织：" prop="useOrganizations">
          <organization-parent-select
            class="search-item-w ps-input"
            placeholder="请选择组织"
            v-model="deviceForm.useOrganizations"
            :isLazy="false"
            :multiple="true"
            :check-strictly="true"
            role="merchant"
            :append-to-body="true"
            :filterable="false"
            :disabled-list="[deviceInfo.organization]"
            :org-id="deviceInfo.organization"
            :disabled-same-level="true"
            :allow-disabled-same-child="true"
            >
            <!-- :only-child="true" -->
          </organization-parent-select>
        </el-form-item>
        <!-- <el-form-item label="退款设置：" prop="isRefund">
          是否支持退款<el-switch v-model="deviceForm.isRefund" active-color="#ff9b45" inactive-color="#ffcda2"></el-switch>
        </el-form-item>
        <el-form-item v-if="deviceForm.isRefund" label="退款密码：" prop="refundPassword">
          <el-input
            v-model="deviceForm.refundPassword"
            placeholder="请输入退款密码"
            class="ps-input w-250"
          ></el-input>
        </el-form-item>
        <el-form-item v-if="deviceForm.isRefund" label="退款时间：" prop="refundType">
          <el-radio-group class="ps-radio" v-model="deviceForm.refundType">
            <el-radio label="any">任意时间</el-radio>
            <el-radio label="custom" style="margin-right:30px;">自定义时间</el-radio>
            <el-radio label="meal" style="margin-right:30px;">按餐段设置</el-radio>
          </el-radio-group>
        </el-form-item> -->
        <el-form-item v-if="deviceInfo.device_type === 'ZZKWJ'" label="设备提示语：">
          <el-input
            v-model="deviceForm.promptMessage"
            placeholder="例：如有疑问请联系管理员"
            class="ps-input w-250"
            maxlength="25"
          ></el-input>
        </el-form-item>
        <el-form-item v-if="deviceForm.refundType === 'custom'" label=" ">
          <span>允许订单在</span>
          <el-input-number v-model="deviceForm.refundTime" :min="0" :max="24"></el-input-number>
          <span>小时内，可进行退款</span>
        </el-form-item>
        <el-form-item v-if="deviceForm.refundType === 'meal'" label=" ">
          <span>允许餐段</span>
          <el-input-number v-model="deviceForm.refundTime" :min="0" :max="24"></el-input-number>
          <span>小时内，可进行退款</span>
        </el-form-item>
      </div>
      <div v-if="type==='muleditname'">
        <el-table
          v-loading="isLoading"
          :data="deviceForm.editData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
          class="ps-table-tree"
        >
          <el-table-column prop="device_name" label="原设备名" align="center"></el-table-column>
          <el-table-column prop="" label="修改设备名" align="center">
            <template slot-scope="scope">
              <el-input class="ps-input" v-model="scope.row.newName"></el-input>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!--自动售卖机只有 设置可消费组织 -->
      <div v-if="type === 'autoSelling'">
        <el-form-item  label="可消费组织：" prop="payUseOrganizations">
          <organization-select
            class="search-item-w ps-input"
            placeholder="请选择组织"
            v-model="deviceForm.payUseOrganizations"
            :isLazy="false"
            :multiple="true"
            :check-strictly="true"
            role="merchant"
            :append-to-body="true"
            :filterable="false"
            :disabled-list="[deviceInfo.organization]"
            :check-disabled-and-parents="true"
            :disabled-same-level="true"
            :allow-disabled-same-child="true"
            >
          </organization-select>
        </el-form-item>
      </div>
    </el-form>
    <template slot="tool">
      <div slot="footer" class="dialog-footer" style="margin-top: 20px; text-align: right;">
        <el-button
          :disabled="isLoading"
          class="ps-cancel-btn"
          @click="clickCancleHandle"
        >
          取消
        </el-button>
        <el-button
          :disabled="isLoading"
          class="ps-btn"
          type="primary"
          @click="clickConfirmHandle"
        >
          确定
        </el-button>
      </div>
    </template>
  </dialog-message>
  <!-- end -->
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import UserGroupSelect from '@/components/UserGroupSelect'
import OrganizationSelect from '@/components/OrganizationSelect'
import OrganizationParentSelect from '@/components/OrganizationParentSelect'
import { to, deepClone } from "@/utils/index"
export default {
  name: 'trayDialog',
  components: {
    UserGroupSelect,
    OrganizationSelect,
    OrganizationParentSelect
  },
  props: {
    loading: Boolean,
    isshow: Boolean,
    type: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    width: {
      type: String,
      default: '600px'
    },
    deviceInfo: {
      type: Object,
      default() {
        return {}
      }
    },
    deviceList: {
      type: Array,
      default() {
        return []
      }
    },
    confirm: Function
  },
  // mixins: [activatedLoadData],
  data() {
    let validateRefundPass = (rule, value, callback) => {
      let regPass = /^[0-9A-Za-z]{8,20}$/;
      if (value && !regPass.test(value)) {
        callback(new Error("密码长度8~20位，英文加数字"));
      } else {
        callback();
      }
    };
    let validatePass = (rule, value, callback) => {
      if (!value) {
        return callback(new Error("密码不能为空"));
      } else {
        let regPass = /^[0-9A-Za-z]{8,20}$/;
        if (!regPass.test(value)) {
          callback(new Error("密码长度8~20位，英文加数字"));
        } else {
          callback();
        }
      }
    };
    return {
      isLoading: false,
      deviceForm: {
        group: [],
        groupType: '',
        allowUserGroup: {
          all: [],
          breakfast: [],
          lunch: [],
          afternoon: [],
          dinner: [],
          supper: [],
          morning: []
        },
        deviceName: '',
        menuListType: 'week',
        menuId: '',
        password: '',
        isRefund: false,
        refundPassword: '',
        refundType: 'any',
        refundTime: 0,
        editData: [],
        promptMessage: '', // 自助卡务机提示语
        useOrganizations: [], // 适用组织，个别类型更改为可核销组织
        payUseOrganizations: [], // 可消费组织
        printPwd: '' // 打印密码
      },
      deviceFormRules: {
        deviceName: [{ required: true, message: '请输入设备名', trigger: 'blur' }],
        menuId: [{ required: true, message: '请选择菜谱', trigger: 'change' }],
        password: [{ required: true, validator: validatePass, trigger: 'blur' }],
        printPwd: [{ required: false, validator: validateRefundPass, trigger: 'blur' }],
        refundPassword: [{ validator: validateRefundPass, trigger: 'blur' }],
        groupType: [{ required: true, message: '请选择分组类型', trigger: 'change' }]
        // 'allowUserGroup.all': [{ required: true, message: '请选择分组', trigger: 'change' }],
        // 'allowUserGroup.breakfast': [{ required: true, message: '请选择分组', trigger: 'change' }],
        // 'allowUserGroup.lunch': [{ required: true, message: '请选择分组', trigger: 'change' }],
        // 'allowUserGroup.afternoon': [{ required: true, message: '请选择分组', trigger: 'change' }],
        // 'allowUserGroup.dinner': [{ required: true, message: '请选择分组', trigger: 'change' }],
        // 'allowUserGroup.supper': [{ required: true, message: '请选择分组', trigger: 'change' }],
        // 'allowUserGroup.morning': [{ required: true, message: '请选择分组', trigger: 'change' }]
        // group: [{ required: true, message: '请选择分组', trigger: 'change' }]
      },
      menuList: [],
      groupTypeList: [
        { label: '统一设置适用分组', value: 'all' },
        { label: '按餐段设置适用分组', value: 'meal' }
      ],
      mealList: [
        // { label: '全部', value: 'all' },
        { label: '早餐', value: 'breakfast' },
        { label: '午餐', value: 'lunch' },
        { label: '下午茶', value: 'afternoon' },
        { label: '晚餐', value: 'dinner' },
        { label: '夜宵', value: 'supper' },
        { label: '凌晨餐', value: 'morning' }
      ],
      groupList: [], // 分组
      noShowuseOrganizationsType: ['ZNC', 'QCG', 'TPJ', 'CPT', 'K1', 'RLTPBDJ', 'ZZKWJ', 'M2', 'KBCX'] // 不需要显示可核销组织的
    }
  },
  computed: {
    visible: {
      get() {
        if (this.isshow) {
          // this.initGroup()
        }
        return this.isshow
      },
      set(val) {
        this.$emit('update:isshow', val)
      }
    }
  },
  watch: {
    visible() {
      if (this.visible) {
        this.userGroupList(this.deviceInfo.organization)
      }
      if (this.type === 'muleditname') {
        this.deviceForm.editData = []
        this.deviceList.map(item => {
          this.deviceForm.editData.push({
            ...item,
            newName: ''
          })
        })
      } else if (this.type === 'setting') {
        this.deviceForm.password = this.deviceInfo.device_settings_pwd
        this.deviceForm.refundPassword = this.deviceInfo.device_refund_pwd
        this.deviceForm.menuListType = this.deviceInfo.menu_type
        this.deviceForm.menuId = this.deviceInfo.menu_type_id
        this.deviceForm.printPwd = this.deviceInfo.print_pwd
        // eslint-disable-next-line eqeqeq
        // 取餐柜无需增加“可支付/核销组织”的选择
        if (this.deviceInfo.device_type !== 'QCG') {
          this.deviceForm.payUseOrganizations = this.deviceInfo.pay_use_organizations
        }
        if (this.deviceInfo.device_type === 'QCG') {
          this.deviceForm.useOrganizations = this.deviceInfo.cupboard_organization_ids
        } else if (this.deviceInfo.device_type !== 'ZNC') { // 智能秤无需增加“可核销组织”的选择
          this.deviceForm.useOrganizations = this.deviceInfo.use_organizations
        }
        // 当前的默认组织,仅用于显示
        this.deviceForm.useOrganizations.push(this.deviceInfo.organization)

        switch (this.deviceInfo.can_refund) {
          case 1:
            this.deviceForm.isRefund = true
            this.deviceForm.refundType = 'any'
            break;
          case 2:
            this.deviceForm.isRefund = true
            this.deviceForm.refundType = 'custom'
            this.deviceForm.refundTime = this.deviceInfo.refund_time
            break;
          case 3:
            this.deviceForm.isRefund = true
            this.deviceForm.refundType = 'meal'
            this.deviceForm.refundTime = this.deviceInfo.refund_time
            break;
        }
        // 自助卡务机提示语
        if (this.deviceInfo.device_type === 'ZZKWJ') {
          this.deviceForm.promptMessage = this.deviceInfo.prompt_message
        }
        this.getMenuList()
      } else if (this.type === 'name') {
        this.deviceForm.deviceName = this.deviceInfo.device_name
      } else if (this.type === 'group') {
        // this.deviceForm.group = this.deviceInfo.group_id
        // if (this.deviceInfo.user_group_map.length > 0) {
        //   this.deviceForm.group = this.deviceInfo.user_group_map.map(group => {
        //     return group.id
        //   })
        // }
        if (this.deviceInfo.allow_user_group) {
          this.deviceForm.groupType = this.deviceInfo.allow_user_group_setting
          if (this.deviceInfo.allow_user_group_setting === 'all') {
            this.deviceForm.allowUserGroup.all = this.deviceInfo.allow_user_group.all ? this.deviceInfo.allow_user_group.all : []
          } else {
            this.mealList.map(meal => {
              this.deviceForm.allowUserGroup[meal.value] = this.deviceInfo.allow_user_group[meal.value]
            })
          }
        }
      } else if (this.type === 'autoSelling') {
        // 配置可消费组织回显
        this.deviceForm.payUseOrganizations = this.deviceInfo.pay_use_organizations
      }
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {
  },
  methods: {
    initLoad() {
      this.getMenuList()
    },
    // 获取分组
    async userGroupList(organization) {
      const res = await this.$apis.apiCardServiceCardUserGroupListPost({
        status: 'enable',
        is_show_other: true,
        // organization: organization,
        page: 1,
        page_size: 99999
      })
      if (res.code === 0) {
        this.groupList = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    clickConfirmHandle() {
      this.$refs.deviceForm.validate(valid => {
        console.log(11, this.deviceForm)
        if (valid) {
          let params
          switch (this.type) {
            case 'group':
              params = {
                device_no: this.deviceInfo.device_no,
                // user_group_ids: this.deviceForm.group
                user_group_setting: this.deviceForm.groupType
              }
              if (this.deviceForm.groupType === 'all') {
                params.allow_user_group = {
                  all: this.deviceForm.allowUserGroup.all
                }
              } else {
                params.allow_user_group = {}
                this.mealList.map(meal => {
                  params.allow_user_group[meal.value] = this.deviceForm.allowUserGroup[meal.value]
                })
              }
              console.log(params)
              this.modifyDevice(params)
              break;
            case 'name':
              params = {
                device_no: this.deviceInfo.device_no,
                device_name: this.deviceForm.deviceName
              }
              this.modifyDevice(params)
              break;
            case 'muleditname': {
              let ids = []
              let data = []
              this.deviceForm.editData.map(item => {
                ids.push(item.device_no)
                data.push({
                  device_name: item.newName,
                  device_no: item.device_no
                })
              })
              params = {
                choices: 0,
                device_nos: ids,
                data
              }
              this.modifyMulName(params)
              break;
            }
            case 'setting':
              params = {
                device_no: this.deviceInfo.device_no,
                device_settings_pwd: this.deviceForm.password,
                menu_type: this.deviceForm.menuListType,
                menu_type_id: this.deviceForm.menuId,
                print_pwd: this.deviceForm.printPwd
              }
              if (this.deviceForm.isRefund) {
                params.device_refund_pwd = this.deviceForm.refundPassword
                if (this.deviceForm.refundType === 'any') {
                  params.can_refund = 1
                } else if (this.deviceForm.refundType === 'custom') {
                  params.can_refund = 2
                  params.refund_time = this.deviceForm.refundTime
                } else if (this.deviceForm.refundType === 'meal') {
                  params.can_refund = 3
                  params.refund_time = this.deviceForm.refundTime
                }
              } else {
                params.can_refund = 0
              }
              // 自助卡务机提示语
              if (this.deviceInfo.device_type === 'ZZKWJ' && this.deviceForm.promptMessage) {
                params.prompt_message = this.deviceForm.promptMessage
              }
              // eslint-disable-next-line no-case-declarations
              const index = this.deviceForm.useOrganizations.indexOf(this.deviceInfo.organization)
              // eslint-disable-next-line no-case-declarations
              const payIndex = this.deviceForm.payUseOrganizations.indexOf(this.deviceInfo.organization)
              // eslint-disable-next-line no-case-declarations
              const organizations = deepClone(this.deviceForm.useOrganizations)
              // eslint-disable-next-line no-case-declarations
              const payUseOrganizations = deepClone(this.deviceForm.payUseOrganizations)
              if (index > -1) {
                organizations.splice(index, 1)
              }
              if (payIndex > -1) {
                payUseOrganizations.splice(payIndex, 1)
              }

              if (this.deviceInfo.device_type === 'QCG') {
                params.cupboard_organization_ids = organizations
              } else if (this.deviceInfo.device_type !== 'ZNC') {
                params.use_organizations = organizations
              }
              if (this.deviceInfo.device_type !== 'QCG') {
                params.pay_use_organizations = payUseOrganizations
              }
              // if (this.deviceInfo.device_type === 'SCJ') {
              //   params.use_organizations = this.deviceForm.useOrganizations
              // }
              this.modifyDeviceConfig(params)
              break;
            // 自动售卖机确认
            case "autoSelling":
              if (!this.deviceForm.payUseOrganizations) {
                return this.$messge.error("请选择可消费组织")
              }
              var payUseOrganizationList = deepClone(this.deviceForm.payUseOrganizations)
              params = {
                device_no: this.deviceInfo.device_no,
                pay_use_organizations: payUseOrganizationList,
                can_refund: 0
              }
              console.log("autoSelling", params);
              this.modifyAutoSelling(params)
              break;
          }
        } else {
        }
      })
    },
    // 批量修改设备名
    async modifyMulName(params) {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundDeviceDeviceBatchModifyPost(params)
      this.isLoading = false
      if (res.code === 0) {
        this.$message.success('修改成功')
        // this.confirm()
        this.$emit('confirm', 'search')
      } else {
        this.$message.error(res.msg)
      }
    },
    // 修改设备名、分组
    async modifyDevice(params) {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundDeviceDeviceModifyPost(params)
      this.isLoading = false
      if (res.code === 0) {
        this.$message.success('修改成功')
        this.$emit('confirm', 'search')
      } else {
        this.$message.error(res.msg)
      }
    },
    // 修改设备设置
    async modifyDeviceConfig(params) {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundDeviceDeviceConfigPost(params)
      this.isLoading = false
      if (res.code === 0) {
        this.$message.success('修改成功')
        this.$emit('confirm', 'search')
      } else {
        this.$message.error(res.msg)
      }
    },
    clickCancleHandle() {
      this.visible = false
      // this.$emit('cancel')
    },
    handleClose(e) {
      this.isLoading = false
      this.visible = false
      this.$refs.deviceForm && this.$refs.deviceForm.resetFields()
      // this.$emit('close')
    },
    changeMenuList() {
      this.deviceForm.menuId = ''
      this.getMenuList()
    },
    // 获取周/月菜谱列表
    async getMenuList() {
      let res
      if (this.deviceForm.menuListType === 'week') {
        res = await this.$apis.apiBackgroundFoodMenuWeeklyListPost()
      } else {
        // 获取月菜谱列表
        res = await this.$apis.apiBackgroundFoodMenuMonthlyListPost()
      }
      if (res.code === 0) {
        this.menuList = res.data.results
      } else {
        this.menuList = []
        this.$message.error(res.msg)
      }
    },
    // 修改自动售卖机设置可消费组织
    async modifyAutoSelling(params) {
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundDeviceDeviceConfigPost(params))
      this.isLoading = false
      if (err) {
        return this.$message.error(err.$message)
      }
      if (res && res.code === 0) {
        this.$message.success('修改成功')
        this.$emit('confirm', 'search')
      } else {
        this.$message.error(res.msg)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/styles/variables.scss';
.code-form-item-wrapper {
  display: flex;
  .code-btn-wrapper {
    margin-left: 20px;
  }
}
.detail{
  &__span{
    width: 150px;
    display: inline-block;
  }
}
.dialog-form{
  .red{
    color: red;
  }
  .text-align-left {
    ::v-deep.el-form-item__label{
      text-align: left;
    }
  }
}
</style>
