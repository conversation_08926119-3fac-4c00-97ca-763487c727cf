<template>
  <div class="RoutineSetting container-wrapper">
    <!-- <refresh-tool @refreshPage="refreshHandle" /> -->
    <div class="table-wrapper" style="margin-top: 0px;margin-bottom: 50px;">
      <div class="table-header">
        <div>
          <div class="table-title" style="display: flex;align-items: center; justify-content: space-between;width: 380px;">适用组织：
            <organization-select
              class="search-item-w ps-input w-250"
              placeholder="请选择所属组织"
              :isLazy="false"
              :multiple="false"
              :check-strictly="true"
              v-model="organizationId"
              @change="changeOrganization"
              :append-to-body="true"
              >
            </organization-select>
          </div>
        </div>
        <div style="padding-right:20px;">
          <el-button size="small" type="primary" class="ps-origin-btn" @click="checkForm('self')">保存</el-button>
          <el-button size="small" type="primary" class="ps-plain-btn" @click="openOtherOrg">适用到其他组织</el-button>
        </div>
      </div>
      <div class="setting-wrap">
        <div class="tab-type">
          <div v-for="item in tabTypeList" :key="item.key" :class="['tabItem',tabType===item.key?'activeTab':'']" @click="changeType(item.key)">{{item.name}}</div>
        </div>
        <el-form
          :model="settingForm"
          :rules="settingFormRules"
          ref="settingForm"
        >
          <div v-show="tabType === '1'">
            <div class="title">就餐场景</div>
            <el-form-item>
              <el-radio-group class="ps-radio" v-model="settingForm.mealMode" @change="changeMealMode">
                <el-radio label="HF">智能称重</el-radio>
                <!-- <el-radio label="DE">固定金额任吃</el-radio>
                <el-radio label="ZD">即拿即付-自动扣款</el-radio>
                <el-radio label="SD">即拿即付-手动扣款</el-radio> -->
                <el-radio label="PT">普通称重</el-radio>
                <!-- 原来的 无感称-先吃后付，改成无感秤-人脸模式-->
                <el-radio label="WG">无感称-人脸模式</el-radio>
                <el-radio label="WG_TRAY">无感称-智能绑盘</el-radio>
              </el-radio-group>
            </el-form-item>
            <div v-if="settingForm.mealMode === 'HF'">
              <div class="title">支付模式</div>
              <el-form-item>
                <el-radio-group class="ps-radio" v-model="settingForm.payModel">
                  <el-radio label="unify">统一支付</el-radio>
                  <el-radio label="single">一菜一付</el-radio>
                </el-radio-group>
              </el-form-item>
            </div>
            <div v-if="settingForm.mealMode==='DE'">
              <el-form-item prop="fixedPrice">
                <el-input v-model="settingForm.fixedPrice" class="margin-input w-180 ps-input"></el-input>元
                <span class="tips">（ 注：一托一价 ）</span>
              </el-form-item>
              <el-form-item prop="minute">
                <el-input v-model.number="settingForm.minute" class="margin-input w-180 ps-input"></el-input>分钟内托盘订单已被支付，则可任意取餐
              </el-form-item>
            </div>
            <div v-if="settingForm.mealMode==='SD'">
              <el-form-item>
                <span class="margin-r-20">设备存在待支付订单是否支持绑定托盘</span>
                <el-radio-group class="ps-radio" v-model="settingForm.isSupportBind">
                  <el-radio :label="true">支持</el-radio>
                  <el-radio :label="false">不支持</el-radio>
                </el-radio-group>
              </el-form-item>
            </div>
            <div class="title">就餐限制</div>
            <el-form-item label="是否支持绑定多托盘：" v-if="settingForm.mealMode !== 'WG'">
              <el-radio-group class="ps-radio margin-r-30" v-model="settingForm.isSupportMore">
                <el-radio :label="-1">不支持</el-radio>
                <el-radio :label="1" :disabled="isSupportMoreDisable">支持</el-radio>
              </el-radio-group>
              <div class="inline-box" style="margin-right: 20px;" v-if="settingForm.isSupportMore === 1">
                托盘数量
                <el-form-item class="form-content-inline" prop="trayNum">
                  <el-input v-model.number="settingForm.trayNum" class="margin-input w-180 ps-input"></el-input>
                </el-form-item>
                个
              </div>
              <div class="inline-box" v-if="settingForm.isSupportMore === 1">
                绑定间隔不能超过
                <el-form-item class="form-content-inline" prop="bindSecond">
                  <el-input v-model.number="settingForm.bindSecond" class="margin-input w-180 ps-input"></el-input>
                </el-form-item>
                秒
              </div>
            </el-form-item>
            <el-form-item label="在线绑定额度限制">
              <el-radio-group class="ps-radio margin-r-30" v-model="settingForm.onlineLimit">
                <el-radio :label="-1">无限制</el-radio>
                <el-radio :label="1">额度限制</el-radio>
              </el-radio-group>
              <div class="inline-box" v-if="settingForm.onlineLimit === 1">
                额度大于等于
                <el-form-item class="form-content-inline" prop="onlineQuota">
                  <el-input v-model="settingForm.onlineQuota" class="margin-input w-180 ps-input"></el-input>
                </el-form-item>
                元，才能绑定托盘
              </div>
            </el-form-item>
            <el-form-item label="离线绑定额度限制">
              <el-radio-group class="ps-radio margin-r-30" v-model="settingForm.offlineLimit">
                <el-radio :label="-1">无限制</el-radio>
                <el-radio :label="1">额度限制</el-radio>
              </el-radio-group>
              <div class="inline-box" v-if="settingForm.offlineLimit === 1">
                额度大于等于
                <el-form-item class="form-content-inline" prop="offlineQuota">
                  <el-input v-model="settingForm.offlineQuota" class="margin-input w-180 ps-input"></el-input>
                </el-form-item>
                元，才能绑定托盘
              </div>
            </el-form-item>
            <!-- 自动结算订单时间（分钟）-->
            <div>
              <el-form-item
                class="form-content-inline"
                prop="autoReleaseTrayTime"
                label="自动释放托盘时间（分钟）:"
                v-if="settingForm.mealMode=='HF' && settingForm.payModel === 'single'">
                <el-slider v-model="settingForm.autoReleaseTrayTime" :min="minTime" :max="maxTime" class="w-350"></el-slider>
              </el-form-item>
              <el-form-item
                class="form-content-inline"
                prop="autoPayOrderTime"
                label="自动结算订单时间（分钟）:"
                v-if="(settingForm.mealMode=='HF' && settingForm.payModel === 'unify') || settingForm.mealMode === 'WG' || settingForm.mealMode === 'WG_TRAY'">
                <el-slider v-model="settingForm.autoPayOrderTime" :min="minTime" :max="maxTime" class="w-350"></el-slider>
              </el-form-item>
            </div>
            <div class="title"  v-if="settingForm.mealMode === 'WG'">绑定限制</div>
            <el-form-item label=""  v-if="settingForm.mealMode === 'WG'">
              <div class="inline-box margin-r-30">
                <span class="margin-r-5">刷卡绑定</span>
                <el-switch v-model="settingForm.isCardPay" active-color="#ff9b45" inactive-color="#ffcda2"></el-switch>
              </div>
              <div class="inline-box margin-r-30">
                <span class="margin-r-5">人脸绑定</span>
                <el-switch v-model="settingForm.isFacePay" active-color="#ff9b45" inactive-color="#ffcda2"></el-switch>
              </div>
            </el-form-item>
            <!-- 在线在线人脸识别roi： -->
            <div>
              <el-form-item class="form-content-inline" prop="onlineFaceRoi" label="在线在线人脸识别roi：" v-if="settingForm.mealMode === 'WG'">
                <el-slider v-model="settingForm.onlineFaceRoi" :step="0.01" :min="0" :max="1" class="w-350"></el-slider>
              </el-form-item>
            </div>
            <!-- 称重阀值： -->
            <div>
              <el-form-item class="form-content-inline" prop="weighingThreshold" label="称重阀值：" v-if="settingForm.mealMode === 'WG'">
                <el-slider v-model="settingForm.weighingThreshold" :min="0" :max="20" class="w-350"></el-slider>
              </el-form-item>
            </div>
            <!-- 稳定时长阀值： -->
            <el-form-item class="form-content-inline" prop="stabilityThreshold" label="稳定时长阀值：" v-if="settingForm.mealMode === 'WG'">
              <el-slider v-model="settingForm.stabilityThreshold" :min="1" :max="60" class="w-350"></el-slider>
            </el-form-item>
            <!-- 额外加了判断 WG情况下 不需要支付(绑定)设置 绑定设置是智能称重的，支付设置是普通称重的-->
            <div v-if="settingForm.mealMode !== 'WG'">
              <div class="title">{{settingForm.mealMode === 'HF' || settingForm.mealMode === 'WG_TRAY' ? '绑定设置' : '支付设置'}}</div>
              <el-form-item class="form-label">
                <span class="margin-r-5">{{settingForm.mealMode === 'HF' || settingForm.mealMode === 'WG_TRAY' ? '刷卡绑定' : '刷卡支付'}}</span>
                <el-switch v-model="settingForm.openCardPay" active-color="#ff9b45" inactive-color="#ffcda2"></el-switch>
                <span class="margin-l-30 margin-r-5">{{settingForm.mealMode === 'HF' || settingForm.mealMode === 'WG_TRAY' ? '人脸绑定' : '人脸支付'}}</span>
                <el-switch v-model="settingForm.openFacePay" active-color="#ff9b45" inactive-color="#ffcda2"></el-switch>
                <span class="margin-l-30 margin-r-5" v-if="settingForm.mealMode !== 'WG_TRAY'">{{settingForm.mealMode === 'HF' ? '扫码绑定' : '扫码支付'}}</span>
                <el-switch v-model="settingForm.openCodePay" active-color="#ff9b45" inactive-color="#ffcda2" v-if="settingForm.mealMode !== 'WG_TRAY'"></el-switch>
              </el-form-item>
              <el-form-item label="在线人脸识别距离（像素）" label-width="180px" v-if="settingForm.openFacePay">
                <el-slider class="detection-range-slider w-250" v-model="settingForm.faceDistanceScore" :min="100" :max="300"></el-slider>
              </el-form-item>
              <el-form-item :label="settingForm.mealMode === 'HF' ? '人脸绑定是否确认：' : '人脸支付是否确认：'"
               v-if="settingForm.mealMode !== 'WG_TRAY'">
                <el-switch v-model="settingForm.facePayconfirm" active-color="#ff9b45" inactive-color="#ffcda2"></el-switch>
              </el-form-item>
              <el-form-item label="支付信息停留时间（秒）：" v-if="!(settingForm.mealMode === 'HF' && settingForm.payModel === 'unify') && settingForm.mealMode !== 'WG_TRAY'">
                <el-input-number class="ps-input-number" v-model="settingForm.payInfoWaitTime" :min="1" :max="10" label=""></el-input-number>
              </el-form-item>
              <!-- <div class="title">称重消费规则</div>
              <el-form-item>
                <el-radio-group class="ps-radio" v-model="settingForm.weightConsumptionType" @change="weightConsumptionTypeChange">
                  <el-radio label="SJ">无</el-radio>
                  <el-radio label="DE">折扣消费</el-radio>
                  <el-radio label="ZK">固定金额</el-radio>
                </el-radio-group>
              </el-form-item> -->
              <div v-if="settingForm.weightConsumptionType === 'DE'" class="form-text-size">
                每单折扣
                <el-form-item prop="weightDiscount" class="form-content-inline">
                  <el-input v-model.number="settingForm.weightDiscount" class="margin-input w-180 ps-input"></el-input>
                </el-form-item>
                %
                <span class="tips">注：一托一单；仅输入整数</span>
              </div>
              <div v-if="settingForm.weightConsumptionType === 'ZK'" class="form-text-size">
                每称固定金额
                <el-form-item prop="weightFixedPrice" class="form-content-inline">
                  <el-input v-model="settingForm.weightFixedPrice" class="margin-input w-180 ps-input"></el-input>
                </el-form-item>
                元
                <span class="tips">注：一称一价；使用固定金额任吃模式不支持使用该规则</span>
              </div>
            </div>
          </div>
          <div>
          <div v-show="tabType === '2'">
            <div class="title">菜谱启用</div>
            <el-form-item label="设备主页面菜谱">
              <el-radio-group class="ps-radio" v-model="settingForm.homeMenuType">
                <el-radio label="day">按日期</el-radio>
                <el-radio label="week">按周</el-radio>
              </el-radio-group>
            </el-form-item>
            <!-- <el-form-item label="设备菜谱列表">
              <el-radio-group class="ps-radio margin-r-30" v-model="settingForm.menuListType" @change="changeMenuList">
                <el-radio label="normal">无</el-radio>
                <el-radio label="week">周菜谱</el-radio>
                <el-radio label="month">月菜谱</el-radio>
              </el-radio-group>
              <div class="inline-box" v-if="settingForm.menuListType!=='normal'">
                <el-form-item :label="settingForm.menuListType === 'week'?'请选择周菜谱':'请选择月菜谱'" class="form-content-inline" prop="menuId">
                  <el-select v-model="settingForm.menuId" class="ps-select w-180">
                    <el-option
                      v-for="item in menuList"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </div>
            </el-form-item> -->
            <div class="title">设备预警</div>
            <div class="form-text-size">
              <span class="form-label">菜品余量提示</span>
              低于
              <el-form-item class="form-content-inline" prop="foodNotEnough">
                <el-input v-model="settingForm.foodNotEnough" class="margin-input w-180 ps-input"></el-input>
              </el-form-item>
              克，提示菜品余量不足，
              低于
              <el-form-item class="form-content-inline" prop="foodSellOut">
                <el-input v-model="settingForm.foodSellOut" class="margin-input w-180 ps-input"></el-input>
              </el-form-item>
              克，提示菜品已售罄
            </div>
            <el-form-item label="在线取餐额度限制" v-if="settingForm.mealMode === 'HF' && settingForm.payModel === 'single'">
              <el-radio-group class="ps-radio" v-model="settingForm.getTrayBalanceWarningLimit">
                  <el-radio :label="false">无限制</el-radio>
                  <el-radio :label="true">额度限制</el-radio>
                </el-radio-group>
              <div class="inline-box m-l-20" v-if="settingForm.getTrayBalanceWarningLimit">
                额度小于等于
                <el-form-item class="form-content-inline" prop="getTrayBalanceWarningLimitAmount">
                  <el-input v-model="settingForm.getTrayBalanceWarningLimitAmount" class="margin-input w-180 ps-input"></el-input>
                </el-form-item>
                元，提示余额不足，请勿取餐
              </div>
            </el-form-item>
            <el-form-item label="余额报警额度限制" v-if="settingForm.mealMode === 'WG' || settingForm.mealMode === 'WG_TRAY'">
              <el-radio-group class="ps-radio" v-model="settingForm.balanceWarning">
                  <el-radio :label="false">无限制</el-radio>
                  <el-radio :label="true">额度限制</el-radio>
                </el-radio-group>
              <div class="inline-box m-l-20" v-if="settingForm.balanceWarning">
                额度小于等于
                <el-form-item class="form-content-inline" prop="balanceWarningLimitAmount">
                  <el-input v-model="settingForm.balanceWarningLimitAmount" class="margin-input w-180 ps-input"></el-input>
                </el-form-item>
                元，提示余额不足，请充值后取餐
              </div>
            </el-form-item>
            <el-form-item label="异常是否报警">
              <div class="inline-box margin-r-30">
                <span class="margin-r-5">余量不足</span>
                <el-switch v-model="settingForm.isWarningWeight" active-color="#ff9b45" inactive-color="#ffcda2"></el-switch>
              </div>
              <div class="inline-box margin-r-30">
                <span class="margin-r-5">菜品售罄</span>
                <el-switch v-model="settingForm.isWarningOut" active-color="#ff9b45" inactive-color="#ffcda2"></el-switch>
              </div>
              <div class="inline-box margin-r-30" v-if="settingForm.mealMode === 'HF' && settingForm.payModel === 'single'">
                <span class="margin-r-5">取餐额度</span>
                <el-switch v-model="settingForm.getTrayBalanceWarning" active-color="#ff9b45" inactive-color="#ffcda2"></el-switch>
              </div>
              <div class="inline-box margin-r-30" v-if="settingForm.mealMode === 'WG' || settingForm.mealMode === 'WG_TRAY'">
                <span class="margin-r-5">余额不足</span>
                <el-switch v-model="settingForm.balanceWarningLimit" active-color="#ff9b45" inactive-color="#ffcda2"></el-switch>
              </div>
            </el-form-item>
            <!-- <el-form-item label="报警设置">
              <div class="inline-box margin-r-30">
                <span class="margin-r-5">报警灯</span>
                <el-switch v-model="settingForm.warningLights" active-color="#ff9b45" inactive-color="#ffcda2"></el-switch>
              </div>
              <div class="inline-box margin-r-30">
                <span class="margin-r-5">报警声</span>
                <el-switch v-model="settingForm.warningSound" active-color="#ff9b45" inactive-color="#ffcda2"></el-switch>
              </div>
              <div class="inline-box margin-r-30">
                <span class="margin-r-5">报警时长</span>
                <el-input-number class="ps-input-number" v-model="settingForm.warningTime" :min="1" :max="10" label=""></el-input-number>
              </div>
            </el-form-item> -->
            <div class="title">报警设置</div>
            <div>
              <el-form-item class="form-content-inline" prop="warningLightNum" label="报警灯（秒）" >
                <el-slider v-model="settingForm.warningLightNum" :min="0" :max="60" class="w-350"></el-slider>
              </el-form-item>
            </div>
            <div>
            <el-form-item class="form-content-inline" prop="warningSoundNum" label="报警声（次）" >
              <el-slider v-model="settingForm.warningSoundNum" :min="0" :max="10" class="w-350"></el-slider>
            </el-form-item>
            </div>
            <div class="ps-text font-size-15">(当活动条数值为0时，关闭对应报警功能)</div>
            <div class="title">其他设置</div>
            <el-form-item label="设备首页图显示时长">
              <el-input-number class="ps-input-number" v-model="settingForm.bannerTime" :min="1" :max="10" label=""></el-input-number>
            </el-form-item>
            <el-form-item label="开机是否进行重量重置">
              <el-radio-group class="ps-radio" v-model="settingForm.startUpReset">
                <el-radio :label="true">是</el-radio>
                <el-radio :label="false">否</el-radio>
              </el-radio-group>
            </el-form-item>
            <!-- <div class="title">管理卡</div>
            <div class="card-admin">
              <div class="card-admin__add card-admin__size__border">
                <img src="@/assets/img/add.png" alt="" srcset=""><span>添加管理员卡</span>
              </div>
              <div class="card-admin-item card-admin__size__border" v-for="item in cardAdminList" :key="item.id">
                <div class="card-admin-item-left">
                  <img v-if="item.gender==='WOMEN'" src="@/assets/img/women.png" alt="" srcset="">
                  <img v-else src="@/assets/img/man.png" alt="" srcset="">
                  <div class="card-admin-item-left-info">
                    <div class="card-admin-item-left-info-name">{{item.name}}</div>
                    <div class="card-admin-item-left-info-cardNo">{{item.card_no}}</div>
                  </div>
                </div>
                <div class="card-admin-item-btn">删除</div>
              </div>
            </div> -->
          </div>
          <div v-show="tabType === '3'">
            <div class="offline-title">
              <div class="title">自动开启离线模式</div>
              <el-switch v-model="settingForm.autoOpenOfflineMode" active-color="#ff9b45" inactive-color="#ffcda2"></el-switch>
            </div>
            <el-form-item>
              <el-radio-group class="ps-radio" v-model="settingForm.openOfflineType" @change="changeOpenOfflineType">
                <el-radio label="auto">根据网络状态切换</el-radio>
                <el-radio label="on_date">固定时间开启</el-radio>
                <el-radio label="on_meal_time">固定餐段开启</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item prop="openOfflineTime" class="margin-l-20" v-if="settingForm.openOfflineType === 'on_date'">
              <!-- <el-time-picker
                v-model="settingForm.openOfflineTime"
                value-format="HH:mm"
                format="HH:mm"
                placeholder="选择时间">
              </el-time-picker> -->
              <el-time-picker
                is-range
                v-model="settingForm.openOfflineTime"
                value-format="HH:mm"
                format="HH:mm"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                placeholder="选择时间范围">
              </el-time-picker>
            </el-form-item>
            <el-form-item prop="openOfflineMeal" class="margin-l-20" v-if="settingForm.openOfflineType === 'on_meal_time'">
              <el-checkbox-group v-model="settingForm.openOfflineMeal">
                <el-checkbox v-for="item in mealList" :key="item.key" :label="item.key" :disabled="item.disabled" class="ps-checkbox">{{item.name}}</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <div class="offline-title">
              <div class="title">自动上传离线订单</div>
              <el-switch v-model="settingForm.autoUploadOfflineOrder" active-color="#ff9b45" inactive-color="#ffcda2"></el-switch>
            </div>
            <el-form-item>
              <el-radio-group class="ps-radio" v-model="settingForm.uploadOfflineType">
                <el-radio label="auto">联网自动上传</el-radio>
                <el-radio label="on_date" :disabled="settingForm.openOfflineType!=='auto'">固定时间上传</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item prop="uploadOfflineTime" class="margin-l-20" v-if="settingForm.uploadOfflineType === 'on_date'">
              <el-time-picker
                v-model="settingForm.uploadOfflineTime"
                value-format="HH:mm"
                format="HH:mm"
                placeholder="选择时间">
              </el-time-picker>
            </el-form-item>
            <div class="title">离线人脸相关</div>
            <el-form-item label="是否自动开启离线人脸支付" v-if="settingForm.mealMode !== 'WG'" label-width="180px">
              <el-switch v-model="settingForm.isAutoOpenOfflineFacePay" active-color="#ff9b45" inactive-color="#ffcda2"></el-switch>
              <span class="tips">注：关闭则设备离线时，不支持使用人脸绑盘/支付。</span>
            </el-form-item>
            <el-form-item label="离线人脸识别分数" label-width="180px">
              <el-slider class="detection-range-slider w-250" v-model="settingForm.offlineFaceRecognizeScore" :min="75" :max="100"></el-slider>
            </el-form-item>
            <el-form-item label="离线人脸识别距离（像素）" label-width="180px">
              <el-slider class="detection-range-slider w-250" v-model="settingForm.detectionRange" :min="100" :max="200"></el-slider>
            </el-form-item>
            <el-form-item label="离线是否开启活体检测" label-width="180px">
              <el-switch v-model="settingForm.isAutoOpenLiveDetection" active-color="#ff9b45" inactive-color="#ffcda2"></el-switch>
            </el-form-item>
            <el-form-item label="活体检测识别分数" label-width="180px">
              <el-slider class="detection-range-slider w-250" v-model="settingForm.liveFaceRecognizeScore" :min="50" :max="100"></el-slider>
              <span class="tips">分数越高，误刷率越低</span>
            </el-form-item>
          </div>
          </div>
        </el-form>
      </div>
    </div>
    <setting-dialog
      :isshow.sync="dialogVisible"
      :type="dialogType"
      :title="dialogTitle"
      :confirm="dialogConfirm"
      @otherOrgConfirm="otherOrgConfirm"
    />
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import SettingDialog from './components/SettingDialog.vue'
import OrganizationSelect from '@/components/OrganizationSelect'
import { divide, times } from '@/utils'

export default {
  name: 'RoutineSetting',
  components: {
    OrganizationSelect,
    SettingDialog
  },
  props: {},
  // mixins: [activatedLoadData],
  data() {
    let validMoney = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('金额不能为空'))
      } else {
        let reg = /^-?(([0-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/
        if (!reg.test(value)) {
          callback(new Error('金额格式有误'))
        } else {
          callback()
        }
      }
    }
    let validWeight = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('克数不能为空'))
      } else {
        let reg = /^[+]{0,1}(\d+)$|^[+]{0,1}(\d+\.\d+)$/
        if (!reg.test(value)) {
          callback(new Error('菜品余量格式有误'))
        } else if (this.settingForm.foodNotEnough && this.settingForm.foodSellOut && this.settingForm.foodNotEnough < this.settingForm.foodSellOut) {
          callback(new Error('余量不足设置的值必须大于售罄值'))
        } else {
          callback()
        }
      }
    }
    return {
      isLoading: false,
      organizationId: '',
      organizationIds: [],
      tabType: '1',
      tabTypeList: [
        {
          name: '就餐设置',
          key: '1'
        },
        {
          name: '基础设置',
          key: '2'
        },
        {
          name: '离线设置',
          key: '3'
        }
      ],
      settingForm: {
        // 就餐场景
        mealMode: 'HF', // 场景选择
        payModel: 'unify', // 智能称重的统一支付、一菜一付
        fixedPrice: '', // 固定金额
        minute: '', // 固定金额-多少分钟内支付
        isSupportBind: 0, // 存在待支付订单是否支持绑定托盘
        // 就餐限制
        isSupportMore: -1, // 是否支持绑定多托盘：
        bindSecond: '', // 绑定间隔
        trayNum: '', // 托盘数量
        onlineLimit: -1, // 在线绑定额度限制
        onlineQuota: '', // 在线绑定额度
        offlineLimit: -1, // 离线绑定额度限制
        offlineQuota: '', // 离线绑定额度
        // 支付设置
        openCardPay: false, // 刷卡支付
        openFacePay: false, // 人脸支付
        openCodePay: false, // 扫码支付
        facePayconfirm: false, // 人脸支付是否确认
        faceDistanceScore: 300, // 在线人脸识别距离
        payInfoWaitTime: 2, // 支付信息停留时间（秒）
        // 称重消费规则
        weightConsumptionType: 'SJ', // 称重消费规则类型
        weightDiscount: '', // 每单折扣
        weightFixedPrice: '', // 每称固定金额
        // 菜谱启用
        homeMenuType: 'day', // 设备主页面菜谱
        menuListType: 'week', // 设备菜谱列表
        menuId: '', // 设备菜谱列表——周菜谱/月菜谱
        // 设备预警
        foodNotEnough: 500, // 提示菜品余量不足
        foodSellOut: 100, // 提示菜品已售罄
        balanceWarning: false, // 余额报警限制
        balanceWarningLimitAmount: '', // 余额报警额度限制金额
        getTrayBalanceWarningLimit: false, // 取餐额度限制
        getTrayBalanceWarningLimitAmount: '', // 取餐额度限制金额
        isWarningWeight: false, // 余额报警额度限制
        isWarningOut: false, // 菜品售罄是否报警
        balanceWarningLimit: false, // 余额不足是否报警
        getTrayBalanceWarning: false, // 取餐额度是否报警
        warningLights: false, // 报警灯
        warningSound: false, // 报警声
        warningTime: 0, // 报警声
        warningLightNum: 3, // 报警灯
        warningSoundNum: 0, // 报警声
        // 其他设置
        bannerTime: 0,
        startUpReset: false,
        // 自动开启离线模式
        autoOpenOfflineMode: true, // 是否自动开启离线模式
        openOfflineType: 'auto', // 根据网络状态切换
        openOfflineTime: [], // 固定时间开启
        openOfflineMeal: [], // 固定餐段开启
        // 自动上传离线订单
        autoUploadOfflineOrder: true, // 是否自动上传离线订单
        uploadOfflineType: 'auto', // 联网自动上传
        uploadOfflineTime: '', // 固定时间上传
        isAutoOpenOfflineFacePay: true, // 是否自动开启离线人脸支付
        offlineFaceRecognizeScore: 0, // 离线人脸识别分数
        detectionRange: 0, // 离线人脸识别距离（像素）
        isAutoOpenLiveDetection: true, // 离线是否开启活体检测
        liveFaceRecognizeScore: 0, // 活体检测识别分数
        autoPayOrderTime: 15, //  自动结算时间 默认15分钟
        autoReleaseTrayTime: 10, //  自动释放餐盘时间 默认10分钟
        isCardPay: false, // 刷卡绑定
        isFacePay: true, // 人脸绑定
        onlineFaceRoi: 0.35,
        weighingThreshold: 5,
        stabilityThreshold: 3
      },
      menuList: [],
      mealList: [{
        key: 'breakfast',
        name: '早餐',
        disabled: false
      }, {
        key: 'lunch',
        name: '午餐',
        disabled: false
      }, {
        key: 'afternoon',
        name: '下午茶',
        disabled: false
      }, {
        key: 'dinner',
        name: '晚餐',
        disabled: false
      }, {
        key: 'supper',
        name: '宵夜',
        disabled: false
      }, {
        key: 'morning',
        name: '凌晨餐',
        disabled: false
      }],
      cardAdminList: [{
        id: '1',
        name: 'lihua',
        card_no: '123456',
        gender: 'WOMEN'
      }, {
        id: '2',
        name: 'lihua',
        card_no: '123456',
        gender: 'MAN'
      }, {
        id: '3',
        name: 'lihua',
        card_no: '123456',
        gender: ''
      }],
      settingFormRules: {
        fixedPrice: [{ required: true, validator: validMoney, trigger: 'blur' }],
        minute: [
          { required: true, message: '请输入分钟数' },
          { type: 'number', message: '请输入整数' }
        ],
        bindSecond: [
          { required: true, message: '请输入秒数' },
          { type: 'number', message: '请输入整数' }
        ],
        trayNum: [
          { required: true, message: '请输入托盘数量' },
          { type: 'number', message: '请输入整数' }
        ],
        onlineQuota: [{ required: true, validator: validMoney, trigger: 'blur' }],
        offlineQuota: [{ required: true, validator: validMoney, trigger: 'blur' }],
        foodNotEnough: [{ required: true, validator: validWeight, trigger: 'blur' }],
        foodSellOut: [{ required: true, validator: validWeight, trigger: 'blur' }],
        weightDiscount: [
          { required: true, message: '请输入折扣' },
          { type: 'number', message: '请输入整数' }
        ],
        weightFixedPrice: [{ required: true, validator: validMoney, trigger: 'blur' }],
        menuId: [{ required: true, message: '请选择菜谱', trigger: 'change' }],
        openOfflineTime: [{ required: true, message: '请选择时间', trigger: 'change' }],
        openOfflineMeal: [{ type: 'array', required: true, message: '请至少选择一个餐段', trigger: 'change' }],
        uploadOfflineTime: [{ required: true, message: '请选择时间', trigger: 'change' }],
        balanceWarningLimitAmount: [{ required: true, validator: validMoney, trigger: 'blur' }],
        getTrayBalanceWarningLimitAmount: [{ required: true, validator: validMoney, trigger: 'blur' }]
      },
      dialogVisible: false,
      dialogTitle: '',
      dialogType: '',
      isSupportMoreDisable: false,
      minTime: 3,
      maxTime: 30
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getMenuList()
      this.organizationId = Number(sessionStorage.getItem('organization'))
      this.getSettingDetail()
      this.getOrgMeal()
    },
    // 刷新页面
    refreshHandle() {
    },
    checkForm(type) {
      let params = {
        meal_scene: this.settingForm.mealMode,
        pay_model: this.settingForm.payModel,
        meal_scene_conf: {},
        is_many_tray: this.settingForm.isSupportMore === 1 ? this.settingForm.bindSecond : -1,
        online_fee_limit: this.settingForm.onlineLimit === 1 ? this.settingForm.onlineQuota : -1,
        offline_fee_limit: this.settingForm.offlineLimit === 1 ? this.settingForm.offlineQuota : -1,
        can_micropay: this.settingForm.openCardPay,
        can_facepay: this.settingForm.openFacePay,
        can_scanpay: this.settingForm.openCodePay,
        face_pay_verify: this.settingForm.facePayconfirm,
        face_distance_score: this.settingForm.openFacePay ? this.settingForm.faceDistanceScore : 300,
        payinfo_wait_time: this.settingForm.payInfoWaitTime,
        consume_rule: this.settingForm.weightConsumptionType,
        // consume_rule_conf: {}, // 暂时为空对象，后续再做修改
        menu_page: this.settingForm.homeMenuType,
        menu_type: type === 'other' ? 'normal' : this.settingForm.menuListType,
        menu_type_id: this.settingForm.menuListType === 'normal' || type === 'other' ? null : Number(this.settingForm.menuId),
        warn_weight: this.settingForm.foodNotEnough,
        out_weight: this.settingForm.foodSellOut,
        balance_warning: this.settingForm.balanceWarning,
        balance_warning_limit_amount: this.settingForm.balanceWarningLimitAmount || 0,
        get_tray_balance_warning_limit: this.settingForm.getTrayBalanceWarningLimit,
        get_tray_balance_warning_limit_amount: times(this.settingForm.getTrayBalanceWarningLimitAmount) || 0,
        warn_weight_warning: this.settingForm.isWarningWeight,
        out_weight_warning: this.settingForm.isWarningOut,
        balance_warning_limit: this.settingForm.balanceWarningLimit,
        get_tray_balance_warning: this.settingForm.getTrayBalanceWarning,
        warning_lights: this.settingForm.warningLightNum !== 0,
        warning_sound: this.settingForm.warningSoundNum !== 0,
        // warning_span: this.settingForm.warningTime,
        homepage_span: this.settingForm.bannerTime,
        start_up_reset: this.settingForm.startUpReset,
        offline_model: this.settingForm.autoOpenOfflineMode ? this.settingForm.openOfflineType : 'off',
        offline_model_conf: {},
        online_model_conf: {},
        offline_order: this.settingForm.autoUploadOfflineOrder ? this.settingForm.uploadOfflineType : 'off',
        offline_order_conf: {},
        offline_face_pay: this.settingForm.isAutoOpenOfflineFacePay,
        offline_face_recognize_score: this.settingForm.offlineFaceRecognizeScore,
        detection_range: this.settingForm.detectionRange,
        live_detection: this.settingForm.isAutoOpenLiveDetection,
        live_face_recognize_score: this.settingForm.liveFaceRecognizeScore,
        warning_sound_count: this.settingForm.warningSoundNum ? this.settingForm.warningSoundNum : 0,
        warning_span: this.settingForm.warningLightNum ? this.settingForm.warningLightNum : 0
      }
      if (this.settingForm.isSupportMore === 1) {
        params.many_tray_count = this.settingForm.trayNum // 多托盘
      }
      if (this.settingForm.mealMode === 'DE') {
        params.meal_scene_conf = {
          fee: Number(this.settingForm.fixedPrice),
          minute: this.settingForm.minute
        }
      } else if (this.settingForm.mealMode === 'SD') {
        params.meal_scene_conf = {
          can_bind_tray: this.settingForm.isSupportBind
        }
      }
      // 关于离线模式
      if (this.settingForm.openOfflineTime && this.settingForm.openOfflineTime[0] && this.settingForm.openOfflineTime[1]) {
        let timeOff = this.settingForm.openOfflineTime[0].split(':')
        params.offline_model_conf = {
          timestamp: timeOff[0] * 60 * 60 + timeOff[1] * 60
        }
        let timeOn = this.settingForm.openOfflineTime[1].split(':')
        params.online_model_conf = {
          timestamp: timeOn[0] * 60 * 60 + timeOn[1] * 60
        }
      }
      if (this.settingForm.openOfflineMeal.length) {
        params.offline_model_conf.meal = this.settingForm.openOfflineMeal
      }

      if (this.settingForm.uploadOfflineTime) {
        let time = this.settingForm.uploadOfflineTime.split(':')
        params.offline_order_conf = {
          timestamp: time[0] * 60 * 60 + time[1] * 60
        }
      }
      params.auto_pay_order_time = this.settingForm.autoPayOrderTime
      params.auto_release_tray_time = this.settingForm.autoReleaseTrayTime
      params.is_card_pay = this.settingForm.isCardPay
      params.is_face_pay = this.settingForm.isFacePay
      params.online_face_roi = this.settingForm.onlineFaceRoi
      params.weighing_threshold = this.settingForm.weighingThreshold
      params.stability_threshold = this.settingForm.stabilityThreshold

      if (this.settingForm.mealMode === 'WG') {
        if (!this.settingForm.isCardPay && !this.settingForm.isFacePay) {
          return this.$message.error('请选择绑定限制')
        }
      }
      this.$refs.settingForm.validate(valid => {
        if (valid) {
          if (type === 'self') {
            if (!this.organizationId) {
              this.$message.error('请选择适用组织')
              return
            }
            params.mode = 'self'
            params.org_no = this.organizationId
          } else {
            params.mode = 'other'
            params.org_nos = this.organizationIds
          }
          this.saveRoutineSetting(params)
        } else {
          this.$message.error('数据填写有误，请检查')
          return false
        }
      })
    },
    async saveRoutineSetting(params) {
      this.$confirm(`当前消费点可能正在就餐，是否保存？`, `提示`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            const res = await this.$apis.apiBackgroundDeviceDeviceBuffetAddPost(params)
            if (res.code === 0) {
              this.$message.success('保存成功')
              this.getSettingDetail()
            } else {
              this.$message.error(res.msg)
            }
            done()
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {
        })
        .catch(e => {})
    },
    async getSettingDetail() {
      const res = await this.$apis.apiBackgroundDeviceDeviceBuffetDetailsPost({
        org_no: this.organizationId
      })
      if (res.code === 0) {
        if (res.data) {
          let data = res.data.setting
          // 就餐场景
          this.settingForm.mealMode = data.meal_scene
          this.settingForm.payModel = data.pay_model
          this.settingForm.fixedPrice = data.meal_scene_conf.fee
          this.settingForm.minute = data.meal_scene_conf.minute
          this.settingForm.isSupportBind = data.meal_scene_conf.can_bind_tray
          // 就餐限制
          this.settingForm.isSupportMore = data.is_many_tray === -1 ? -1 : 1
          this.settingForm.trayNum = data.many_tray_count
          this.settingForm.bindSecond = data.is_many_tray === -1 ? '' : data.is_many_tray
          this.settingForm.onlineLimit = data.online_fee_limit === -1 ? -1 : 1
          this.settingForm.onlineQuota = data.online_fee_limit === -1 ? '' : data.online_fee_limit
          this.settingForm.offlineLimit = data.offline_fee_limit === -1 ? -1 : 1
          this.settingForm.offlineQuota = data.offline_fee_limit === -1 ? '' : data.offline_fee_limit
          // 支付设置
          this.settingForm.openCardPay = data.can_micropay
          this.settingForm.openFacePay = data.can_facepay
          this.settingForm.openCodePay = data.can_scanpay
          this.settingForm.facePayconfirm = data.face_pay_verify
          this.settingForm.payInfoWaitTime = data.payinfo_wait_time
          this.settingForm.faceDistanceScore = data.face_distance_score ? data.face_distance_score : 300
          // 称重消费规则
          this.settingForm.weightConsumptionType = data.consume_rule
          // this.settingForm.weightDiscount = data.consume_rule_conf
          // this.settingForm.weightFixedPrice = data.consume_rule_conf
          // 菜谱启用
          this.settingForm.homeMenuType = data.menu_page
          this.settingForm.menuListType = data.menu_type
          this.settingForm.menuId = data.menu_type_no
          // 设备预警
          this.settingForm.foodNotEnough = data.warn_weight
          this.settingForm.foodSellOut = data.out_weight
          this.settingForm.balanceWarning = data.balance_warning
          this.settingForm.balanceWarningLimitAmount = data.balance_warning_limit_amount
          this.settingForm.getTrayBalanceWarningLimit = data.get_tray_balance_warning_limit
          this.settingForm.getTrayBalanceWarningLimitAmount = divide(data.get_tray_balance_warning_limit_amount)
          this.settingForm.isWarningWeight = data.warning_warn_weight
          this.settingForm.isWarningOut = data.warning_out_weight
          this.settingForm.balanceWarningLimit = data.balance_warning_limit
          this.settingForm.getTrayBalanceWarning = data.get_tray_balance_warning
          this.settingForm.warningLights = data.warning_lights
          this.settingForm.warningSound = data.warning_sound
          this.settingForm.warningTime = data.warning_span
          this.settingForm.warningSoundNum = data.warning_sound && data.warning_sound_count && typeof data.warning_sound_count === 'number' ? data.warning_sound_count : 0
          this.settingForm.warningLightNum = data.warning_lights && data.warning_span && typeof data.warning_sound_count === 'number' ? data.warning_span : 0
          // 其他设置
          this.settingForm.bannerTime = data.homepage_span
          this.settingForm.startUpReset = data.start_up_reset
          // 自动开启离线模式
          // eslint-disable-next-line no-unneeded-ternary
          this.settingForm.autoOpenOfflineMode = data.offline_model === 'off' ? false : true
          this.settingForm.openOfflineType = data.offline_model
          if (data.offline_model_conf.timestamp && data.online_model_conf.timestamp) {
            this.settingForm.openOfflineTime[0] = this.formateTimestamp(data.offline_model_conf.timestamp)
            this.settingForm.openOfflineTime[1] = this.formateTimestamp(data.online_model_conf.timestamp)
          } else {
            this.settingForm.openOfflineTime = null
          }
          // this.settingForm.openOfflineMeal = data.offline_model_conf.meal
          // 自动上传离线订单
          // eslint-disable-next-line no-unneeded-ternary
          this.settingForm.autoUploadOfflineOrder = data.offline_order === 'off' ? false : true
          this.settingForm.uploadOfflineType = data.offline_order
          this.settingForm.uploadOfflineTime = data.offline_order_conf.timestamp ? this.formateTimestamp(data.offline_order_conf.timestamp) : ''
          this.settingForm.isAutoOpenOfflineFacePay = data.offline_face_pay
          this.settingForm.offlineFaceRecognizeScore = data.offline_face_recognize_score
          this.settingForm.detectionRange = data.detection_range
          this.settingForm.isAutoOpenLiveDetection = data.live_detection
          this.settingForm.liveFaceRecognizeScore = data.live_face_recognize_score
          // 自动结算时间
          this.settingForm.autoPayOrderTime = data.auto_pay_order_time ? data.auto_pay_order_time : 15
          this.settingForm.autoReleaseTrayTime = data.auto_release_tray_time ? data.auto_release_tray_time : 10
          this.settingForm.isCardPay = data.is_card_pay
          this.settingForm.isFacePay = data.is_face_pay
          this.settingForm.onlineFaceRoi = data.online_face_roi ? data.online_face_roi : 0.35
          this.settingForm.weighingThreshold = data.weighing_threshold ? data.weighing_threshold : 5
          this.settingForm.stabilityThreshold = data.stability_threshold ? data.stability_threshold : 3
          this.changeMealMode()
        } else {
          this.resetForm()
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    formateTimestamp(timestamp) {
      return parseInt(timestamp / 3600) + ':' + timestamp % 3600 / 60
    },
    openOtherOrg() {
      this.dialogVisible = true
      this.dialogTitle = '请选择适用的组织'
      this.dialogType = 'other'
    },
    dialogConfirm() {
      this.dialogVisible = false
    },
    otherOrgConfirm(val) {
      this.dialogVisible = false
      this.organizationIds = val
      this.checkForm('other')
    },
    // 获取周/月菜谱列表
    async getMenuList() {
      const params = {
        device_types: "ZNC",
        use_organization: [this.organizationId]
      }
      let res
      if (this.settingForm.menuListType === 'week') {
        res = await this.$apis.apiBackgroundFoodMenuWeeklyListPost(params)
      } else {
        // 获取月菜谱列表
        res = await this.$apis.apiBackgroundFoodMenuMonthlyListPost(params)
      }
      if (res.code === 0) {
        this.menuList = res.data.results
      } else {
        this.menuList = []
        this.$message.error(res.msg)
      }
    },
    // 获取当前组织餐段
    async getOrgMeal() {
      const params = {
        id: this.organizationId
      }
      const res = await this.$apis.apiBackgroundOrganizationOrganizationGetCommonSettingsPost(params)
      if (res.code === 0) {
        let mealTimeKey
        if (res.data.meal_time_settings) {
          mealTimeKey = 'meal_time_settings'
        } else {
          mealTimeKey = 'parent_meal_time_settings'
        }
        for (let key in res.data.meal_time_settings) {
          if (key.split('_')[0] === 'enable') {
            this.mealList.map(meal => {
              if (key.split('_')[1] === meal.key) {
                meal.disabled = !res.data[mealTimeKey][key]
              }
            })
          }
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    resetForm() {
      this.settingForm = {
        // 就餐场景
        mealMode: 'HF', // 场景选择
        payModel: 'unify', // 智能称重的统一支付、一菜一付
        fixedPrice: '', // 固定金额
        minute: '', // 固定金额-多少分钟内支付
        isSupportBind: 0, // 存在待支付订单是否支持绑定托盘
        // 就餐限制
        isSupportMore: -1, // 是否支持绑定多托盘：
        bindSecond: '', // 绑定间隔
        trayNum: '', // 托盘数量
        onlineLimit: -1, // 在线绑定额度限制
        onlineQuota: '', // 在线绑定额度
        offlineLimit: -1, // 离线绑定额度限制
        offlineQuota: '', // 离线绑定额度
        // 支付设置
        openCardPay: false, // 刷卡支付
        openFacePay: false, // 人脸支付
        openCodePay: false, // 扫码支付
        facePayconfirm: false, // 人脸支付是否确认
        payInfoWaitTime: 5, // 支付信息停留时间（秒）
        faceDistanceScore: 300, // 在线人脸识别距离
        // 称重消费规则
        weightConsumptionType: 'SJ', // 称重消费规则类型
        weightDiscount: '', // 每单折扣
        weightFixedPrice: '', // 每称固定金额
        // 菜谱启用
        homeMenuType: 'day', // 设备主页面菜谱
        menuListType: 'week', // 设备菜谱列表
        menuId: '', // 设备菜谱列表——周菜谱/月菜谱
        // 设备预警
        foodNotEnough: 500, // 提示菜品余量不足
        foodSellOut: 100, // 提示菜品已售罄
        balanceWarning: false, // 额度限制
        balanceWarningLimitAmount: '', // 额度限制
        getTrayBalanceWarningLimit: false, // 取餐额度限制
        getTrayBalanceWarningLimitAmount: '', // 取餐额度限制金额
        isWarningWeight: false, // 余量不足是否报警
        isWarningOut: false, // 菜品售罄是否报警
        balanceWarningLimit: false, // 余额不足是否报警
        getTrayBalanceWarning: false, // 取餐额度是否报警
        warningLights: false, // 报警灯
        warningSound: false, // 报警声
        warningTime: 0, // 报警声
        warningLightNum: 0, // 报警灯
        warningSoundNum: 0, // 报警声
        // 其他设置
        bannerTime: 0,
        startUpReset: false,
        // 自动开启离线模式
        autoOpenOfflineMode: true, // 是否自动开启离线模式
        openOfflineType: 'auto', // 根据网络状态切换
        openOfflineTime: [], // 固定时间开启
        openOfflineMeal: [], // 固定餐段开启
        // 自动上传离线订单
        autoUploadOfflineOrder: true, // 是否自动上传离线订单
        uploadOfflineType: 'auto', // 联网自动上传
        uploadOfflineTime: '', // 固定时间上传
        isAutoOpenOfflineFacePay: true, // 是否自动开启离线人脸支付
        offlineFaceRecognizeScore: 0, // 离线人脸识别分数
        detectionRange: 0, // 离线人脸识别距离（像素）
        isAutoOpenLiveDetection: true, // 离线是否开启活体检测
        liveFaceRecognizeScore: 0, // 活体检测识别分数
        autoPayOrderTime: 15,
        autoReleaseTrayTime: 10,
        isCardPay: false, // 刷卡绑定
        isFacePay: true, // 人脸绑定
        onlineFaceRoi: 0.35,
        weighingThreshold: 5,
        stabilityThreshold: 3
      }
    },
    changeOrg() {
      this.getSettingDetail()
    },
    changeType(key) {
      this.tabType = key
    },
    changeMealMode() {
      if (this.settingForm.mealMode === 'ZD' || this.settingForm.mealMode === 'SD') {
        this.settingForm.isSupportMore = -1
        this.isSupportMoreDisable = true
      } else {
        this.isSupportMoreDisable = false
      }
    },
    changeMenuList() {
      this.getMenuList()
    },
    weightConsumptionTypeChange() {
      this.$nextTick(() => {
        this.$refs.settingForm.clearValidate(['weightFixedPrice', 'weightDiscount']);
      })
    },
    changeOpenOfflineType() {
      if (this.settingForm.openOfflineType === 'on_date' || this.settingForm.openOfflineType === 'on_meal_time') {
        this.settingForm.uploadOfflineType = 'auto'
      }
      this.$nextTick(() => {
        this.$refs.settingForm.clearValidate(['openOfflineTime', 'openOfflineMeal']);
      })
    },
    changeOrganization() {
      this.resetForm()
      if (this.organizationId) {
        this.getSettingDetail()
        this.getOrgMeal()
        this.getMenuList()
      }
    }
  }
}
</script>

<style lang="scss">
.RoutineSetting{
  padding-top: 30px;
  .setting-wrap{
    margin: 0 20px;
    .tab-type{
      display: flex;
      margin: 20px 0;
      .tabItem{
        border: 1px #DAE1EB solid;
        border-radius: 15px;
        height: 30px;
        line-height: 30px;
        width: 90px;
        text-align: center;
        font-size: 14px;
        color: #7B7C80;
        margin-right: 20px;
        cursor: pointer;
      }
      .activeTab{
        color: #fff;
        background-color: #ff9b45;
        border-color: #ff9b45;
      }
    }
    .card-admin{
      display: flex;
      margin-bottom: 20px;
      &__add{
        width: 170px;
        cursor: pointer;
      }
      &-item{
        width: 200px;
        &-left{
          display: flex;
          justify-content: space-around;
          border-right: 1px #DAE1EB solid;
          padding-right: 20px;
          &-info{
            margin-left: 10px;
            &-name{
              font-size: 20px;
              font-weight: bold;
              margin-bottom: 6px;
            }
            &-cardNo{
              font-size: 14px;
              color: #b1b2b9;
            }
          }
        }
        &-btn{
          color: #fb594d;
          cursor: pointer;
          font-size: 16px;
        }
      }
      &__size__border{
        display: flex;
        justify-content: space-around;
        align-items: center;
        height: 70px;
        margin-right: 25px;
        border: 1px solid #e9e9e9;
        box-sizing: border-box;
        padding: 0 7px;
      }
    }
    .offline-title{
      display: flex;
      align-items: center;
    }
    .title{
      font-size: 16px;
      font-weight: bold;
      border-left: 5px #ff9b45 solid;
      padding: 0 10px;
      margin: 15px 0;
    }
    .form-text-size{
      font-size: 14px;
    }
    .form-label{
      font-size: 14px;
      color: #606266;
      font-weight: bold;
      margin-right: 8px;
    }
    .tips{
      margin: 0 30px;
      color: #b1b2b9;
    }
    .margin-input{
      margin: 0 5px;
    }
    .margin-r-5{
      margin-right: 5px;
    }
    .margin-l-20{
      margin-left: 20px;
    }
    .margin-r-20{
      margin-right: 20px;
    }
    .margin-l-30{
      margin-left: 30px;
    }
    .margin-r-30{
      margin-right: 30px;
    }
    .inline-box{
      display: inline-block;
    }
    .form-content-inline{
      display: inline-block;
      .el-form-item__content{
        display: inline-block;
      }
    }
    .el-input-number__decrease:hover:not(.is-disabled)~.el-input .el-input__inner:not(.is-disabled), .el-input-number__increase:hover:not(.is-disabled)~.el-input .el-input__inner:not(.is-disabled){
      border-color: #ff9b45;
    }
    .el-input.is-active .el-input__inner, .el-input__inner:focus{
      border-color: #ff9b45;
    }
    .el-input-number__decrease:hover, .el-input-number__increase:hover{
      color: #ff9b45;
    }
    .detection-range-slider {
      .el-slider__bar{
        background-color: #ff9b45;
      }
      .el-slider__button{
        border: 2px solid #ff9b45;
      }
    }
  }
  .font-size-15{
    font-size: 15px;
  }
}
</style>
