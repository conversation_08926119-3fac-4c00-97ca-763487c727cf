export const MEALTIME_SETTING = {
  tooltip: {
    trigger: 'item',
    borderColor: '#FCA155',
    textStyle: {
      color: '#000',
      fontWeight: 500
    },
    backgroundColor: '#fff',
    extraCssText: 'box-shadow: 0 0 10px rgba(0,0,0,0.2);font-weight: 540;',
    formatter: function(params) {
      console.log('tooltip的params', params)
      let marker = params.marker
      let percent = params.percent
      return marker + params.name + '&nbsp;&nbsp;&nbsp;' + percent + '%'
    }
  },
  title: {
    text: '0',
    x: 'center',
    y: 'center',
    top: '25%',
    textStyle: {
      color: '#fd953c',
      fontSize: 18
    }
  },
  legend: {
    top: '62%',
    // bottom: '30%',
    // left: '30%',
    // x: 'left',
    // formatter: function(label) {
    //   return label
    // },
    // icon: 'circle',
    orient: 'vertical',
    y: 'bottom',
    // itemWidth: 8, // 设置宽度
    // itemHeight: 8 // 设置高度
    padding: [
      0, // 上
      0, // 右
      0, // 下
      0 // 左
    ]
    // textStyle: {
    //   color: 'red',
    //   width: 100,
    //   overflow: 'truncate'
    // }
    // itemWidth: 50
  },

  series: [
    {
      // center: ['15%', '50%'],
      type: 'pie',
      radius: ['45%', '60%'],
      avoidLabelOverlap: false,
      top: '-10%',
      height: 200,
      itemStyle: {
        borderRadius: 10,
        borderColor: '#f1f2f5',
        borderWidth: 3
      },
      hoverAnimation: false,
      label: {
        show: false,
        position: 'center'
        // normal: {
        //   formatter: '{{b},{d}%}'
        // }
      },
      emphasis: {
        label: {
          show: false,
          fontSize: '20',
          fontWeight: 'bold'
        }
      },
      labelLine: {
        show: false
      },
      data: []
    }
  ],
  color: ['#07DED0', '#FE985F', '#e98397', '#F97C95', '#58AFFE', '#F8C345']
}

export const JOB_LIST = [
  {
    label: '学生',
    value: 'student',
    status: false,
    type: '0'
  },
  {
    label: '在职教学人员',
    value: 'teacher',
    status: false,
    type: '1'
  },
  {
    label: '军人',
    value: 'soldier',
    status: false,
    type: '2'
  },
  {
    label: '国家机关、党群组织、企业、事业单位负责人',
    value: 'org_leaders',
    status: false,
    type: '3'
  },
  {
    label: '其他专业技术和管理人员',
    value: 'manage',
    status: false,
    type: '4'
  },
  {
    label: '商业、服务业人员',
    value: 'service',
    status: false,
    type: '5'
  },
  {
    label: '办事人员和有关人员（含公务员）',
    value: 'clerks',
    status: false,
    type: '6'
  },
  {
    label: '农、林、牧、渔、水利生产人员',
    value: 'farmers',
    status: false,
    type: '7'
  },
  {
    label: '生产、运输设备操作人员及其有关人员',
    value: 'production',
    status: false,
    type: '8'
  },
  {
    label: '不便分类的其他从业人员',
    value: 'other',
    status: false,
    type: '9'
  },
  {
    label: '待业人员',
    value: 'unemployed',
    status: false,
    type: '10'
  }
]
