<template>
  <div class="right-function">
    <div class="expand">
      <div class="title">拓展功能</div>
      <div class="expand-all" v-loading="isLoading">
        <div v-for="(item, index) in expand" :key="index" :class="['item', item.disabled ? 'is-disabled' : '']"
          @click="clickHandle(item)">
          <img :src="item.src" alt="" />
          <div class="name">{{ item.label }}</div>
        </div>
      </div>
    </div>
    <!-- <div class="banner-img">
      <img src="@/assets/img/banner.png" alt="" />
    </div> -->
    <!-- banner start -->
    <el-carousel trigger="click" height="150px" class="banner m-t-30 m-b-30">
      <el-carousel-item v-for="(item, index) in bannerList" :key="index">
        <div class="banner-img" @click="bannerHandle(item)"><img :src="item.img_url" alt="" /></div>
      </el-carousel-item>
    </el-carousel>
    <!-- banner end -->
    <div class="notice" v-if="noticeList && noticeList.length > 0">
      <!-- <div class="title">消息公告</div> -->
      <div class="notice-item" v-for="(item,index) in noticeList" :key="index" @click="getMessagesDetails(item)">
        <div class="content line-1">
          {{ item.title }}
          <!-- <div class="count">{{ item.id }}</div> -->
        </div>
        <div class="flex check">
          <div class="notice-time">{{ item.post_time }}</div>
          <div class="notice-check" >
            <span>查看详情</span>
            <i class="el-icon-arrow-right"></i>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, to, getSessionStorage } from '@/utils'
import { EXPAND, ULR_DO_BEI_SYSTEM_DEV, ULR_DO_BEI_SYSTEM_STAGING, ULR_DO_BEI_SHOP_DEV, ULR_DO_BEI_SHOP_STAGING, URL_WASHING_SYSTEM_DEV, URL_WASHING_SYSTEM_STAGING } from './home'
import { URL_MANUFACTURER, URL_MANUFACTURER_STAGING } from '@/views/merchant/user-center/constants/cardManageConstants'
import { formateDate } from '@/utils/date'
export default {
  name: 'RightFunction',
  components: {},
  props: {
    orgid: [Number, String, Array]
  },
  data() {
    return {
      expand: EXPAND,
      noticeList: [],
      isLoading: false,
      bannerList: []
    }
  },
  created() {
    this.initLoad()
  },
  mounted() { },
  methods: {
    initLoad() {
      this.getThirdLogin()
      this.getBannerList()
      this.getMsgList()
    },
    // 节下流咯
    searchHandle: debounce(function () {
    }, 300),
    // 获取第三方登录
    async getThirdLogin() {
      console.log("getThirdLogin");
      this.isLoading = true
      var organization = this.$store.getters.organization || {}
      console.log("userInfo", organization);
      const [err, res] = await to(this.$apis.apiBackgroundOrganizationOrganizationGetOpenThirdPermissionsPost({ id: organization }))
      this.isLoading = false
      if (err) {
        return
      }
      if (res.code === 0) {
        var resultList = res.data || []
        // 获取后台的数据跟我们写的列表对比，有的我们就放开可以跳转
        this.expand = this.expand.map(item => {
          var type = item.type || ''
          var hasPermissItem
          if (type) {
            hasPermissItem = resultList.find(subItem => {
              return subItem.name === type
            })
            // 存在权限
            if (hasPermissItem) {
              item.extra = hasPermissItem.extra || {}
              item.disabled = false
            }
          }
          return item
        })
        // 排个序 disabled 为false 的排前面
        if (this.expand) {
          this.expand = this.expand.sort((a, b) => {
            return !a.disabled ? -1 : (!b.disabled ? 1 : 0)
          })
        }
        console.log("this.expand", this.expand);
      } else {
        // this.$message.error(res.msg)
      }
    },
    // 获取轮播图
    async getBannerList() {
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundOrganizationBannerGetOrgBannerListPost({
        org_id: +getSessionStorage('organization')
      }))
      this.isLoading = false
      if (err) {
        return
      }
      if (res.code === 0) {
        this.bannerList = res.data
      } else {
        // this.$message.error(res.msg)
      }
    },
    clickHandle(data) {
      console.log("data", data);
      var url = process.env.NODE_ENV === 'development' ? URL_MANUFACTURER : URL_MANUFACTURER_STAGING
      if (!data.disabled) {
        // window.location.href = data.url
        if (data.id === 6) {
          // 车辆管理如果要免登需要先获取token
          this.getCarToken(data.extra, url)
        } else if (Reflect.has(data, 'type') && data.type === 'DoBay') {
          // 东贝系统跳转
          this.goToDoBeiSystem(data)
        } else if (data.id === 9) {
          // 对接中卡洗衣
          url = process.env.NODE_ENV === 'development' ? URL_WASHING_SYSTEM_DEV : URL_WASHING_SYSTEM_STAGING
          console.log('url', url);
          this.getWashingSystemToken(data.extra, url)
        } else if (Reflect.has(data, 'type') && data.type === 'YDZHXY') {
          // 智慧校园
          window.open(data.extra ? data.extra.login_url : '', '_blank')
        } else {
          window.open(data.url ? data.url : url, '_blank')
        }
      } else {
        this.$message.error('功能正在开发中！')
      }
    },
    /***
     * 获取第三方车辆管理token
     */
    async getCarToken(data, urlPath) {
      var params = {
        project_no: data.project_no,
        app_secret: data.app_secret,
        appid: data.appid
      }
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundCarTravelCarTravelInfoGenerateRedirectTokenPost(params))
      this.isLoading = false
      if (err) {
        window.open(urlPath + 'login', '_blank')
        return
      }
      if (res.code === 0) {
        var resultData = res.data || {}
        console.log("data", resultData);
        if (Reflect.has(resultData, "data") && Reflect.has(resultData.data, 'data') && resultData.data.data !== null && Reflect.has(resultData.data.data, 'token')) {
          var newPath = urlPath + "parkingLot/homePage?token=" + resultData.data.data.token
          window.open(newPath, '_blank')
        } else {
          window.open(urlPath + 'login', '_blank')
        }
      } else {
        window.open(urlPath + 'login', '_blank')
      }
    },
    bannerHandle(item) {
      if (!item.jump_url) return
      window.open(item.jump_url, '_blank')
    },
    /**
     * 跳转东贝系统
     * @param {} data
     */
    async goToDoBeiSystem(data) {
      var label = data.label || ''
      var urlManage = process.env.NODE_ENV === 'development' ? ULR_DO_BEI_SYSTEM_DEV : ULR_DO_BEI_SYSTEM_STAGING
      var urlShop = process.env.NODE_ENV === 'development' ? ULR_DO_BEI_SHOP_DEV : ULR_DO_BEI_SHOP_STAGING
      var returnUrl = ''
      var message = "获取第三方授权信息失败"
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundDobayGetLoginUrlPost({ login_type: label === '督贝管理' ? '1' : '2' }))
      console.log("err", err, res)
      this.isLoading = false
      if (Reflect.has(res, "code") && res.code === 0) {
        returnUrl = res.data || urlManage
      } else if (Reflect.has(res, "msg") && res.msg) {
        message = res.msg || message
        this.$message.error(message)
      }
      switch (label) {
        case "督贝管理":
          urlManage = returnUrl || urlManage
          window.open(urlManage, '_blank')
          break;
        case "智慧门店":
          urlShop = returnUrl || urlShop
          window.open(urlShop, '_blank')
          break;
        default:
          break;
      }
    },
    // 获取公告列表
    async getMsgList() {
      this.isLoading = true
      var end = new Date()
      var start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
      start = formateDate(start) + " 00:00"
      end = formateDate(end) + " 23:59"
      const [err, res] = await to(this.$apis.apiBackgroundMessagesMessagesGetMsgListPost({
        page: 1,
        page_size: 9999,
        read_flag: false,
        end_time: end,
        start_time: start
      }))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res && res.code === 0) {
        var data = res.data || {}
        this.noticeList = data.results || []
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取公告详情
    async getMessagesDetails(data) {
      var flag = await this.changeMsgStatus(data.msg_no)
      if (flag) {
        this.$router.push({
          name: 'MerchantNoticeDetail',
          query: {
            type: 'list',
            msg_no: data.msg_no
          }
        })
      }
    },
    // 改变公告状态
    async changeMsgStatus(id) {
      return new Promise((resolve) => {
        this.$apis.apiBackgroundMessagesMessagesBulkMsgReadPost({
          msg_nos: [id]
        }).then(res => {
          if (res.code === 0) {
            resolve(true)
          } else {
            this.$message.error('已读失败')
            resolve(false)
          }
        }).catch(err => {
          console.log("err", err);
          this.$message.error('已读失败')
          resolve(false)
        })
      })
    },
    /**
     * 获取第三方中卡洗衣对接token
     * @param {*} data
     * @param {*} urlPath
     */
    async getWashingSystemToken(data, urlPath) {
      var url = urlPath
      // 重新确认Url
      if (Reflect.has(data, "login_url") && data.login_url !== null && data.login_url !== '') {
        url = data.login_url
        console.log("url,", data.login_url);
      }
      // 由于对方系统没有免登的功能，所以改为直接跳转他们的URL，优先义后台配置的为准
      window.open(url, '_blank')
    }
  }
}
</script>

<style lang="scss" scoped>
.flex {
  display: flex;
}

.right-function {
  // position: fixed;
  width: 300px;
  height: 100%;

  .expand {
    background-color: #fff;
    border-radius: 10px;
    padding: 20px;
    max-height: 32%;
    overflow: hidden;
    overflow-y: auto;
    &::-webkit-scrollbar {
      display: none;
    }

    .title {
      color: #000;
      font-size: 16px;
      font-weight: bold;
    }

    .expand-all {
      .item {
        display: inline-block;
        align-content: center;
        margin: 6% 11.5% 0 0;
        cursor: pointer;
        flex-wrap: wrap !important;

        &:nth-child(3n) {
          margin-right: 0;
        }

        img {
          width: 50px;
          height: 50px;
          margin-left: 5px;
        }
      }

      .is-disabled {
        opacity: .5;
      }
    }
  }
  .banner {
    border-radius: 10px;
    .banner-img {
      width: 100%;
      height:100%;
      cursor: pointer;
      overflow:hidden;
      position: relative;
      img{
        width: 100%;
        height:100%;
        object-fit: cover;
        vertical-align:middle;
      }
    }
  }
  ::v-deep .banner .el-carousel__indicators .el-carousel__button{
    width: 20px!important;
  }
  .notice {
    background-color: #fff;
    border-radius: 10px;
    padding: 10px 20px;
    max-height: 41.5%;
    font-size: 16px;
    overflow: hidden;
    overflow-y: scroll;
    &::-webkit-scrollbar {
      display: none;
    }
    .title {
      color: #000;
      font-size: 16px;
      font-weight: bold;
    }

    .notice-item {
      margin-top: 5%;
      color: #999;
      cursor: pointer;
      .content {
        color: #000;
        .count {
          color: #fe943c;
          width: 21px;
          height: 21px;
          line-height: 22px;
          border-radius: 21px;
          text-align: center;
          margin-right: 5px;
          background-color: #f9e4d4;
        }

        .news {
          width: 240px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }

      .check {
        margin-top: 7px;
        justify-content: space-between;
        align-items: center;

        .notice-check,
        .notice-time {
          font-size: 14px;
        }

        .notice-check {
          cursor: pointer;

          .el-icon-arrow-right {
            font-size: 12px;
          }
        }
      }
    }
  }
}

@media screen and (max-height: 900px) {
  .right-function {
    .expand {
      img {
        width: 40px !important;
        height: 40px !important;
      }

      .name {
        font-size: 14px !important;
      }
    }
  }
}
.line-1 {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
</style>
