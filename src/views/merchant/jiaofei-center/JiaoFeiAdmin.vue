<template>
  <div class="JiaoFeiAdmin container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form ref="searchRef" @search="searchHandle" :form-setting="searchFormSetting" :autoSearch="false"></search-form>

    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" @click="showProofDialog">批量开凭证</button-icon>
          <button-icon color="origin" type="add" @click="gotoAddOrEdit('add')" v-permission="['background_jiaofei.jiao_fei_setting.add']">添加缴费</button-icon>
          <button-icon color="plain" type="export" @click="handleExport" v-permission="['background_jiaofei.jiaofei_setting.list_export']">导出Excel</button-icon>
        </div>
      </div>
      <!-- table-content start -->
      <div class="table-content">
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
        >
          <el-table-column prop="name" label="缴费名称" align="center"></el-table-column>
          <el-table-column prop="jiaofei_type_alias" label="缴费类别" align="center"></el-table-column>
          <el-table-column prop="jiaofei_fee" label="应缴总金额" align="center"></el-table-column>
          <el-table-column prop="user_group_name" label="已缴/应缴" align="center">
            <template slot-scope="scope">
              <span>{{scope.row.jiaofei_count}}人/{{scope.row.total_jiaofei_count}}人</span>
            </template></el-table-column>
          <el-table-column prop="total_jiaofei_fee" label="实收总金额" align="center"></el-table-column>
          <el-table-column prop="operator_name" label="创建人" align="center"></el-table-column>
          <el-table-column prop="create_time" label="创建时间" align="center"></el-table-column>
          <el-table-column prop="source_alias" label="缴费时间" align="center" width="200">
            <template slot-scope="scope">
              <div>开始：{{scope.row.start_time}}</div>
              <div>截止：{{scope.row.end_time}}</div>
            </template>
          </el-table-column>
          <el-table-column prop="source_alias" label="允许退款" align="center">
            <template slot-scope="scope">
              <span>{{scope.row.can_refund?'是':'否'}}</span>
            </template>
          </el-table-column>
          <el-table-column prop="jiaofei_progress_alias" label="进度" align="center"></el-table-column>
          <el-table-column prop="jiaofei_status" label="状态" align="center" width="90">
            <template slot-scope="scope">
              <el-switch v-model="scope.row.jiaofei_status" active-color="#ff9b45" inactive-color="#ffcda2" @change="mulOperation('status', scope.row)" :disabled="!allPermissions.includes('background_jiaofei.jiaofei_setting.change_status')"></el-switch>
              <span>{{scope.row.jiaofei_status?'启用':'停用'}}</span>
            </template>
          </el-table-column>
          <el-table-column prop="" label="是否开票" align="center"></el-table-column>
          <el-table-column fixed="right" label="操作" align="center" width="150">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                class="ps-bule"
                v-show="scope.row.jiaofei_progress !== 'finish'"
                @click="gotoAddOrEdit('edit', scope.row.id)"
                v-permission="['background_jiaofei.jiaofei_setting.modify']"
                >编辑</el-button>
              <el-button
                type="text"
                size="small"
                class="ps-text"
                @click="gotoJiaoFeiDetail(scope.row)"
                v-permission="['background_jiaofei.jiaofei_setting.jiaofei_detail_list']"
                >缴费明细</el-button>
              <el-button
                type="text"
                size="small"
                @click="showProofDialog(scope.row)"
                >开凭证</el-button>
              <el-button
                type="text"
                size="small"
                class="ps-red"
                v-show="scope.row.jiaofei_progress === 'no_start'"
                @click="mulOperation('del', scope.row)"
                v-permission="['background_jiaofei.jiaofei_setting.delete']"
                >删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!-- table content end -->
      <!-- 分页 start -->
      <pagination
        :onPaginationChange="onPaginationChange"
        :current-page.sync="currentPage"
        :page-size.sync="pageSize"
        :layout="'total, prev, pager, next, jumper'"
        :total="totalCount"
      ></pagination>
      <!-- 分页 end -->
    </div>

    <div class="ps-el-drawer">
      <el-drawer
        title="同步"
        :visible="proofDialogVisible"
        :show-close="false"
        size="40%">
        <div class="p-20">
          <el-form ref="proofFormRef" :model="proofForm" label-width="auto" label-position="right">
            <el-form-item label="开票人">
              <el-input v-model="proofForm.name" placeholder="请输入开票人姓名" class="w-250"></el-input>
            </el-form-item>
            <el-form-item>
              <el-checkbox v-model="proofForm.isNeedSignet">电子印章</el-checkbox>
            </el-form-item>
            <el-form-item v-show="proofForm.isNeedSignet" label="印章名称">
              <el-input v-model="proofForm.signetName" placeholder="请输入印章名称" class="w-250"></el-input>
            </el-form-item>
          </el-form>
          <div class="ps-el-drawer-footer">
            <el-button size="small" type="plain" class="w-100" @click="proofDialogVisible = false">关闭</el-button>
            <el-button size="small" type="primary" class="w-100" @click="proofDialogVisible = false">同步</el-button>
          </div>
        </div>
      </el-drawer>
    </div>
  </div>
</template>

<script>
import { divide, debounce } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import { mapGetters } from 'vuex'

export default {
  name: 'JiaoFeiAdmin',
  mixins: [exportExcel],
  computed: {
    ...mapGetters(['allPermissions'])
  },
  data() {
    return {
      isLoading: false,
      tableData: [],
      currentPage: 1,
      pageSize: 10,
      totalCount: 0,
      searchFormSetting: {
        select_date: {
          type: 'daterange',
          label: '创建时间',
          value: [],
          clearable: true
        },
        jiaofei_name: {
          type: 'input',
          label: '缴费名称',
          value: '',
          maxlength: 20,
          placeholder: '请输入缴费名称'
        },
        jiaofei_type: {
          type: 'select',
          label: '缴费类别',
          value: '',
          clearable: true,
          placeholder: '请选择缴费类别',
          listNameKey: 'name',
          listValueKey: 'key',
          dataList: []
        }
      },
      proofDialogVisible: false,
      proofForm: {
        name: '',
        isNeedSignet: false,
        signetName: ''
      }
    }
  },
  mounted() {
    this.initLoad()
  },
  methods: {
    async initLoad() {
      if (this.$route.query.name) {
        this.searchFormSetting.jiaofei_name.value = this.$route.query.name
      }
      this.getJiaoFeiList()
      this.getJiaoFeiType()
    },
    // 刷新页面
    refreshHandle() {
      this.$refs.searchRef.resetForm()
      this.currentPage = 1;
      this.tableData = []
      this.initLoad()
    },
    // 节下流咯
    searchHandle: debounce(function(e) {
      if (e && e === 'search') {
        this.currentPage = 1;
        this.getJiaoFeiList()
      }
    }, 300),
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_date') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 获取列表数据
    async getJiaoFeiList() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundJiaofeiJiaofeiSettingListPost({
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      })
      this.isLoading = false
      if (res.code === 0) {
        this.tableData = res.data.results
        this.tableData.map(item => {
          item.jiaofei_fee = divide(item.jiaofei_fee)
          item.total_jiaofei_fee = divide(item.total_jiaofei_fee)
        })
        this.totalCount = res.data.count
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getJiaoFeiList()
    },
    // 操作提示
    mulOperation(type, data) {
      console.log(data.jiaofei_status)
      let title = '提示'
      let content = ''
      switch (type) {
        case 'del':
          content = '确定删除该缴费数据吗？'
          break;
        case 'status':
          if (data.jiaofei_status) {
            content = '确定启用该规则？'
          } else {
            content = '确定停用该规则？'
          }
          break;
      }
      this.$confirm(`${content}`, `${title}`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            let params = {}
            switch (type) {
              case 'del':
                params.ids = [data.id]
                this.delJiaoFei(params)
                break;
              case 'status':
                params.id = data.id
                params.jiaofei_status = data.jiaofei_status
                this.changeStatus(params)
                break;
            }
            done()
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {
          this.getJiaoFeiList()
        })
    },
    async delJiaoFei(params) {
      const res = await this.$apis.apiBackgroundJiaofeiJiaofeiSettingDeletePost(params)
      if (res.code === 0) {
        this.$message.success('删除成功')
        this.getJiaoFeiList()
      } else {
        this.$message.error(res.msg)
      }
    },
    async changeStatus(params) {
      const res = await this.$apis.apiBackgroundJiaofeiJiaofeiSettingChangeStatusPost(params)
      if (res.code === 0) {
        this.$message.success(params.jiaofei_status ? '启用成功' : '停用成功')
        this.getJiaoFeiList()
      } else {
        this.$message.error(res.msg)
      }
    },
    async getJiaoFeiType() {
      const res = await this.$apis.apiBackgroundBaseMenuGetJiaofeiTypePost()
      if (res.code === 0) {
        let jiaofeiTypeList = []
        for (let key in res.data) {
          jiaofeiTypeList.push({
            key,
            name: res.data[key]
          })
        }
        this.searchFormSetting.jiaofei_type.dataList = jiaofeiTypeList
      } else {
        this.$message.error(res.msg)
      }
    },
    gotoAddOrEdit(type, id) {
      let query = {}
      if (type === 'edit') {
        query = { id }
      }
      this.$router.push({
        name: 'MerchantAddOrEditJiaoFei',
        params: {
          type
        },
        query
      })
    },
    gotoJiaoFeiDetail(data) {
      this.$router.push({
        name: 'MerchantJiaoFeiDetail',
        query: {
          id: data.id,
          progress: data.jiaofei_progress
        }
      })
    },
    handleExport() {
      const option = {
        type: "ExportJiaoFeiList",
        params: {
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        }
      }
      this.exportHandle(option)
    },
    showProofDialog(data) {
      this.proofDialogVisible = true
    }
  }
}
</script>
