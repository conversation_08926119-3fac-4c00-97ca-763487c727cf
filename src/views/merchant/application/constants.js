export const defaultPathList = [
  // 用户中心
  {
    value: '用户列表',
    key: 'card_service.card_user.list',
    name: 'MerchantUseCenter'
  },
  {
    value: '用户部门',
    key: 'card_service.card_department_group',
    name: 'MerchantUserDepartment'
  },
  {
    value: '用户分组',
    key: 'card_service.card_user_group',
    name: 'MerchantUserGroup'
  },
  {
    value: '挂失卡管理',
    key: 'card_service.card_user.card_loss_list',
    name: 'MerchantCardLossAdmin'
  },
  {
    value: '信息收集',
    key: 'card_service.user_collect.list',
    name: 'MerchantInformationCollection'
  },
  {
    value: '自注册审批',
    key: 'approve_register',
    name: 'MerchantAutoRegisterApprove'
  },
  {
    value: '账户列表',
    key: 'card_service.card_user.account_list',
    name: 'MerchantUserAccountList'
  },
  {
    value: '账户设置',
    key: 'card_service.card_user_group.get_org_flat_and_patch_cost_settings',
    name: 'MerchantUserAccountSetting'
  },
  {
    value: '透支设置',
    key: 'card_service.card_user_group.get_org_debt_settings',
    name: 'MerchantOverdraftSetting'
  },
  {
    value: '透支明细',
    key: 'card_service.card_user_group.debt_order_report',
    name: 'MerchantOverdraftDetail'
  },
  {
    value: '退户管理',
    key: 'card_service.card_user.person_quit_list',
    name: 'MerchantWithdrawalAdmin'
  },
  {
    value: '工本费列表',
    key: 'card_service.flat_cost.list',
    name: 'MerchantFlatCostList'
  },
  {
    value: '补卡费列表',
    key: 'card_service.flat_cost.supplementary_list',
    name: 'MerchantRepairCardList'
  },
  {
    value: '卡操作历史',
    key: 'card_service.flat_cost.card_operation_list',
    name: 'MerchantCardOperationHistoryList'
  },
  {
    value: '补贴发放规则',
    key: 'card_service.card_subsidy.list',
    name: 'MerchantCardSubsidy'
  },
  {
    value: '用户公告',
    key: 'background_marketing.marketing_notice.list',
    name: 'MerchantMobileNotice'
  },
  {
    value: '商城水控用户绑定',
    key: 'card_service.third_card_user.sk_list',
    name: 'UserBandingWater'
  },
  {
    value: '农行客户号绑定',
    key: 'card_service.third_card_user.client_number_list',
    name: 'BindCustomer'
  },
  {
    value: '农行校园用户',
    key: 'background_ebank.abc_school_user.list',
    name: 'BankSchoolUser'
  },
  {
    value: '账户操作日志',
    key: 'card_service.card_log.get_card_operation_log_list',
    name: 'MerchantCardLogList'
  },
  // 门禁考勤
  {
    value: '考勤组管理',
    key: 'background_attendance.group',
    name: 'MerchantAttendanceGroupManage'
  },
  {
    value: '考勤设置',
    key: 'background_attendance.settings.list',
    name: 'MerchantAttendanceSetting'
  },
  {
    value: '个人考勤统计',
    key: 'background_attendance.attendance_record_details.card_user_punch_status_count',
    name: 'MerchantPersonAttendanceReport'
  },
  {
    value: '考勤记录',
    key: 'background_attendance.attendance_record.list',
    name: 'MerchantAttendanceRecordDetail'
  },
  {
    value: '考勤明细',
    key: 'background_attendance.record_details.list',
    name: 'MerchantAttendanceRecord'
  },
  {
    value: '缺卡记录',
    key: 'background_attendance.absence_work_record_details.list',
    name: 'MerchantMissCardRecord'
  },
  {
    value: '补卡审批',
    key: 'background_attendance.attendance_supplement_record',
    name: ''
  },
  {
    value: '补卡规则',
    key: 'background_attendance.attendance_supplement_rule.list',
    name: ''
  },
  {
    value: '门禁设置',
    key: 'background_attendance.access_control_settings.list',
    name: 'MerchantAccessControlSetting'
  },
  {
    value: '通行统计',
    key: 'background_attendance.access_control_record.org_punch_status_count',
    name: 'MerchantThroughStatistics'
  },
  {
    value: '通行记录',
    key: 'background_attendance.access_control_record.list',
    name: 'MerchantThroughRecord'
  },
  {
    value: '请假规则',
    key: 'background_attendance.attendance_for_leave_rule.list',
    name: ''
  },
  {
    value: '请假审批',
    key: 'background_attendance.attendance_for_leave_record',
    name: ''
  },
  // 进销存
  {
    key: "background_drp.warehouse.list",
    value: "仓库管理",
    name: 'WarehouseAdmin'
  },
  {
    key: "background_drp.unit_management.list",
    value: "单位管理",
    name: 'InventoryUnitAdmin'
  },
  {
    key: "background_drp.materials.list",
    value: "物资库",
    name: 'MaterialWarehouse'
  },
  {
    key: "background_drp.purchase_info.details_list",
    value: "采购清单明细",
    name: 'PurchaseListReport'
  },
  {
    key: "background_drp.approve",
    value: "采购审批",
    name: 'ApprovalManagementList'
  },
  {
    key: "background_drp.subscribe_info.list",
    value: "申购单列表",
    name: 'SubscriptionOrderList'
  },
  {
    key: "background_drp.subscribe_info.list_1",
    value: "申购记录",
    name: 'SubscriptionRecordList'
  },
  // 系统管理
  {
    key: "background_organization.organization.list",
    value: "组织架构",
    name: 'MerchantOrganizationList'
  },
  {
    key: "background_organization.role.list",
    value: "角色管理",
    name: 'MerchantRoleList'
  },
  {
    key: "background_organization.account.list",
    value: "账号管理",
    name: 'MerchantAccountList'
  },
  {
    key: "background_organization.organization.get_common_settings",
    value: "常规设置",
    name: 'MerchantGeneralSettings'
  },
  {
    key: "background_approve.approve_rule.list",
    value: "审批规则",
    name: 'MerchantApproveRulesList'
  },
  {
    key: "background_organization.app_permission",
    value: "移动端管理",
    name: 'MerchantModuleAdmin'
  },
  {
    key: "background_organization.report_download.list",
    value: "下载中心",
    name: ''
  },
  {
    key: "background_messages.messages.list",
    value: "通知公告",
    name: 'MerchantNoticeAdmin1'
  },
  {
    key: "background_messages.dingtalk_message_setting",
    value: "钉钉消息配置",
    name: 'DingTalkMessageSetting1'
  },
  {
    key: "background.log.list",
    value: "操作日志",
    name: 'MerchantLogList'
  },
  // 其他
  {
    key: "background_car_travel.car_travel_info.list",
    value: "车辆绑定",
    name: 'MerchantCarBinding'
  },
  {
    key: "background_car_travel.car_pass_record.list",
    value: "通行记录",
    name: 'MerchantTrafficRecords'
  },
  {
    key: "background_car_travel.order_pass_record.list",
    value: "通行订单",
    name: 'MerchantTrafficOrders'
  },
  {
    key: "background_datascreen.kanban_temp.list",
    value: "数据大屏",
    name: 'MerchantDataBoardAdmin'
  },
  // 订单管理
  {
    key: "background_order.order_payment",
    value: "消费订单",
    name: 'MerchantConsumption'
  },
  {
    key: "background_order.vending_machine.order",
    value: "售货柜订单",
    name: 'MerchantConsumptionAutoSelling'
  },
  {
    key: "background_order.order_refund.list",
    value: "退款订单",
    name: 'MerchantRefundOrder'
  },
  {
    key: "background_order.order_charge",
    value: "充值订单",
    name: 'MerchantRechargeOrder'
  },
  {
    key: "background_order.order_offline.list",
    value: "异常订单",
    name: 'MerchantConsumptionFailure'
  },
  {
    key: "background_order.order_withdraw.list",
    value: "提现订单",
    name: 'MerchantWithdrawOrder'
  },
  {
    key: "background_operation_management.order_evaluation",
    value: "订单评价",
    name: 'MerchantOperationsManagementEvaluateList'
  },
  {
    key: "background_report_center.data_report.third_order_list",
    value: "第三方对账异常订单",
    name: ''
  },
  {
    key: "background_order.order_appeal.list",
    value: "申述审批",
    name: 'MerchantAppealOrder'
  },
  {
    key: "background_order.order_review.list",
    value: "取消订单审批",
    name: 'MerchantApproveOrder'
  },
  {
    key: "background_order.order_charge.charge_appeal_list",
    value: "充值退款审批",
    name: 'MerchantRechargeRefundOrder'
  },
  {
    key: "background_order.order_withdraw.approval_list",
    value: "提现审批",
    name: 'MerchantRechargeWithdrawOrder'
  },
  {
    key: "background_approve.approve_order_visitor",
    value: "访客就餐审批",
    name: 'MerchantMealApproveList'
  },
  // 用餐管理
  {
    key: "background_food.menu",
    value: "菜谱管理",
    name: 'MerchantRecipesManage'
  },
  {
    key: "background_fund_supervision.ration_recipe.list",
    value: "带量食谱",
    name: 'MerchantRationRecipe'
  },
  {
    key: "background_food.food.intelligent_food_list",
    value: "智能菜品设置",
    name: ''
  },
  {
    key: "background_food.menu_template.list",
    value: "菜谱模板管理",
    name: 'MerchantRecipeTemplateAdmin'
  },
  {
    key: "background_food.food.food_evaluation_list",
    value: "菜品评分排行",
    name: 'MerchantDishRatingsList'
  },
  {
    key: "background_food.ingredient.list",
    value: "食材库",
    name: 'MerchantIngredientsAdmin'
  },
  {
    key: "background_food.food_sort.list",
    value: "菜品.商品分类",
    name: 'MerchantMealFoodClassification'
  },
  {
    key: "background_food.food.list",
    value: "菜品.商品库",
    name: 'MerchantMealFoodList'
  },
  {
    key: "background_food.set_meal.list",
    value: "套餐管理",
    name: 'MerchantSetMealAdmin'
  },
  {
    key: "background_food.intent_food.list",
    value: "意向菜谱排行",
    name: 'MerchantIntentionMenu'
  },
  {
    key: "background_food.food_reserved.list",
    value: "菜品留样记录",
    name: 'MerchantAiRetentionInstrument'
  },
  {
    key: "background_food.menu_setting_info.list",
    value: "菜谱规则设置",
    name: 'MenuRuleSetting'
  },
  // {
  //   key: "background_tablecode_rsv.canteen_settings.get_orgs_settings_list",
  //   value: "堂食设置",
  //   name: ''
  // },
  // {
  //   key: "background_tablecode_rsv.table.list",
  //   value: "包厢/桌台",
  //   name: ''
  // },
  {
    key: "background_reservation.background_reservation_settings.list",
    value: "预约规则",
    name: 'MerchantBookingMeal'
  },
  {
    key: "background_order.reservation_order.info_list",
    value: "预约点餐明细",
    name: 'MerchantBookingOrderList'
  },
  {
    key: "background_order.reservation_order.collect_list",
    value: "预约点餐汇总",
    name: 'MerchantCanteenBooking'
  },
  {
    key: "background_order.reservation_order.group_collect_list",
    value: "分组点餐汇总",
    name: 'MerchantGroupOrder'
  },
  {
    key: "background_order.reservation_order.food_collect_list",
    value: "菜品汇总",
    name: 'MerchantCategoryList'
  },
  {
    key: "background_order.reservation_order.set_meal_collect_list",
    value: "套餐汇总",
    name: 'MerchantSetMealSummary'
  },
  {
    key: "background_order.reservation_order.cupboard_order_list",
    value: "取餐柜订单",
    name: 'MerchantCupboardOrder'
  },
  {
    key: "background_order.reservation_order.delivery_list",
    value: "配送汇总表",
    name: 'MerchantDeliverReport'
  },
  {
    key: "address.adders_area.list",
    value: "配送区域",
    name: 'MerchantAddressAreaAdmin'
  },
  {
    key: "address.adders_center.list",
    value: "配送地址",
    name: 'MerchantAddressAdmin'
  },
  {
    key: "background_organization.qrcode_template",
    value: "地址码模板",
    name: 'MerchantQrcodeTemplate'
  },
  {
    key: "background_report_meal.report_meal_settings.list",
    value: "报餐规则",
    name: 'MerchantMealReportAdmin'
  },
  {
    key: "background_report_meal.report_meal_pack_settings.list",
    value: "餐包规则",
    name: 'MerchantMealPackageRule'
  },
  {
    key: "background_order.order_report_meal.not_report_list",
    value: "未报餐人员统计",
    name: 'MerchantHeadCount'
  },
  {
    key: "background_order.order_report_meal.info_list",
    value: "报餐点餐明细",
    name: 'MerchantMealReportDetail'
  },
  {
    key: "background_order.order_report_meal.order_report_meal_pack_list",
    value: "餐包点餐明细",
    name: 'MerchantMealPackageDetail'
  },
  {
    key: "background_order.order_report_meal.group_collect_list",
    value: "部门报餐汇总",
    name: 'MerchantDepMealReport'
  },
  {
    key: "background_order.order_report_meal.collect_list",
    value: "食堂报餐汇总",
    name: 'MerchantCanteenMealReport'
  },
  {
    key: "background_healthy.label_group.list",
    value: "菜品标签",
    name: 'MerchantFoodLabel'
  },
  {
    key: "background_healthy.label_group.ingredient_list",
    value: "食材标签",
    name: 'MerchantIngredientsLabel'
  },
  {
    key: "background_food.ingredient_supplier.list",
    value: "供应商管理",
    name: 'MerchantSupplierAdmin'
  },
  {
    key: "background_store.goods",
    value: "商品管理",
    name: 'MerchantStoreGoodsAdmin'
  },
  {
    key: "background_store.goods_stock",
    value: "商品库存",
    name: 'MerchantStockGoodsStock'
  },
  {
    key: "background_store.goods.sales",
    value: "商品销量",
    name: 'MerchantStockGoodsSales'
  },
  {
    key: "background_store.goods.goods_stock",
    value: "商品出入库明细",
    name: 'MerchantGoodsStockDetails'
  },
  {
    key: "background_approve.approve_order_rule.list",
    value: "就餐申请规则",
    name: 'MerchantMealApplyAdmin'
  },
  {
    key: "background_approve.order_approve_visitor.list",
    value: "访客餐订单",
    name: 'MerchantMealApplyOrder'
  },
  {
    key: "background_approve.order_approve_visitor.collect_list",
    value: "访客餐汇总表",
    name: 'MerchantMealApplyTotal'
  },
  {
    key: "background_approve.order_approve_visitor.preparation_list",
    value: "访客餐备餐表",
    name: 'MerchantMealApplyPrepare'
  },
  // {
  //   key: "background_order.ordering_food.list",
  //   value: "订餐明细表",
  //   name: ''
  // },
  // {
  //   key: "background_order.ordering_food.ordering_food_summary",
  //   value: "科室订餐汇总",
  //   name: ''
  // },
  // {
  //   key: "background_order.ordering_food.ordering_charge_list",
  //   value: "用户充值/退款汇总",
  //   name: ''
  // },
  // 设备管理
  {
    key: "background_device.device_info.list",
    value: "设备列表",
    name: 'MerchantDeviceList'
  },
  {
    key: "background_organization.manage_card",
    value: "设备管理卡",
    name: 'MerchantDeviceAdminCard'
  },
  {
    key: "background_device.face_record.list",
    value: "设备同步记录",
    name: 'MerchantFaceSynchronizationHistory'
  },
  {
    key: "background_device.third_device.list",
    value: "第三方设备",
    name: 'MerchantThirdPartyEquipmentAdmin'
  },
  {
    key: "background_printer_management",
    value: "打印管理",
    name: 'MerchantPrintAdmin'
  },
  {
    key: "background_printer.printer.add_printer_settings",
    value: "打印设置",
    name: 'MerchantPrinterSettings'
  },
  {
    key: "background_device.device_buffet",
    value: "常规设置",
    name: 'RoutineSetting'
  },
  {
    key: "background_device.tray.tray_list",
    value: "托盘码管理",
    name: 'TrayQRcode'
  },
  {
    key: "background_device.cupboard_conf.details",
    value: "取餐柜设置",
    name: 'MerchantCupboardSetting'
  },
  {
    key: "background_device.d2_consume.details",
    value: "D2消费机设置",
    name: 'MerchantD2ConsumerMachineSeting'
  },
  {
    key: "background_device.consume_info.details",
    value: "AI消费机设置",
    name: 'MerchantConsumeMachineSetting'
  },
  {
    key: "background_device.settle_info.details",
    value: "结算台设置",
    name: 'MerchantAiSettlementSetting'
  },
  // {
  //   key: "background_device.device_nutrition_price_tag.details",
  //   value: "营养价签设置",
  //   name: ''
  // },
  // 营销活动
  {
    key: "background_marketing.consume.list",
    value: "消费规则",
    name: 'MerchantConsumptionRules'
  },
  {
    key: "background_marketing.discount_limit",
    value: "优惠限制",
    name: 'MerchantDiscountLimit'
  },
  {
    key: "background_marketing.recharge.list",
    value: "活动列表",
    name: 'MerchantActivityRecharges'
  },
  {
    key: "background_marketing.recharge.report",
    value: "充值活动统计",
    name: 'MerchantActivityRechargeStatistics'
  },
  {
    key: "background_coupon.coupon_manage.list",
    value: "优惠券管理",
    name: 'MerchantCouponAdmin'
  },
  {
    key: "background_marketing.marketing_banner",
    value: "banner图",
    name: 'MerchantBanner'
  },
  {
    key: "background_marketing.marketing_popup",
    value: "弹窗",
    name: 'MerchantMobilePopup'
  },
  {
    key: "background_marketing.survey_info.list",
    value: "调查问卷",
    name: 'MerchantSurveyAdmin'
  },
  // {
  //   key: "background_operation_management.kitchen_info.list",
  //   value: "后厨人员信息公示",
  //   name: ''
  // },
  {
    key: "marketing_activities_rate_settings",
    value: "手续费规则",
    name: 'MerchantRechargeDeductionService'
  },
  {
    key: "background_messages.third_messages_settings.modify",
    value: "推送配置",
    name: 'MerchantMessagesSetting'
  },
  {
    key: "background_marketing.alipay_qycode_rule.list",
    value: "支付宝企业码规则",
    name: 'MerchantAlipayEnterpriseCodeRules'
  },
  {
    key: "background_feedback.feedback_record.list",
    value: "食堂反馈",
    name: 'MerchantCanteenFeedback'
  },
  {
    key: "background_order.finance_report.unified_order_list",
    value: "明细总表",
    name: 'MerchantDetailTotalList'
  },
  {
    key: "background_report_center.data_report.coupon_order",
    value: "优惠券明细表",
    name: 'MerchantCouponDetailList'
  },
  {
    key: "background_order.finance_report.payment_order_detail_list",
    value: "消费明细表",
    name: 'MerchantConsumeDetailList'
  },
  {
    key: "background_order.finance_report.pecharge_order_list",
    value: "充值明细表",
    name: 'MerchantTopUpDetail'
  },
  {
    key: "background_report_center.data_report.order_withdraw_details_list",
    value: "提现明细表",
    name: 'MerchantWithdrawList'
  },
  {
    key: "background_report_center.finance_report.charge_off_details_list",
    value: "冲销明细表",
    name: 'MerchantWriteOffDetail'
  },
  {
    key: "background_report_center.finance_report.clear_subsidy_details_list",
    value: "补贴清零明细表",
    name: 'MerchantSubsidyClearDetail'
  },
  {
    key: "background_report_center.data_report.flat_cost_list",
    value: "工本费收款明细表",
    name: 'MerchantFlatCostReport'
  },
  {
    key: "background_report_center.data_report.flat_cost_refund_list",
    value: "工本费退款明细表",
    name: 'MerchantFlatCostRefund'
  },
  {
    key: "background_order.finance_report.device_consume_list",
    value: "设备消费明细",
    name: 'MerchantDeviceCost'
  },
  {
    key: "background_order.finance_report.reconciliation_statement_list",
    value: "消费点对账表",
    name: 'MerchantConsumeReconciliation'
  },
  {
    key: "background_report_center.data_report.third_order_list",
    value: "第三方对账表",
    name: 'MerchantThirdReconciliation'
  },
  {
    key: "background_report_center.data_report.group_wallet_daily_list",
    value: "分组储值汇总表",
    name: 'MerchantGroupWalletDaily'
  },
  {
    key: "background_report_center.finance_report.consumption_summary_list",
    value: "消费点汇总表",
    name: 'MerchantConsumptionSummaryReport'
  },
  {
    key: "background_report_center.data_report.consume_summary_list",
    value: "消费汇总表",
    name: 'MerchantConsumptionsSummary'
  },
  {
    key: "background_report_center.data_report.recharge_summary_list",
    value: "充值汇总表",
    name: 'MerchantRechargeSummary'
  },
  {
    key: "background_order.finance_report.order_business_list",
    value: "营业额日报表",
    name: 'MerchantReconciliationStatement'
  },
  {
    key: "background_order.finance_report.person_charge_list",
    value: "个人充值汇总",
    name: 'MerchantPersonalRechargeSummary'
  },
  {
    key: "background_order.finance_report.department_payment_collect_list",
    value: "部门消费汇总",
    name: 'MerchantDepartmentalConsumptionSummary'
  },
  {
    key: "background_order.finance_report.person_payment_collect_list",
    value: "个人消费汇总",
    name: 'MerchantPersonalConsumptionSummary'
  },
  {
    key: "background_order.finance_report.wallet_daily_list",
    value: "账户钱包日报表",
    name: 'MerchantAccountWalletDaily'
  },
  {
    key: "background_order.finance_report.person_wallet_daily_list",
    value: "个人钱包日报表",
    name: 'MerchantPersonalWalletDaily'
  },
  {
    key: "background_order.finance_report.instore_payment_detail_list",
    value: "收款码明细表",
    name: 'MerchantCollectionlCodeReport'
  },
  {
    key: "background_order.finance_report.commission_consume_list",
    value: "手续费总表",
    name: 'MerchantServiceChargeReport'
  },
  {
    key: "background_order.manage_report.food_payment_ranking_list",
    value: "菜品销售排行",
    name: 'MerchantFoodSaleRanking'
  },
  {
    key: "background_order.manage_report.food_sort_payment_ranking_list",
    value: "菜品分类销售汇总表",
    name: 'MerchantSummaryOfSales'
  },
  {
    key: "background_order.manage_report.food_payment_weight_ranking_list",
    value: "菜品取用量排行",
    name: 'MerchantDishesTakenRanking'
  },
  {
    key: "background_order.finance_report.out_put_list",
    value: "结算明细表",
    name: 'MerchantSettlementDetails'
  },
  {
    key: "background_order.finance_report.sub_mch_order_list",
    value: "账户结账明细",
    name: 'MerchantAccountBillingDetails'
  },
  {
    key: "background_order.finance_report.sub_mch_order_detail_list",
    value: "交易流水明细",
    name: 'MerchantBankFlowDetails'
  },
  // {
  //   key: "background_report_center.data_report.abc_jf_charge_order_sum_list",
  //   value: "二级缴费账户明细表",
  //   name: ''
  // },
  // {
  //   key: "background_report_center.data_report.abc_jf_charge_order_list",
  //   value: "二级缴费交易流水明细表",
  //   name: ''
  // },
  {
    key: "background_report_center.alipay_qycode.alipay_order",
    value: "额度使用明细表",
    name: 'MerchantAlipayEnterpriseCodeOrder'
  },
  // {
  //   key: "background_invoice.invoice_record.invoice_record_list",
  //   value: "开票记录",
  //   name: ''
  // },
  {
    key: "background_order.finance_report.person_meal_list",
    value: "人员就餐统计报表",
    name: 'MerchantPersonMealReport'
  },
  // 健康系统
  {
    key: "background_healthy.healthy_info.list",
    value: "健康档案",
    name: 'MerchantHealthSystem'
  },
  {
    key: "background.admin.healthy_info.check_data_list",
    value: "体检报告",
    name: 'MerchantPhysicalExaminationReport'
  },
  {
    key: "background_healthy.disease.list",
    value: "疾病管理",
    name: 'MerchantDiseaseManagement'
  },
  // 缴费中心
  {
    key: "background_jiaofei.list",
    value: "缴费管理",
    name: 'MerchantJiaoFeiAdmin'
  },
  {
    key: "background_order.order_jiao.list",
    value: "缴费订单",
    name: 'MerchantJiaoFeiOrder'
  },
  {
    key: "background_order.order_jiao.refund_list",
    value: "退款订单",
    name: 'MerchantJiaoFeiRefundOrder'
  },
  {
    key: "background_order.order_jiao.approval_list",
    value: "退款申请",
    name: 'MerchantJiaoFeiRefundApply'
  },
  // 监管管理
  {
    key: "background_fund_supervision.fund_upload.fund_upload_list",
    value: "资金上传",
    name: 'MerchantFundUpload'
  },
  {
    key: "background_approve.approve_fund.list",
    value: "资金审批",
    name: 'MerchantFundApproval'
  },
  {
    key: "background_fund_supervision.details_report.details_list",
    value: "收支明细表",
    name: 'MerchantIncomeAndExpenditureDetail'
  },
  {
    key: "background_fund_supervision.warn_manage.list",
    value: "智能预警",
    name: 'MerchantOperationWarning'
  },
  {
    key: "supervision_canteen_info",
    value: "食堂信息",
    name: 'MerchantCanteenInfo'
  },
  {
    key: "democratic_feedback",
    value: "民主监督",
    name: 'MerchantDemocraticFeedback'
  },
  {
    key: "background_fund_supervision.finance_approve.list",
    value: "财务审批",
    name: 'MerchantFinancialApplication'
  },
  {
    key: "background_fund_supervision.appropriation.list",
    value: "拨款申请",
    name: 'MerchantFinancialApproval'
  },
  {
    key: "background_store.retention_record.food_reserved_sample_record",
    value: "留样记录",
    name: 'MerchantSampleRecord'
  },
  {
    key: "background_fund_supervision.canteen_safety_management.get_person_schedule",
    value: "排班管理",
    name: 'MerchantSchedulingManagement'
  },
  {
    key: "background_fund_supervision.canteen_safety_management.morning_check_record",
    value: "晨检记录",
    name: 'MerchantMorningCheckLog'
  },
  {
    key: "background_fund_supervision.canteen_safety_management.pest_control_record",
    value: "有害生物防制",
    name: 'MerchantPestControl'
  },
  {
    key: "background_fund_supervision.food_safety_source",
    value: "食安溯源",
    name: 'MerchantFoodSafetyRoots'
  }
]
// 拓展功能
export const EXPAND = [
  { id: 9, label: '洗衣管理', disabled: true, src: require('@/assets/img/washing_system_orange.png'), type: "ZK_LAUNDRY" },
  { id: 6, label: '车辆管理', disabled: true, src: require('@/assets/img/icon-car.png'), url: 'http://passage-customer-manager-test.rlinking.com/#/', type: "car_management" },
  { id: 1, label: '考勤管理', disabled: true, src: require('@/assets/img/icon1.png') },
  { id: 2, label: '水控管理', disabled: true, src: require('@/assets/img/icon2.png') },
  { id: 13, label: '智慧校园', disabled: true, src: require('@/assets/img/icon13.png'), type: 'YDZHXY' },
  { id: 3, label: '电控管理', disabled: true, src: require('@/assets/img/icon3.png') },
  { id: 10, label: '督贝管理', disabled: true, src: require('@/assets/img/icon_dbgl.png'), type: "DoBay" },
  { id: 11, label: '智慧门店', disabled: true, src: require('@/assets/img/icon_zhmd.png'), type: "DoBay" },
  { id: 4, label: '监控管理', disabled: true, src: require('@/assets/img/icon4.png') },
  { id: 5, label: '门禁管理', disabled: true, src: require('@/assets/img/icon5.png') }
]

//  东贝跳转链接 ，默认用后台返回的URL 管理系统
export const ULR_DO_BEI_SYSTEM_DEV = 'https://dobayadmin.anasit.com/login'
export const ULR_DO_BEI_SYSTEM_STAGING = 'https://dobayadmin.anasit.com/login'
//  东贝跳转链接 ，默认用后台返回的URL 门店
export const ULR_DO_BEI_SHOP_DEV = 'https://shopadmin.anasit.com/login'
export const ULR_DO_BEI_SHOP_STAGING = 'https://shopadmin.anasit.com/login'
// 洗衣系统跳转URL
export const URL_WASHING_SYSTEM_DEV = 'https://web-xhf.lxt6.cn:8089/#/login'
export const URL_WASHING_SYSTEM_STAGING = 'https://web-xhf.lxt6.cn:8089/#/login'
