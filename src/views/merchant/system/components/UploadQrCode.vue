<template>
  <div class="upload-qr-code">
    <CustomDrawer
      :show.sync="visible"
      :loading="isLoading"
      title="上传二维码"
      @confirm="confirmUpload"
      @cancel="cancelUpload"
      :cancelText="'取 消'"
      :confirmText="'确 定'"
      :size="600"
    >
      <div class="upload-drawer-content">
        <el-form
          :model="uploadForm"
          :rules="uploadFormRules"
          ref="uploadFormRef"
          label-width="120px"
          class="upload-form"
        >
          <el-form-item label="二维码名称：" prop="name">
            <el-input
              v-model="uploadForm.name"
              placeholder="请输入二维码名称"
              class="ps-input w-250"
              maxlength="20"
            ></el-input>
          </el-form-item>
          <el-form-item label="支付类型：" prop="paymentType">
            <el-select
              v-model="uploadForm.paymentType"
              placeholder="请选择"
              filterable
              clearable
              class="ps-select w-250"
            >
              <el-option
                v-for="item in paymentTypeList"
                :key="item.payway + '_' + item.sub_payway"
                :label="item.merchant_name"
                :value="item.payway + '_' + item.sub_payway"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="上传图片" label-width="100px" prop="qrcodeImages">
            <div class="ps-red">仅支持jpg、png、bmp格式，大小不超过5M</div>
            <el-upload
              ref="uploadFoodImage"
              :class="{ 'upload-food': true, 'hide-upload': uploadForm.qrcodeImagesList.length > 0 }"
              drag
              :data="uploadParams"
              :action="actionUrl"
              :multiple="false"
              :file-list="uploadForm.qrcodeImagesList"
              list-type="picture-card"
              :on-change="handelChange"
              :on-success="handleFoodImgSuccess"
              :before-upload="beforeFoodImgUpload"
              :limit="1"
              :headers="headersOpts"
            >
              <div v-if="uploadForm.qrcodeImagesList.length < 1" class="upload-placeholder">
                <div class="upload-icon"><i class="el-icon-circle-plus"></i></div>
                <div class="upload-text">上传图片</div>
              </div>
              <div
                slot="file"
                slot-scope="{ file }"
                v-loading="file.status === 'uploading'"
                element-loading-text="上传中"
              >
                <div class="upload-food-img">
                  <el-image :src="file.url" alt="" fit="cover" class="img-tag"></el-image>
                </div>
                <span class="el-upload-list__item-actions">
                  <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
                    <i class="el-icon-zoom-in"></i>
                  </span>
                  <span class="el-upload-list__item-delete" @click="handleFoodImgRemove(file, 'qrcodeImages')">
                    <i class="el-icon-delete"></i>
                  </span>
                </span>
              </div>
            </el-upload>
          </el-form-item>
        </el-form>
      </div>
    </CustomDrawer>
    <el-dialog :visible.sync="dialogVisible">
      <img width="100%" :src="dialogImageUrl" alt="" />
    </el-dialog>
  </div>
</template>

<script>
import { getToken, getSuffix } from '@/utils'
export default {
  name: 'UploadQrCode',
  props: {
    show: {
      type: Boolean,
      required: true
    },
    organizationData: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    let validatorImage = (rule, value, callback) => {
      if (!this.uploadForm.qrcodeImages.length) {
        return callback(new Error('请上传二维码'))
      } else {
        callback()
      }
    }
    return {
      isLoading: false,
      uploadParams: {
        prefix: 'qrcodeImage'
      },
      actionUrl: '/api/background/file/upload',
      headersOpts: {
        TOKEN: getToken()
      },
      uploadForm: {
        name: '',
        paymentType: '',
        qrcodeImages: [],
        qrcodeImagesList: []
      },
      uploadFormRules: {
        name: [{ required: true, message: '请输入二维码名称', trigger: ['change', 'blur'] }],
        paymentType: [{ required: true, message: '请选择支付类型', trigger: ['change', 'blur'] }],
        qrcodeImages: [{ required: true, validator: validatorImage, trigger: ['change', 'blur'] }]
      },
      paymentTypeList: [],
      dialogImageUrl: '',
      dialogVisible: false
    }
  },
  computed: {
    visible: {
      get() {
        return this.show
      },
      set(val) {
        this.$emit('update:show', val)
      }
    }
  },
  created() {},
  watch: {
    show(newVal) {
      if (newVal) {
        this.getpayList()
        this.resetUploadForm()
      }
    }
  },
  methods: {
    // 重置上传表单
    resetUploadForm() {
      this.uploadForm = {
        name: '',
        paymentType: '',
        qrcodeImages: [],
        qrcodeImagesList: []
      }
      this.$nextTick(() => {
        this.$refs.uploadFormRef && this.$refs.uploadFormRef.clearValidate()
      })
    },
    // 取消上传
    cancelUpload() {
      this.visible = false
      this.resetUploadForm()
    },
    // 确认上传
    confirmUpload() {
      this.$refs.uploadFormRef.validate(valid => {
        if (valid) {
          this.submitUpload()
        } else {
          return false
        }
      })
    },
    handelChange(file, fileList) {
      this.uploadParams.key = this.uploadParams.prefix + new Date().getTime() + Math.floor(Math.random() * 150) + '.png'
    },
    handleFoodImgSuccess(res, file, fileList) {
      if (res.code === 0) {
        this.uploadForm.qrcodeImagesList = fileList
        this.uploadForm.qrcodeImages.push(res.data.public_url)
      } else {
        this.$message.error(res.msg)
      }
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    handleFoodImgRemove(file, type) {
      let index = this.uploadForm[type + 'List'].findIndex(item => item.url === file.url)
      this.uploadForm[type].splice(index, 1)
      this.uploadForm[type + 'List'].splice(index, 1)
    },
    beforeFoodImgUpload(file) {
      const unUploadType = ['.jpeg', '.jpg', '.png', '.bmp']
      const isLt2M = file.size / 1024 / 1024 < 5
      if (!unUploadType.includes(getSuffix(file.name))) {
        this.$message.error('上传图片只能是JPG/BMP/PNG格式!')
        return false
      }

      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 5MB!')
        return false
      }
    },
    async submitUpload() {
      this.isLoading = true
      const params = {
        name: this.uploadForm.name,
        payway: this.uploadForm.paymentType,
        organization_id: this.organizationData.id,
        url: this.uploadForm.qrcodeImages[0]
      }

      const [err, res] = await this.$to(this.$apis.apiBackgroundPaymentPayInfoAddConsumeQrcodePost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.$emit('upload-success')
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取支付方式 / 支付类型
    async getpayList() {
      const res = await this.$apis.apiBackgroundReportCenterDataReportGetPayInfoScanpayPost()
      if (res.code === 0) {
        this.paymentTypeList = res.data
      } else {
        this.$message.error(res.msg)
      }
    }
  }
}
</script>

<style lang="scss">
.upload-qr-code {
  .upload-drawer-content {
    padding: 20px;
  }
  .upload-food {
    overflow: hidden;
    max-height: 830px;
    &.hide-upload {
      .el-upload--picture-card {
        display: none;
      }
    }
    .el-upload--picture-card {
      border: none;
    }
    .el-upload-dragger {
      width: 146px;
      height: 146px;
    }
    .upload-food-img {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 146px;
      height: 146px;
      img {
        width: 146px;
        height: 146px;
        object-fit: cover;
      }
    }
  }
  .upload-placeholder {
    width: 146px;
    height: 146px !important;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background: #f5f7fa;
    .el-icon-circle-plus {
      font-size: 30px;
      color: #ff9b45;
    }
    .upload-icon {
      line-height: 30px;
      height: 30px;
    }

    .upload-text {
      margin-top: 8px;
      font-size: 12px;
      color: #8c939d;
      line-height: 12px;
      height: 12px;
    }
    .dis-content {
      display: contents;
    }
  }
}
</style>
