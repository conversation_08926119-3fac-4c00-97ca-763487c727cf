<template>
  <div class="AddOrEditAttendanceSetting container-wrapper">
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">{{type==='add'?'新建':'编辑'}}考勤设置</div>
      </div>
      <div>
        <el-form
          :model="settingForm"
          @submit.native.prevent
          status-icon
          ref="settingFormRef"
          :rules="settingFormRules"
          label-width="100px"
          class="attendance-form"
        >
          <el-form-item label="名称" prop="name">
            <el-input v-model="settingForm.name" class="ps-input w-250"></el-input>
          </el-form-item>
          <el-form-item label="考勤组" prop="attendanceGroup">
            <el-select
              v-model="settingForm.attendanceGroup"
              :multiple="true"
              placeholder="请下拉选择"
              class="ps-select w-250"
              popper-class="ps-popper-select"
            >
              <el-option
                v-for="item in attendanceGroupList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="适用日期" prop="week">
            <el-select
              v-model="settingForm.week"
              :multiple="true"
              @change="changeWeek"
              placeholder="请下拉选择"
              class="ps-select w-250"
              popper-class="ps-popper-select"
            >
              <el-option
                v-for="item in weekList"
                :key="item.key"
                :label="item.name"
                :value="item.key"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="考勤时段" prop="timeRangeList">
            <div v-for="(item,index) in settingForm.timeRangeList" :key="index" class="time-setting">
              <div class="time-range">
                <el-form-item label="签到时间" :prop="'timeRangeList.'+index+'.sign_in_time'" :rules="settingFormRules.sign_in_time">
                  <el-time-picker
                    class="ps-picker w-150"
                    value-format="HH:mm"
                    format="HH:mm"
                    v-model="item.sign_in_time">
                  </el-time-picker>
                </el-form-item>
                <el-form-item label="签退时间" :prop="'timeRangeList.'+index+'.sign_out_time'" :rules="settingFormRules.sign_out_time">
                  <el-time-picker
                    class="ps-picker w-150"
                    value-format="HH:mm"
                    format="HH:mm"
                    v-model="item.sign_out_time">
                  </el-time-picker>
                </el-form-item>
                <img src="@/assets/img/reduce.png" alt="" @click="delTimeRange(index)" v-if="settingForm.timeRangeList.length > 1">
                <img src="@/assets/img/plus.png" alt="" @click="addTimeRange()"  v-if="settingForm.timeRangeList.length-1===index">
              </div>
              <el-form-item label="打卡时间限制"  class="time-limit">
                <el-form-item :prop="'timeRangeList.'+index+'.sign_in_time_ahead'" :rules="settingFormRules.validateMin">
                  签到前
                  <el-input v-model="item.sign_in_time_ahead" class="ps-input time-limit-input"></el-input>
                  分钟可打签到卡
                </el-form-item>
                <el-form-item :prop="'timeRangeList.'+index+'.sign_out_time_ahead'" :rules="settingFormRules.validateMin">
                  允许最晚
                  <el-input v-model="item.sign_out_time_ahead" class="ps-input time-limit-input"></el-input>
                  分钟打签退卡
                </el-form-item>
                <el-form-item :prop="'timeRangeList.'+index+'.be_late_time_ahead'" :rules="settingFormRules.validateMin">
                  <el-input v-model="item.be_late_time_ahead" class="ps-input time-limit-input"></el-input>
                  分钟内打卡计为迟到，超过设定时间计为缺卡
                </el-form-item>
                <el-form-item :prop="'timeRangeList.'+index+'.leave_early_time_ahead'" :rules="settingFormRules.validateMin">
                  提前
                  <el-input v-model="item.leave_early_time_ahead" class="ps-input time-limit-input"></el-input>
                  分钟打卡算早退
                </el-form-item>
              </el-form-item>
            </div>
          </el-form-item>
          <el-form-item label="节假日跳过">
            <el-switch v-model="settingForm.isSkipHoliday" active-color="#ff9b45" inactive-color="#ffcda2"></el-switch>
          </el-form-item>
          <el-form-item label="特殊日期" class="special-date">
            <div class="no-skip-days" v-for="(item, index) in settingForm.skipDaysList" :key="index">
              {{formatDateText(item)}}
              <div class="del-day" @click="delSkipDay(index)"><i class="el-icon-error"></i></div>
            </div>
            <div class="hidden-date" style="">
              <img src="@/assets/img/plus.png" alt="">
              <el-date-picker
                type="dates"
                :clearable="false"
                v-model="settingForm.skipDays"
                @change="changeSkipDays"
                placeholder="选择一个或多个日期"
                format="MM-dd"
                value-format="MM-dd"
                popper-class="hidden-picker-year">
              </el-date-picker>
            </div>
            <!-- <span style="color:#2694ee; cursor: pointer;" @click="clearSkipDays">清空</span> -->
          </el-form-item>
          <el-form-item>
            <el-button size="small" type="primary" class="ps-origin-btn w-150" @click="saveSetting">保存</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script>
import { weekList } from '../constantsConfig'
export default {
  name: 'AddOrEditAttendanceSetting',
  components: {},
  props: {},
  data() {
    let validateMin = (rule, value, callback) => {
      let reg = /^[0-9]+$/
      if (!value) {
        return callback(new Error("请输入时长，单位为分钟"));
      } else if (!reg.test(value)) {
        callback(new Error("分钟必须为大于零的正整数"));
      } else if (value > 1000) {
        callback(new Error("不能大于1000分钟"));
      } else {
        callback();
      }
    };
    let validateSignInTime = (rule, value, callback) => {
      let index = rule.field.split('.')[1]
      let data = this.settingForm.timeRangeList[index]
      if (!value) {
        return callback(new Error("请选择签到时间"));
      } else {
        if (data.sign_in_time === data.sign_out_time) {
          callback(new Error("签到时间与签退时间不能相同"));
        } else if (data.sign_out_time && data.sign_in_time > data.sign_out_time) {
          callback(new Error("签到时间不能晚于签退时间"));
        } else {
          callback();
        }
      }
    };
    let validateSignOutTime = (rule, value, callback) => {
      let index = rule.field.split('.')[1]
      let data = this.settingForm.timeRangeList[index]
      if (!value) {
        return callback(new Error("请选择签退时间"));
      } else {
        if (data.sign_in_time === data.sign_out_time) {
          callback(new Error("签到时间与签退时间不能相同"));
        } else if (data.sign_in_time && data.sign_in_time > data.sign_out_time) {
          callback(new Error("签退时间不能早于签到时间"));
        } else {
          callback();
        }
      }
    };
    return {
      isLoading: false, // 刷新数据
      type: '',
      settingData: {},
      settingForm: {
        name: '',
        attendanceGroup: [],
        week: [],
        timeRangeList: [
          {
            sign_in_time: '',
            sign_out_time: '',
            sign_in_time_ahead: '',
            sign_out_time_ahead: '',
            be_late_time_ahead: '',
            leave_early_time_ahead: ''
          }
        ],
        isSkipHoliday: false,
        skipDaysList: [],
        skipDays: []
      },
      settingFormRules: {
        name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
        attendanceGroup: [{ required: true, message: '请选择考勤组', trigger: 'change' }],
        week: [{ required: true, message: '请选择适用日期', trigger: 'change' }],
        sign_in_time: [{ required: true, validator: validateSignInTime, trigger: 'change' }],
        sign_out_time: [{ required: true, validator: validateSignOutTime, trigger: 'change' }],
        validateMin: [{ required: true, validator: validateMin, trigger: 'blur' }]
      },
      attendanceGroupList: [],
      weekList
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      if (this.$route.query.data) {
        this.settingData = JSON.parse(decodeURIComponent(this.$route.query.data))
        this.settingForm.name = this.settingData.name
        this.settingForm.attendanceGroup = this.settingData.attendance_groups_ids
        this.settingForm.week = this.settingData.week_day_range
        this.settingForm.timeRangeList = []
        this.settingData.attendance_time_settings.map((item, index) => {
          this.settingForm.timeRangeList.push({})
          for (let key in item) {
            if (key !== 'id' && key !== 'status') {
              this.$set(this.settingForm.timeRangeList[index], key, item[key])
            }
          }
        })
        this.settingForm.isSkipHoliday = this.settingData.is_pass_holiday
        this.settingForm.skipDaysList = this.settingData.except_date_range
        this.settingForm.skipDays = this.settingData.except_date_range
      }
      if (this.$route.params.type) {
        this.type = this.$route.params.type
      }
      this.getAttendanceGroupList()
    },
    saveSetting() {
      this.$refs.settingFormRef.validate(valid => {
        if (valid) {
          let params = {
            name: this.settingForm.name,
            attendance_groups: this.settingForm.attendanceGroup,
            week_day_range: this.settingForm.week,
            attendance_time_settings: this.settingForm.timeRangeList,
            is_pass_holiday: this.settingForm.isSkipHoliday,
            except_date_range: this.settingForm.skipDaysList
          }
          let api
          switch (this.type) {
            case 'add':
              api = this.$apis.apiBackgroundAttendanceAttendanceSettingsAddPost(params)
              break;
            case 'edit':
              params.id = Number(this.settingData.id)
              api = this.$apis.apiBackgroundAttendanceAttendanceSettingsModifyPost(params)
              break;
          }
          this.confirmOperation(api)
        } else {
        }
      })
    },
    async confirmOperation(api) {
      if (this.isLoading) return
      this.isLoading = true
      const res = await api
      this.isLoading = false
      if (res.code === 0) {
        this.$message.success('成功')
        this.$closeCurrentTab(this.$route.path)
      } else {
        this.$message.error(res.msg)
      }
    },
    async getAttendanceGroupList() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundAttendanceAttendanceGroupListPost({
        page: 1,
        page_size: 99999
      })
      this.isLoading = false
      if (res.code === 0) {
        this.attendanceGroupList = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    addTimeRange() {
      this.settingForm.timeRangeList.push({
        sign_in_time: '',
        sign_out_time: '',
        sign_in_time_ahead: '',
        sign_out_time_ahead: '',
        be_late_time_ahead: '',
        leave_early_time_ahead: ''
      })
    },
    delTimeRange(index) {
      this.settingForm.timeRangeList.splice(index, 1)
    },
    changeSkipDays() {
      this.settingForm.skipDaysList = this.settingForm.skipDays
    },
    clearSkipDays() {
      this.settingForm.skipDaysList = []
      this.settingForm.skipDays = ''
    },
    delSkipDay(index) {
      this.settingForm.skipDaysList.splice(index, 1)
      this.settingForm.skipDays = this.settingForm.skipDaysList
    },
    formatDateText(date) {
      let dateText = date.split('-')[0] + '月' + date.split('-')[1] + '日'
      return dateText
    },
    changeWeek(e, index) {
      if (e.indexOf('all') !== -1) {
        this.settingForm.week = ['1', '2', '3', '4', '5', '6', '7']
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/styles/variables.scss";
.attendance-form{
  .time-setting:nth-child(n+2) {
    border-top: 1px #e0e6eb solid;
  }
  .time-setting{
    padding: 15px 0;
    margin-top: 15px;
    .time-range{
      display: flex;
      align-items: center;
      margin-bottom: 20px;
      .el-form-item{
        width: 260px;
      }
      img{
        width: 30px;
        height: 30px;
        margin-right: 5px;
      }
    }
    .time-limit{
      .el-form-item{
        margin-bottom: 20px;
      }
      .time-limit-input{
        width: 80px;
        margin: 0 5px;
      }
    }
  }
  .special-date{
    .no-skip-days{
      display: inline-block;
      margin-right: 30px;
      font-size: 15px;
      position: relative;
      .del-day{
        position: absolute;
        top: -8px;
        right: -12px;
        cursor: pointer;
      }
    }
    .hidden-date{
      color: #2694ee;
      cursor: pointer;
      margin-right: 30px;
      display: inline-block;
      position: relative;
      .el-date-editor {
        position: absolute ;//绝对定位
        top: 0 ;
        left: 0 ;
        opacity: 0;//设置完全透明
        width: 30px;
        .el-input__inner{
          padding: 0!important;
        }
      }
    }
  }
}
</style>
