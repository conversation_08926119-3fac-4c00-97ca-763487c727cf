<template>
  <div class="PersonAttendanceReport container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form
      ref="searchRef"
      @search="searchHandle"
      :form-setting="searchFormSetting"
      :autoSearch="false"
    ></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="plain" type="export" @click="gotoExport" v-permission="['background_attendance.absence_work_record_details_export']">导出Excel</button-icon>
        </div>
      </div>
      <div class="table-content">
        <custom-table
          border
          v-loading="isLoading"
          :table-data="tableData"
          :table-setting="tableSetting"
          ref="tableData"
          style="width: 100%"
          stripe
          :isFirst="isFirstSearch"
          header-row-class-name="ps-table-header-row"/>
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <el-pagination
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-size="pageSize"
          layout="total, prev, pager, next"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>
  </div>
</template>

<script>
import { debounce } from '@/utils'
import { recentSevenDay, getRequestParams } from '../constantsConfig'
import exportExcel from '@/mixins/exportExcel' // 导出混入
export default {
  name: 'PersonAttendanceReport',
  mixins: [exportExcel],
  props: {},
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [],
      tableSetting: [
        { label: '时间', key: 'create_time' },
        { label: '姓名', key: 'person_name' },
        { label: '考勤组', key: 'attendance_groups_name' },
        { label: '人员编号', key: 'person_no' },
        { label: '应打卡时间', key: 'right_punch_time' },
        { label: '状态', key: 'punch_status_alias' }
      ],
      searchFormSetting: {
        select_time: {
          type: 'daterange',
          label: '日期',
          value: recentSevenDay,
          clearable: false
        },
        person_name: {
          type: 'input',
          label: '姓名',
          value: '',
          placeholder: '请输入姓名'
        },
        person_no: {
          type: 'input',
          label: '人员编号',
          value: '',
          placeholder: '请输入人员编号'
        },
        attendance_groups_ids: {
          type: 'select',
          label: '考勤组',
          value: [],
          multiple: true,
          collapseTags: true,
          placeholder: '请选择考勤组',
          listNameKey: 'name',
          listValueKey: 'id',
          dataList: []
        }
      },
      isFirstSearch: false
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getMissCardRecordList()
      this.getAttendanceGroupList()
    },
    // 节下流咯
    searchHandle: debounce(function(e) {
      if (e && e === 'search') {
        this.currentPage = 1;
        this.isFirstSearch = false
        this.initLoad()
      }
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1;
      this.isFirstSearch = false
      this.tableData = []
      this.initLoad()
    },
    async getMissCardRecordList() {
      this.isLoading = true
      const params = getRequestParams(this.searchFormSetting, this.currentPage, this.pageSize)
      const res = await this.$apis.apiBackgroundAttendanceAttendanceRecordDetailsListPost({
        ...params,
        punch_status: ['absence_work']
      })
      this.isLoading = false
      if (res.code === 0) {
        this.tableData = res.data.results
        this.totalCount = res.data.count
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val;
      this.getMissCardRecordList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getMissCardRecordList()
    },
    gotoExport() {
      const option = {
        type: "AttendanceRecordAbsenceWorkExport",
        params: {
          ...getRequestParams(this.searchFormSetting, this.page, this.totalCount),
          punch_status: ['absence_work']
        }
      }
      this.exportHandle(option)
    },
    // 获取考勤组
    async getAttendanceGroupList() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundAttendanceAttendanceGroupListPost({
        page: 1,
        page_size: 99999
      })
      this.isLoading = false
      if (res.code === 0) {
        this.searchFormSetting.attendance_groups_ids.dataList = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  @import "~@/styles/variables.scss";
</style>
