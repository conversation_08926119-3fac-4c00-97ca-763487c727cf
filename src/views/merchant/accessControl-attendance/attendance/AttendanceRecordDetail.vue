<template>
  <div class="AttendanceRecordDetail container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form
      ref="searchRef"
      @search="searchHandle"
      :form-setting="searchFormSetting"
      :autoSearch="false"
    ></search-form>
    <div class="person-count">
      <div class="person-count-item" v-for="(item, index) in personCountList" :key="index">
        <div class="count">{{item.count}}人</div>
        <div>{{item.title}}</div>
      </div>
    </div>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="plain" type="export" @click="gotoExport" v-permission="['background_attendance.record_details.list_export']">导出Excel</button-icon>
        </div>
      </div>
      <div class="table-content">
        <custom-table
          border
          v-loading="isLoading"
          :table-data="tableData"
          :table-setting="tableSetting"
          ref="tableData"
          style="width: 100%"
          stripe
          :isFirst="isFirstSearch"
          header-row-class-name="ps-table-header-row"/>
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <el-pagination
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-size="pageSize"
          layout="total, prev, pager, next"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>
  </div>
</template>

<script>
import { debounce } from '@/utils'
import { recentSevenDay, punchStatuaList, getRequestParams } from '../constantsConfig'
import exportExcel from '@/mixins/exportExcel' // 导出混入
export default {
  name: 'AttendanceRecordDetail',
  mixins: [exportExcel],
  props: {},
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [],
      tableSetting: [
        { label: '一级组织', key: 'primary' },
        { label: '二级组织', key: 'secondary' },
        { label: '三级组织', key: 'company' },
        { label: '姓名', key: 'person_name' },
        { label: '人员编号', key: 'person_no' },
        { label: '考勤组', key: 'attendance_groups_name' },
        { label: '上传时间', key: 'create_time' },
        { label: '打卡时间', key: 'punch_time' },
        { label: '打卡班次', key: 'attendance_settings_name' },
        { label: '打卡设备', key: 'device_name' },
        { label: '打卡结果', key: 'punch_status_alias' }
      ],
      searchFormSetting: {
        date_type: {
          type: 'select',
          value: 'punch_time',
          dataList: [
            {
              label: '打卡时间',
              value: 'punch_time'
            },
            {
              label: '上传时间',
              value: 'upload_time'
            }
          ]
        },
        select_time: {
          type: 'daterange',
          value: recentSevenDay,
          clearable: false
        },
        person_name: {
          type: 'input',
          label: '姓名',
          value: '',
          placeholder: '请输入姓名'
        },
        person_no: {
          type: 'input',
          label: '人员编号',
          value: '',
          placeholder: '请输入人员编号'
        },
        org_ids: {
          type: 'organizationSelect',
          value: [],
          label: '组织',
          checkStrictly: true,
          isLazy: false,
          multiple: true
        },
        punch_status: {
          type: 'select',
          label: '打卡结果',
          value: [],
          multiple: true,
          collapseTags: true,
          placeholder: '请选择打卡结果',
          listNameKey: 'name',
          listValueKey: 'key',
          dataList: punchStatuaList
        },
        attendance_groups_ids: {
          type: 'select',
          label: '考勤组',
          value: [],
          multiple: true,
          collapseTags: true,
          placeholder: '请选择考勤组',
          listNameKey: 'name',
          listValueKey: 'id',
          dataList: []
        }
      },
      personCountList: [
        {
          title: '签到人数',
          key: 'sign_in_count',
          count: 0
        },
        {
          title: '迟到人数',
          key: 'be_late_count',
          count: 0
        },
        {
          title: '早退人数',
          key: 'leave_early_count',
          count: 0
        },
        {
          title: '签退人数',
          key: 'sign_out_count',
          count: 0
        },
        {
          title: '缺卡人数',
          key: 'absence_work_count',
          count: 0
        },
        {
          title: '请假人数',
          key: 'for_leave_count',
          count: 0
        }
      ],
      isFirstSearch: false
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getAttendanceDetailsList()
      this.getAttendanceDetailsPersonList()
      this.getAttendanceGroupList()
    },
    // 节下流咯
    searchHandle: debounce(function(e) {
      if (e && e === 'search') {
        this.currentPage = 1;
        this.isFirstSearch = false
        this.initLoad()
      }
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1;
      this.tableData = []
      this.isFirstSearch = false
      this.initLoad()
    },
    // 列表数据
    async getAttendanceDetailsList() {
      this.isLoading = true
      const params = getRequestParams(this.searchFormSetting, this.currentPage, this.pageSize)
      const res = await this.$apis.apiBackgroundAttendanceAttendanceRecordDetailsListPost(params)
      this.isLoading = false
      if (res.code === 0) {
        this.tableData = res.data.results
        this.totalCount = res.data.count
      } else {
        this.$message.error(res.msg)
      }
    },
    // 人数统计
    async getAttendanceDetailsPersonList() {
      this.isLoading = true
      const params = getRequestParams(this.searchFormSetting)
      const res = await this.$apis.apiBackgroundAttendanceAttendanceRecordDetailsPresonStatusListPost(params)
      this.isLoading = false
      if (res.code === 0) {
        this.personCountList.map(item => {
          item.count = res.data[item.key]
        })
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val;
      this.getAttendanceDetailsList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getAttendanceDetailsList()
    },
    gotoExport() {
      const option = {
        type: "AttendanceRecordDetailsListExport",
        params: getRequestParams(this.searchFormSetting, this.page, this.totalCount)
      }
      this.exportHandle(option)
    },
    // 获取考勤组
    async getAttendanceGroupList() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundAttendanceAttendanceGroupListPost({
        page: 1,
        page_size: 99999
      })
      this.isLoading = false
      if (res.code === 0) {
        this.searchFormSetting.attendance_groups_ids.dataList = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  @import "~@/styles/variables.scss";
  .person-count{
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
    .person-count-item{
      width: 15%;
      background-color: #fff;
      border-radius: 10px;
      text-align: center;
      padding: 20px;
      .count{
        color: #ff9b45;
        font-size: 28px;
      }
    }
    .person-count-item:nth-child(-n+5){
      margin-right: 15px;
    }
  }
</style>
