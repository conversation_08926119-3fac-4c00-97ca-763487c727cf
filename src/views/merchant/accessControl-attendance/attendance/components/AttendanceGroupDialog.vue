<template>
  <dialog-message
    :show.sync="visible"
    :title="title"
    :loading.sync="isLoading"
    @close="handleClose"
    customClass="ps-dialog"
    :width="width"
  >
    <el-form
      :model="dialogForm"
      @submit.native.prevent
      status-icon
      ref="dialogFormRef"
      :rules="dialogFormRules"
      label-width="70px"
      class="attendance-form"
      inline
    >
      <div v-if="type === 'choosePerson'">
        <el-form-item label="姓名">
          <el-input v-model="personName" class="ps-input w-180" @input="searchUserList"></el-input>
        </el-form-item>
        <el-form-item label="人员编号" label-width="80px">
          <el-input v-model="personNo" class="ps-input w-180" @input="searchUserList"></el-input>
        </el-form-item>
        <el-form-item label="分组">
          <user-group-select
            :multiple="true"
            :collapse-tags="true"
            class="search-item-w ps-input w-180"
            v-model="userGroup"
            placeholder="请下拉选择"
            @change="searchUserList"
          ></user-group-select>
        </el-form-item>
        <el-form-item label="部门">
          <user-department-select
            class="w-180 ps-input"
            v-model="userDepartment"
            :clearable="true"
            :multiple="true"
            :check-strictly="true"
            :isLazy="false"
            placeholder="请选择部门"
            :append-to-body="true"
            @change="searchUserList"
            >
          </user-department-select>
        </el-form-item>
        <div class="person-table">
          <el-table
            ref="userListRef"
            :data="userList"
            :row-key="getRowKey"
            header-row-class-name="ps-table-header-row"
            @select="handleSelection"
            @select-all="handleAllSelection">
            <el-table-column type="selection" :reserve-selection="true" width="50" align="center" class-name="ps-checkbox"></el-table-column>
            <el-table-column prop="name" label="姓名" align="center"></el-table-column>
            <el-table-column prop="person_no" label="人员编号" align="center"></el-table-column>
            <el-table-column prop="card_department_group_alias" label="部门" align="center"></el-table-column>
            <el-table-column prop="phone" label="手机号" align="center"></el-table-column>
          </el-table>
        </div>
        <div class="block ps-pagination person-table-bottom">
          <div style="width: 100px;">已选人数：{{selectList.length}}</div>
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[10, 20, 50, 100, 500]"
            :page-size="pageSize"
            layout="total, prev, pager, next, sizes, jumper"
            :total="totalCount"
            :pager-count="5"
            background
            class="ps-text"
            popper-class="ps-popper-select"
          ></el-pagination>
        </div>
      </div>
      <div v-if="type==='addAttendanceGroup'||type==='editAttendanceGroup'">
        <el-form-item label="名称" prop="groupName">
          <el-input maxlength="20" v-model="dialogForm.groupName" class="ps-input w-250"></el-input>
        </el-form-item>
        <el-form-item label="备注" prop="groupRemark">
          <el-input type="textarea" v-model="dialogForm.groupRemark" class="ps-input w-250"></el-input>
        </el-form-item>
      </div>
      <div v-if="type==='addGroupAdministrators'||type==='editGroupAdministrators'">
        <el-form-item label="名称" prop="administratorsName">
          <el-input maxlength="20" v-model="dialogForm.administratorsName" class="ps-input w-250"></el-input>
        </el-form-item>
        <el-form-item label="手机号" prop="administratorsPhone">
          <el-input v-model="dialogForm.administratorsPhone" class="ps-input w-250"></el-input>
        </el-form-item>
        <el-form-item label="考勤组" prop="administratorsGroups">
          <el-select
            v-model="dialogForm.administratorsGroups"
            :multiple="true"
            placeholder="请下拉选择"
            class="ps-select w-250"
            popper-class="ps-popper-select"
          >
            <el-option
              v-for="item in attendanceGroupList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="administratorsRemark">
          <el-input type="textarea" v-model="dialogForm.administratorsRemark" class="ps-input w-250"></el-input>
        </el-form-item>
      </div>
    </el-form>
    <template slot="tool">
      <div slot="footer" class="dialog-footer" style="margin-top: 20px; text-align: right;">
        <el-button
          :disabled="isLoading"
          class="ps-cancel-btn"
          @click="clickCancleHandle"
        >
          取消
        </el-button>
        <el-button
          :disabled="isLoading"
          class="ps-btn"
          type="primary"
          @click="clickConfirmHandle"
        >
          确定
        </el-button>
      </div>
    </template>
  </dialog-message>
  <!-- end -->
</template>

<script>
import { debounce, getSessionStorage } from '@/utils'
import UserGroupSelect from '@/components/UserGroupSelect'
import UserDepartmentSelect from '@/components/UserDepartmentSelect'
import { deepClone } from '@/assets/js/util'
export default {
  name: 'AttendanceGroupDialog',
  components: { UserGroupSelect, UserDepartmentSelect },
  props: {
    loading: Boolean,
    type: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    width: {
      type: String,
      default: '400px'
    },
    selectInfo: {
      type: Object,
      default() {
        return {}
      }
    },
    personList: {
      type: Array,
      default() {
        return []
      }
    },
    isshow: Boolean,
    // confirm: Function
  },
  data() {
    let validataPhone = (rule, value, callback) => {
      let regTelphone = /^1[3456789]\d{9}$/
      if (!value) {
        callback(new Error("请输入手机号"));
      } else if (!regTelphone.test(value)) {
        callback(new Error("手机号格式错误"));
      } else {
        callback();
      }
    };
    let validataName = (rule, value, callback) => {
      let reg = /^\S+$/
      if (!value) {
        callback(new Error("请输入名称"));
      } else if (!reg.test(value)) {
        callback(new Error("名称不能包含空格"));
      } else {
        callback();
      }
    };
    return {
      isLoading: false,
      dialogForm: {
        groupName: '',
        groupRemark: '',
        administratorsName: '',
        administratorsPhone: '',
        administratorsGroups: [],
        administratorsRemark: ''
      },
      dialogFormRules: {
        groupName: [{ required: true, validator: validataName, trigger: 'blur' }],
        administratorsName: [{ required: true, validator: validataName, trigger: 'blur' }],
        administratorsPhone: [{ required: true, validator: validataPhone, trigger: 'blur' }],
        administratorsGroups: [{ required: true, message: '请选择考勤组', trigger: 'change' }]
      },
      organizationId: '',
      userGroup: [],
      userDepartment: [],
      personName: '',
      personNo: '',
      userList: [],
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      selectList: [],
      attendanceGroupList: []
    }
  },
  computed: {
    visible: {
      get() {
        return this.isshow
      },
      set(val) {
        this.$emit('update:isshow', val)
      }
    }
  },
  watch: {
    visible() {
      if (this.visible) {
        if (this.type === 'choosePerson') {
          this.$nextTick(() => {
            this.$refs.userListRef.clearSelection()
          });
          this.currentPage = 1
          this.userGroup = []
          this.userDepartment = []
          this.personName = ''
          this.personNo = ''
          let userInfo = JSON.parse(decodeURIComponent(getSessionStorage('USERINFO'))).orgs
          for (let key in userInfo) {
            this.organizationId = key
          }
          this.getUserList()
          let list = deepClone(this.personList)
          this.selectList = []
          list.map(item => {
            this.selectList.push({
              id: item
            })
          })
        } else if (this.type === 'editAttendanceGroup') {
          this.dialogForm.groupName = this.selectInfo.name
          this.dialogForm.groupRemark = this.selectInfo.remark
        } else if (this.type === 'addGroupAdministrators') {
          this.getAttendanceGroupList()
        } else if (this.type === 'editGroupAdministrators') {
          this.getAttendanceGroupList()
          this.dialogForm.administratorsName = this.selectInfo.name
          this.dialogForm.administratorsPhone = this.selectInfo.phone
          this.dialogForm.administratorsGroups = this.selectInfo.attendance_groups_ids
          this.dialogForm.administratorsRemark = this.selectInfo.remark
        }
      } else {
        this.$refs.dialogFormRef.resetFields()
      }
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
    },
    clickConfirmHandle() {
      this.$refs.dialogFormRef.validate(valid => {
        if (valid) {
          let params
          let api
          switch (this.type) {
            case 'choosePerson': {
              let userList = []
              this.selectList.map(item => { userList.push(item.id) })
              params = {
                id: this.selectInfo.id,
                card_users: userList
              }
              api = this.$apis.apiBackgroundAttendanceAttendanceGroupModifyPost(params)
              break;
            }
            case 'addAttendanceGroup':
              params = {
                name: this.dialogForm.groupName,
                remark: this.dialogForm.groupRemark
              }
              api = this.$apis.apiBackgroundAttendanceAttendanceGroupAddPost(params)
              break;
            case 'editAttendanceGroup':
              params = {
                id: this.selectInfo.id,
                name: this.dialogForm.groupName,
                remark: this.dialogForm.groupRemark
              }
              api = this.$apis.apiBackgroundAttendanceAttendanceGroupModifyPost(params)
              break;
            case 'addGroupAdministrators':
              params = {
                name: this.dialogForm.administratorsName,
                phone: this.dialogForm.administratorsPhone,
                attendance_groups: this.dialogForm.administratorsGroups,
                remark: this.dialogForm.administratorsRemark
              }
              api = this.$apis.apiBackgroundAttendanceAttendanceGroupAdminAddPost(params)
              break;
            case 'editGroupAdministrators':
              params = {
                id: this.selectInfo.id,
                name: this.dialogForm.administratorsName,
                phone: this.dialogForm.administratorsPhone,
                attendance_groups: this.dialogForm.administratorsGroups,
                remark: this.dialogForm.administratorsRemark
              }
              api = this.$apis.apiBackgroundAttendanceAttendanceGroupAdminModifyPost(params)
              break;
          }
          this.confirmOperation(api)
        } else {
        }
      })
    },
    async confirmOperation(api) {
      if (this.isLoading) return
      this.isLoading = true
      const res = await api
      this.isLoading = false
      if (res.code === 0) {
        this.$message.success('成功')
        // this.confirm()
        this.$emit('confirm', 'search')
      } else {
        this.$message.error(res.msg)
      }
    },
    clickCancleHandle() {
      this.visible = false
    },
    handleClose(e) {
      this.isLoading = false
      this.visible = false
      this.$refs.dialogFormRef.resetFields()
    },
    // 获取考勤组
    async getAttendanceGroupList() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundAttendanceAttendanceGroupListPost({
        page: 1,
        page_size: 99999
      })
      this.isLoading = false
      if (res.code === 0) {
        this.attendanceGroupList = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    // 用户的
    searchUserList: debounce(function() {
      this.currentPage = 1
      this.getUserList()
    }, 300),
    async getUserList() {
      this.isLoading = true
      let data = {
        card_user_group_ids: this.userGroup,
        card_department_group_ids: this.userDepartment,
        person_name: this.personName,
        person_no: this.personNo
      }
      let params = {}
      for (let key in data) {
        if (data[key]) {
          params[key] = data[key]
        }
      }
      const res = await this.$apis.apiCardServiceCardUserListPost({
        ...params,
        page: this.currentPage,
        page_size: this.pageSize
      })
      this.isLoading = false
      if (res.code === 0) {
        this.userList = res.data.results
        this.totalCount = res.data.count
        this.userList.map(user => {
          this.personList.map(selectId => {
            if (user.id === selectId) {
              this.$refs.userListRef.toggleRowSelection(user, true);
            }
          })
        })
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val;
      this.getUserList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getUserList()
    },
    handleSelection(val, row) {
      let index = this.selectList.findIndex(item => item.id === row.id)
      if (index === -1) {
        this.selectList.push(row)
      } else {
        this.selectList.splice(index, 1)
      }
    },
    handleAllSelection(selection) {
      let list = deepClone(selection)
      let flag = true
      this.userList.map(user => {
        let index = list.findIndex(item => item.id === user.id)
        if (index === -1) {
          flag = false
        }
      })
      if (flag) { // 全选
        this.userList.map(user => {
          let index = this.selectList.findIndex(item => item.id === user.id)
          if (index === -1) { // 把之前没有的加上
            this.selectList.push(user)
          }
        })
      } else { // 全不选
        this.userList.map(user => {
          let index = this.selectList.findIndex(item => item.id === user.id)
          if (index !== -1) { // 把之前有的去掉
            this.selectList.splice(index, 1)
          }
        })
      }
    },

    getRowKey(row) {
      return row.id;
    }
  }
}
</script>
<style lang="scss" scoped>
@import '~@/styles/variables.scss';
.person-table{
  max-height: 400px;
  overflow: auto;
}
.person-table-bottom{
  display:flex;
  align-items:center;
  justify-content: space-between;
  padding-top:20px;
}
</style>
