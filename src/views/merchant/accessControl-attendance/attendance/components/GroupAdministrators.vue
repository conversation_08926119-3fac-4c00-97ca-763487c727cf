<template>
  <div class="GroupAdministrators container-wrapper">
    <!-- <refresh-tool @refreshPage="refreshHandle" /> -->
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" type="add" @click="openDialog('addGroupAdministrators')" v-permission="['background_attendance.attendance_group_admin.add']">新建</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
        >
          <el-table-column prop="name" label="名称" align="center"></el-table-column>
          <el-table-column key="phone" prop="phone" label="手机号" align="center" >
            <template slot-scope="scope">
              <span>{{sensitiveSetting.phone ? scope.row.phone: '****'}}</span>
            </template>
          </el-table-column>
          <el-table-column prop="attendance_groups" label="管理考勤组" align="center"></el-table-column>
          <el-table-column prop="remark" label="备注" align="center"></el-table-column>
          <el-table-column fixed="right" label="操作" align="center">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                class="ps-bule"
                @click="openDialog('editGroupAdministrators', scope.row)"
                v-permission="['background_attendance.attendance_group_admin.modify']"
                >编辑</el-button>
              <el-button
                type="text"
                size="small"
                class="ps-warn"
                @click="delPushSetting(scope.row.id)"
                v-permission="['background_attendance.attendance_group_admin.delete']"
                >删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <el-pagination
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-size="pageSize"
          layout="total, prev, pager, next"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>
    <attendance-group-dialog
      :isshow.sync="dialogVisible"
      :title="dialogTitle"
      :type="dialogType"
      :width="dialogWidth"
      :select-info="selectInfo"
      @confirm="searchHandle"/>

  </div>
</template>

<script>
import { debounce } from '@/utils'
import AttendanceGroupDialog from './AttendanceGroupDialog.vue'

export default {
  name: 'GroupAdministrators',
  components: { AttendanceGroupDialog },
  props: {},
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [],
      dialogVisible: false,
      dialogTitle: '',
      dialogType: '',
      dialogWidth: '',
      selectInfo: {},
      sensitiveSetting: {}
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getSensitiveSetting()
      this.getGroupAdministrators()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.dialogVisible = false
      this.currentPage = 1;
      this.getGroupAdministrators()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.currentPage = 1;
      this.tableData = []
      this.initLoad()
    },
    async getGroupAdministrators() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundAttendanceAttendanceGroupAdminListPost({
        page: this.currentPage,
        page_size: this.pageSize
      })
      this.isLoading = false
      if (res.code === 0) {
        this.tableData = res.data.results
        this.tableData.map(item => {
          item.attendance_groups = []
          item.attendance_groups_ids = []
          item.attendance_groups_list.map(group => {
            item.attendance_groups.push(group.name)
            item.attendance_groups_ids.push(group.id)
          })
          item.attendance_groups = item.attendance_groups.join(",")
        })
        this.totalCount = res.data.count
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取获取登录组织设置的隐藏信息相关的
    async getSensitiveSetting() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundReportCenterDataReportGetSensitiveSettingPost()
      this.isLoading = false
      if (res.code === 0) {
        this.sensitiveSetting = res.data
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val;
      this.getGroupAdministrators()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getGroupAdministrators()
    },
    async delPushSetting(id) {
      this.$confirm(`确定删除该考勤组管理员？`, `提示`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            const res = await this.$apis.apiBackgroundAttendanceAttendanceGroupAdminDeletePost({
              ids: [id]
            })
            if (res.code === 0) {
              this.$message.success('删除成功')
              this.getGroupAdministrators()
            } else {
              this.$message.error(res.msg)
            }
            done()
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {
        })
        .catch(e => {})
    },
    openDialog(type, data) {
      this.dialogType = type
      this.selectInfo = data
      if (type === 'addGroupAdministrators') {
        this.dialogTitle = '新增考勤组管理员'
        this.dialogWidth = '400px'
      } else if (type === 'editGroupAdministrators') {
        this.dialogTitle = '编辑考勤组管理员'
        this.dialogWidth = '400px'
      }
      this.dialogVisible = true
    }
  }
}
</script>

<style lang="scss" scoped>
  @import "~@/styles/variables.scss";
</style>
