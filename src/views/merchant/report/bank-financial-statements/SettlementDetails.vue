<template>
  <div>
    <div class="settlement-details-wrapper container-wrapper">
      <refresh-tool @refreshPage="refreshHandle" />
      <div class="tab-box">
        <el-radio-group v-model="tabType" @change="changeTabHandle" class="ps-radio-btn">
          <el-radio-button label="record">结算记录</el-radio-button>
          <el-radio-button label="setting">结算设置</el-radio-button>
        </el-radio-group>
      </div>
      <div v-if="tabType === 'record'" class="tab-item">
        <div class="tab-item-content">
          <span>商户余额：</span>
          <span>{{ manualForm.balance? '¥'+ manualForm.balance :'¥0.00' }}</span>
          <div v-if="manualForm.type == 2" class="inline">
          <span class="m-l-20">消费入账金额：</span>
          <span>{{ manualForm.consumeBalance? '¥' + manualForm.consumeBalance :'¥0.00'}}</span>
          </div>
          <el-button
            @click="manualWthdrawalDialog = true"
            v-if="outpayForm.outpayType === 'manual'"
            type="primary"
            size="mini"
            class="cash-btn ps-origin-btn m-l-10"
          >
            手动出金
          </el-button>
          <div class="block ps-text-gray m-t-10" v-if="manualForm.type == 2" > 消费入账金额 = 经营日期内用户实际消费金额（需减去退款）</div>
        </div>
        <search-form
          ref="searchRef"
          :loading="isLoading"
          @search="searchHandle"
          :form-setting="searchFormSetting"
          :autoSearch="false"
        ></search-form>

        <div class="table-wrapper">
          <div class="table-header">
            <div class="table-title">数据列表</div>
            <div class="align-r">
              <el-button size="mini" @click="gotoBilling" v-permission="['background_order.finance_report.sub_mch_order_list']">结账报表</el-button>
              <el-button size="mini" @click="gotoExport" v-permission="['background_order.finance_report.out_put_list_export']">导出Excel</el-button>
              <button-icon color="plain" @click="gotoPrint">打印</button-icon>
              <button-icon color="plain" @click="openPrintSetting">报表设置</button-icon>
            </div>
          </div>
          <!-- table-content start -->
          <div class="table-content">
            <custom-table
              border
              v-loading="isLoading"
              :table-data="tableData"
              :table-setting="currentTableSetting"
              ref="tableData"
              style="width: 100%"
              stripe
              :isFirst="isFirstSearch"
              header-row-class-name="ps-table-header-row"
            />
          </div>
          <!-- table content end -->
          <!-- 统计 start -->
          <!-- <table-statistics :statistics="collect" /> -->
          <!-- 显示层级 -->
          <div class="m-l-20 total-orgs font-size-14">账户适用组织：{{orgs}}</div>
          <!-- end -->
          <!-- 分页 start -->
          <pagination
            :onPaginationChange="onPaginationChange"
            :current-page.sync="currentPage"
            :page-size.sync="pageSize"
            :layout="'total, prev, pager, next, jumper'"
            :total="totalCount"
          ></pagination>
          <!-- 分页 end -->
        </div>
      </div>
      <div v-if="tabType === 'setting'" class="tab-item tab-item-content">
        <el-form ref="outpayFormRef" :model="outpayForm" :rules="outpayFormRule" label-width="80px" label-position="left" :inline-message="true" v-loading="isLoading">
          <el-form-item label="" prop="subMchId" :inline-message="false">
            <el-select v-model="outpayForm.subMchId" @change="changeSubMchHandle" placeholder="请选择" class="ps-select">
              <el-option
                v-for="item in subMchList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="出金方式" prop="outpayType">
            <el-radio-group
              v-model="outpayForm.outpayType"
              @change="outpayTypeChange"
              class="ps-radio"
            >
              <el-radio label="auto">自动出金</el-radio>
              <el-radio label="manual">手动提现</el-radio>
              <el-radio label="fixed_date">固定日期出金</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item v-if="outpayForm.outpayType == 'auto'">
            <p>注：</p>
            <p>
              1.设定为自动出金时，需要在商户中预留部分金额，作为退款的备用金，在首次出金时会做出预留金额。
            </p>
            <p>
              2.如果出现二级商户预留余额不足设定的额度时，在后续的经营过程中，会自动从营业额中提取部分经营额度来作为补充
            </p>
            <!-- <div> -->
              <el-form-item label="出金时间设定：" prop="outpayDay" label-width="150px">
                T+
                <el-input
                  v-model="outpayForm.outpayDay"
                  size="mini"
                  class="ps-input"
                  style="width: 100px"
                  placeholder="请输入天数"
                ></el-input>
                天
              </el-form-item>
              <el-form-item label="二级商户预留金额：" label-width="150px" prop="reservedMoney" :rules="outpayFormRule.money" >
                <el-input
                  v-model="outpayForm.reservedMoney"
                  size="mini"
                  class="ps-input"
                  style="width: 100px"
                  placeholder="请输入金额"
                ></el-input>
                元
              </el-form-item>
            <!-- </div> -->
          </el-form-item>
          <el-form-item v-if="outpayForm.outpayType == 'manual'">
            <p>注：</p>
            <p>1.出金方式为手动出金时，账户的结算需要财务人员进行手动提现</p>
            <p>2.账户经营结算金额可参考报表《账户结账明细》</p>
            <p>3.如果设定部分金额作为退款备用金,那么系统将会冻结部分金额作为退款使用</p>
            <div>
              <el-form-item label="是否预留部分金额作为退款备用金：" prop="reservedMoneyEnable" label-width="250px">
                <el-radio-group
                  v-model="outpayForm.reservedMoneyEnable"
                  class="ps-radio"
                >
                  <el-radio :label="1">预留</el-radio>
                  <el-radio :label="0">不预留</el-radio>
                </el-radio-group>
              </el-form-item>
              <div class="reserve-box" v-if="outpayForm.reservedMoneyEnable">
                <el-form-item label="二级商户预留金额：" prop="manualrReservedMoney" :rules="outpayFormRule.money" label-width="150px">
                  <el-input
                    v-model="outpayForm.manualrReservedMoney"
                    size="mini"
                    class="ps-input"
                    style="width: 100px"
                    placeholder="请输入金额"
                  ></el-input>
                  元
                </el-form-item>
              </div>
            </div>
          </el-form-item>
          <el-form-item v-if="outpayForm.outpayType == 'fixed_date'">
            <p>注：</p>
            <p>
              1.设定为固定日期出金时，需要在商户中预留部分金额，作为退款的备用金，在首次出金时会做出预留金额。
            </p>
            <p>
              2.如果出现二级商户预留余额不足设定的额度时，在后续的经营过程中，会自动从营业额中提取部分经营额度来作为补充
            </p>
            <p>3.固定金额出金会在每月设定的固定日期进行出金，其他时间不会出金</p>
            <div>
              <el-form-item label="出金日期：" prop="fixedDateList" label-width="100px">
                <el-select
                  v-model="outpayForm.fixedDateList"
                  placeholder="请选择"
                  size="mini"
                  class="ps-select"
                  popper-class="ps-popper-select"
                  style="width: 130px;"
                  collapse-tags
                  multiple
                  filterable
                >
                  <el-option
                    v-for="item in outpayDateList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
              <div class="reserve-box" v-if="outpayForm.reservedMoneyEnable">
                <el-form-item label="二级商户预留金额：" prop="reservedMoney" :rules="outpayFormRule.money" label-width="160px">
                  <el-input
                    v-model="outpayForm.reservedMoney"
                    size="mini"
                    class="ps-input"
                    style="width: 100px"
                    placeholder="请输入金额"
                  ></el-input>
                  元
                </el-form-item>
              </div>
            </div>
          </el-form-item>
          <el-form-item>
            <el-button class="ps-origin-btn" type="primary" @click="checkSettingForm">确定</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <!-- 提现提示框 start -->
    <el-dialog
      title=""
      :visible.sync="manualWthdrawalDialog"
      width="450px"
      v-loading="isManualLoading"
    >
      <el-form v-loading="isManualLoading" ref="manualForm" :model="manualForm" :rules="manualFormRule" size="small">
        <el-form-item label="账户总余额：">
          <span>{{ manualForm.balance }}</span>
        </el-form-item>
        <el-form-item label="冻结预留金额：">
          <span>{{ manualForm.manual_reversed_money }}</span>
        </el-form-item>
        <el-form-item label="可提现余额：">
          <span>{{ manualForm.withdrawable_money }}</span>
        </el-form-item>
        <el-form-item label="收款商户：">
          <span>{{ manualForm.sub_mch_account_name }}</span>
        </el-form-item>
        <el-form-item label="收款账号：">
          <span>{{ manualForm.sub_mch_account_no }}</span>
        </el-form-item>
        <el-form-item label="收款银行：">
          <span>{{ manualForm.sub_mch_bank_name }}</span>
        </el-form-item>
        <el-form-item label="" prop="manualPrice">
          <el-input v-model="manualForm.manualPrice" placeholder="请输入提现金额（元）"></el-input>
        </el-form-item>
        <el-form-item label="附言：" >
          <el-input v-model="manualForm.remark" placeholder="请输入附言" maxlength="40"></el-input>
        </el-form-item>
        <el-form-item>
          <div style="text-align: center">
            <el-button type="primary" class="ps-origin-btn" @click="initiatedWithdrawal">
              发起提现
            </el-button>
          </div>
        </el-form-item>
      </el-form>
    </el-dialog>
    <!-- end -->
    <print-setting
      v-if="dialogPrintVisible"
      :extraParams="{ printType: printType }"
      :tableSetting="tableSetting"
      :defaultCheckedSetting="currentTableSetting"
      :show.sync="dialogPrintVisible"
      @confirm="confirmPrintDialog"
    ></print-setting>
  </div>
</template>

<script>
import { SETTLEMENT_DETAILS } from './constantsConfig'
import { divide, times, to, deepClone } from '@/utils'
import { validataPrice } from '@/assets/js/validata'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import report from '@/mixins/report' // 混入
import { mapGetters } from 'vuex'

export default {
  name: 'SettlementDetails',
  mixins: [exportExcel, report],
  data() {
    return {
      isLoading: false,
      tabType: 'record',
      tableSetting: [
        { label: '序号', key: 'index', type: 'index', width: '80px' },
        { label: '清算时间', key: 'create_time' },
        { label: '所属经营日期', key: 'trx_data' },
        { label: '商户出金流水号', key: 'order_no' },
        { label: '收款账号', key: 'sub_mch_account_no' },
        { label: '收款户名', key: 'sub_mch_account_name' },
        { label: '原出金金额', key: 'original_outpay_amount', type: 'money' },
        { label: '手续费', key: 'rate_fee', type: 'money' },
        { label: '经营日期退款金额', key: 'trx_date_refund_money', type: 'money' },
        { label: '原实际出金金额', key: 'original_real_outpay_amount', type: 'money' },
        { label: '当前预留金额', key: 'current_reserved_money', type: 'money' },
        { label: '补偿预留金额', key: 'supply_reserved_money', type: 'money' },
        { label: '增减后预留金额', key: 'final_reserved_money', type: 'money' },
        { label: '实际出金金额', key: 'real_outpay_amount', type: 'money' },
        { label: '剩余金额', key: 'residue_outpay_amount', type: 'money' },
        { label: '清算方式', key: 'outpay_type_verbose' },
        { label: '附言', key: 'remark' },
        { label: '状态', key: 'status_verbose' },
        { label: '原因', key: 'reason' }
      ],
      tableData: [],
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页

      searchFormSetting: deepClone(SETTLEMENT_DETAILS),
      collect: [],
      printType: 'SettlementDetails',
      outpaySettingIsLoading: false,
      outpayForm: {
        subMchId: '',
        outpayType: 'auto', // auto 自动出金，manual 手动提现，fixed_date 固定日期
        outpayDay: '', // 出金时间设定
        reservedMoney: '', // 二级商户预留金额
        reservedMoneyEnable: 1, // 是否预留部分金额作为退款备用金
        manualrReservedMoney: '', // 二级商户预留金额：
        fixedDateList: []
      },
      outpayFormRule: {
        subMchId: [
          { required: true, message: '请选择', trigger: "change" }
        ],
        outpayType: [
          { required: true, message: '请选择出金方式', trigger: "change" }
        ],
        outpayDay: [
          { required: true, message: '请输入天数', trigger: "change" }
        ],
        fixedDateList: [
          { required: true, message: '请选择出金日期', trigger: "change" }
        ],
        money: [
          { required: true, message: '请输入金额', trigger: "change" },
          { validator: validataPrice, trigger: "change" }
        ]
      },
      subMchList: '',
      outpayDateList: [],
      manualForm: {
        manualPrice: '',
        remark: ''
      },
      manualPrice: '',
      isManualLoading: false,
      manualWthdrawalDialog: false,
      manualFormRule: {
        manualPrice: [
          { required: true, message: '请输入金额', trigger: "change" },
          { validator: validataPrice, trigger: "change" }
        ]
      },
      selectPayinfo: {},
      isFirstSearch: true,
      orgs: '' // 适用组织
    }
  },
  computed: {
    ...mapGetters(['userInfo', 'organization'])
  },
  watch: {
    'searchFormSetting.sub_mch_id.value': function(val) {
      console.log("watch", val);
      this.outpayForm.subMchId = val
      this.getPayInfo(val)
    }
  },
  created() {
    // this.initLoad()
  },
  async mounted() {
    this.initOutpayDateList()
    this.initPrintSetting()
    await this.getSubMchList()
    await this.getOutPaySetting()
  },
  methods: {
    async initLoad() {
      // this.currentTableSetting = this.tableSetting
      this.getOutputDataList()
      this.getOutPayManualInfo()
    },

    async refreshHandle() {
      this.tabType = 'record'
      if (this.$refs.searchRef && Reflect.has(this.$refs.searchRef, 'resetForm')) {
        this.$refs.searchRef.resetForm()
      }
      this.currentPage = 1
      this.searchFormSetting = deepClone(SETTLEMENT_DETAILS)
      await this.getSubMchList()
      this.tableData = []
      this.isFirstSearch = true
      // this.getOutputDataList()
    },

    async searchHandle(e) {
      if (e && e === 'search') {
        this.currentPage = 1
        this.getPayInfo(this.searchFormSetting.sub_mch_id.value)
        this.getOutputDataList()
        await this.getOutPaySetting()
        this.getOutPayManualInfo()
        this.isFirstSearch = false
      }
    },
    initOutpayDateList() {
      this.outpayDateList = []
      this.outpayDateList.push({
        value: 0,
        label: '每月最后一天'
      })
      for (let index = 1; index < 29; index++) {
        this.outpayDateList.push({
          value: index,
          label: index
        })
      }
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 请求列表数据
    async getOutputDataList() {
      if (!this.searchFormSetting.sub_mch_id.value) {
        return this.$message.error('账号数据为空！')
      }
      this.isLoading = true
      const res = await this.$apis.apiBackgroundReportCenterDataReportOutputOrderListPost({
        ...this.formatQueryParams(this.searchFormSetting),
        company_id: this.userInfo.company_id,
        organization_id: this.organization,
        page: this.currentPage,
        page_size: this.pageSize
      })
      this.isLoading = false
      if (res.code === 0) {
        this.totalCount = res.data.count
        this.tableData = res.data.results
        // 统计
        this.setCollectData(res)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 请求账户数据
    async getSubMchList() {
      // const params = getRequestParams(this.searchFormSetting, this.currentPage, this.pageSize)
      const params = {
        company_id: this.userInfo.company_id
        // organization_id: this.organization
      }
      this.isLoading = true
      const res = await this.$apis.apiBackgroundAbcOutputOutputGetAbcPayinfoPost(params)
      this.isLoading = false
      if (res.code === 0) {
        if (res.data && res.data.length) {
          this.subMchList = res.data.map((v, i) => {
            let item = { label: v.sub_mch_id, payinfo_id: v.payinfo_id, value: v.sub_mch_id, use_orgs: v.use_orgs }
            if (i === 0) {
              this.searchFormSetting.sub_mch_id.value = v.sub_mch_id
              this.outpayForm.subMchId = v.sub_mch_id
              this.selectPayinfo = item
              this.getOrgsList(v)
            }
            return item
          })
          this.searchFormSetting.sub_mch_id.dataList = this.subMchList
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    // tab change
    changeTabHandle(e) {
      this.getPayInfo(this.searchFormSetting.sub_mch_id.value)
      if (e === 'setting') {
        this.outpayForm.subMchId = this.searchFormSetting.sub_mch_id.value
        this.getOutPaySetting()
      } else {
        this.$set(this.searchFormSetting.sub_mch_id, 'value', this.outpayForm.subMchId)
        this.getOutputDataList()
        if (this.outpayForm.outpayType === 'manual') {
          this.getOutPayManualInfo()
        }
        // this.getOutPaySetting()
      }
    },
    // 获取手动提现信息
    async getOutPayManualInfo() {
      const params = {
        company_id: Number(this.userInfo.company_id),
        organization_id: Number(this.organization),
        sub_mch_id: this.outpayForm.subMchId,
        payinfo_id: Number(this.selectPayinfo.payinfo_id)
      }
      if (!this.outpayForm.subMchId) {
        return this.$message.error('账号数据为空！')
      }
      this.isLoading = true
      const res = await this.$apis.apiBackgroundAbcOutputOutputGetManualOutpayInfoPost(params)
      this.isLoading = false
      if (res.code === 0) {
        //
        this.manualForm = {
          balance: res.data.balance ? divide(res.data.balance, 100) : 0,
          consumeBalance: res.data.consume_balance ? divide(res.data.consume_balance, 100) : 0,
          manual_reversed_money: divide(res.data.manual_reversed_money, 100),
          withdrawable_money: divide(res.data.withdrawable_money, 100),
          sub_mch_account_name: res.data.sub_mch_account_name,
          sub_mch_account_no: res.data.sub_mch_account_no,
          sub_mch_bank_name: res.data.sub_mch_bank_name,
          manualPrice: '',
          remark: '',
          type: res.data.type
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    // 校验手动提现金额
    initiatedWithdrawal() {
      this.$refs.manualForm.validate(valid => {
        if (valid) {
          if (this.isManualLoading) return;
          this.getManualOutpay()
        } else {
          console.log(valid)
        }
      })
    },
    // 提现
    async getManualOutpay(manualPrice) {
      this.isManualLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundAbcOutputOutputManualOutPaymentRequestPost({
          sub_mch_id: this.selectPayinfo.value,
          payinfo_id: Number(this.selectPayinfo.payinfo_id),
          company_id: Number(this.userInfo.company_id),
          organization_id: Number(this.organization),
          outpay_amount: times(this.manualForm.manualPrice),
          remark: this.manualForm.remark
        })
      )
      this.isManualLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success('提现成功')
        this.manualWthdrawalDialog = false
        this.manualForm.manualPrice = ''
        this.manualForm.remark = ''
        this.getOutputDataList()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 结算设置出金类型
    outpayTypeChange(e) {
      // 清楚上传校验
      this.$refs.outpayFormRef.clearValidate()
      // 获取参数
      // this.getOutPaySetting()
    },
    getPayInfo(id) {
      let payinfo = {}
      for (let index = 0; index < this.subMchList.length; index++) {
        const item = this.subMchList[index]
        if (item.value === id) {
          payinfo = item
          break
        }
      }
      this.selectPayinfo = payinfo
      this.getOrgsList(payinfo)
      return payinfo
    },
    // 出金设置
    changeSubMchHandle(e) {
      this.getPayInfo(e)
      this.getOutPaySetting()
    },
    // 获取出金配置
    async getOutPaySetting() {
      const params = {
        company_id: this.userInfo.company_id,
        organization_id: this.organization,
        outpay_type: this.outpayForm.outpayType,
        sub_mch_id: this.outpayForm.subMchId,
        payinfo_id: this.selectPayinfo.payinfo_id
      }
      if (!params.payinfo_id) {
        return this.$message.error('请选择账户')
      }
      this.isLoading = true
      const res = await this.$apis.apiBackgroundAbcOutputOutputGetSettingPost(params)
      this.isLoading = false
      if (res.code === 0) {
        this.outpayForm.outpayType = res.data.outpay_type
        switch (this.outpayForm.outpayType) {
          case 'auto':
            this.outpayForm.reservedMoney = divide(res.data.reserved_money)
            this.outpayForm.outpayDay = res.data.outpay_day
            break
          case 'manual':
            this.outpayForm.reservedMoneyEnable = res.data.reserved_money_enable
            if (res.data.reserved_money_enable) {
              this.outpayForm.manualrReservedMoney = divide(res.data.manual_reserved_money)
            }
            break
          case 'fixed_date':
            this.outpayForm.fixedDateList = res.data.fixed_date_list
            this.outpayForm.reservedMoney = divide(res.data.reserved_money)
            break
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    checkSettingForm() {
      this.$refs.outpayFormRef.validate(valid => {
        if (valid) {
          if (this.isLoading) return;
          this.outpaySettingClick()
        } else {
          console.log(valid)
        }
      })
    },
    // 设置出金方式
    async outpaySettingClick() {
      const params = {
        company_id: this.userInfo.company_id,
        organization_id: this.organization,
        outpay_type: this.outpayForm.outpayType,
        sub_mch_id: this.outpayForm.subMchId,
        payinfo_id: this.selectPayinfo.payinfo_id
      }
      if (this.outpayForm.outpayType === 'auto') {
        params.reserved_money = times(this.outpayForm.reservedMoney)
        params.outpay_day = this.outpayForm.outpayDay
      }
      if (this.outpayForm.outpayType === 'manual') {
        params.reserved_money_enable = this.outpayForm.reservedMoneyEnable
        if (params.reserved_money_enable) {
          params.manual_reserved_money = times(this.outpayForm.manualrReservedMoney)
        }
      }
      if (this.outpayForm.outpayType === 'fixed_date') {
        params.fixed_date_list = this.outpayForm.fixedDateList
        params.reserved_money = times(this.outpayForm.reservedMoney)
      }
      this.isLoading = true
      const res = await this.$apis.apiBackgroundAbcOutputOutputCreateOrUpdateSettingPost(params)
      this.isLoading = false
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.getOutPaySetting()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getOutputDataList()
    },
    // 结账报表
    gotoBilling() {
      this.$router.push({
        name: 'MerchantAccountBillingDetails',
        query: {
          sub_mch_id: this.selectPayinfo.value,
          payinfo_id: this.selectPayinfo.payinfo_id
        }
      })
    },
    // 导出
    gotoExport() {
      const option = {
        type: 'SettlementDetails',
        params: {
          ...this.formatQueryParams(this.searchFormSetting),
          company_id: this.userInfo.company_id,
          organization_id: this.organization,
          page: this.currentPage,
          page_size: this.pageSize
        }
      }
      this.exportHandle(option)
    },
    gotoPrint() {
      const { href } = this.$router.resolve({
        name: 'Print',
        query: {
          print_date_state: true,
          print_type: this.printType,
          print_title: '结算明细表',
          result_key: 'results', // 返回的数据处理的data keys
          api: 'apiBackgroundReportCenterDataReportOutputOrderListPost', // 请求的api
          show_summary: false, // 合计
          show_print_header_and_footer: true, // 打印页头页尾
          table_setting: JSON.stringify(this.tableSetting),
          current_table_setting: JSON.stringify(this.currentTableSetting),
          collect: JSON.stringify(this.collect),
          push_summary: false, // 合计添加到到table数据最后
          params: JSON.stringify({
            ...this.formatQueryParams(this.searchFormSetting),
            company_id: this.userInfo.company_id,
            organization_id: this.organization,
            page: 1,
            page_size: this.totalCount || 10
          })
        }
      })
      window.open(href, '_blank')
    },
    // 根据账户获取层级
    async getOrgsList(v) {
      if (!v) {
        return ''
      }
      var list = v.use_orgs || []
      if (list) {
        this.orgs = list.join(',')
      }
    }
  }
}
</script>
<style lang="scss" scoped>
@import './style/common.scss';
.settlement-details-wrapper {
  .tab-item-setting {
    padding-bottom: 10px;
    background-color: #fff;
  }
  .total-orgs {
    color: #606266;
    font-weight: bold
  }
}
.ps-text-gray {
 color: #909090;
 font-size: 12px;
}
.inline{
  display: inline;
}
</style>
