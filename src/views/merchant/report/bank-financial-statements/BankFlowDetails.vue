<template>
  <div>
    <div class="settlement-details-wrapper container-wrapper">
      <refresh-tool @refreshPage="refreshHandle" />
      <div class="tab-box">
        <el-radio-group v-model="tabType" @change="changeTabHandle" class="ps-radio-btn">
          <el-radio-button v-for="tab in tabTypeList" :key="tab.value" :label="tab.value">{{ tab.label }}</el-radio-button>
        </el-radio-group>
      </div>
      <div class="tab-item m-t-20">
        <search-form
          ref="searchRef"
          :loading="isLoading"
          @search="searchHandle"
          :form-setting="searchFormSetting"
          label-width="120px"
          :autoSearch="false"
        ></search-form>

        <div class="table-wrapper">
          <div class="table-header">
            <div class="table-title">数据列表</div>
            <div class="align-r">
              <el-button size="mini" @click="gotoExport" v-permission="['background_order.finance_report.sub_mch_order_detail_list_export']">导出Excel</el-button>
              <button-icon color="plain" @click="gotoPrint">打印</button-icon>
              <button-icon color="plain" @click="openPrintSetting">报表设置</button-icon>
            </div>
          </div>

          <!-- table-content start -->
          <div class="table-content">
            <custom-table
              border
              v-loading="isLoading"
              :table-data="tableData"
              :table-setting="currentTableSetting"
              ref="tableData"
              style="width: 100%"
              stripe
              :isFirst="isFirstSearch"
              header-row-class-name="ps-table-header-row" />
          </div>
          <!-- table content end -->
          <!-- 统计 start -->
          <table-statistics v-if="tabType === 'flow'" :statistics="collect" />
          <!-- end -->
          <!-- 提示-->
          <div v-if="payWayMode == '消费清分'" class="total font-14">
            储值支付的订单入账金额为订单的储值钱包动账金额
          </div>
          <div v-if="payWayMode == '充值清分'" class="total font-14">
            农行支付（用于充值）的订单入账金额为用户充值的实际支付金额
          </div>
          <!-- 分页 start -->
          <pagination
            :onPaginationChange="onPaginationChange"
            :current-page.sync="currentPage"
            :page-size.sync="pageSize"
            :layout="'total, prev, pager, next, jumper'"
            :total="totalCount"
          ></pagination>
          <!-- 分页 end -->
        </div>
      </div>
    </div>
    <print-setting
      v-if="dialogPrintVisible"
      :extraParams="{ printType: printType }"
      :tableSetting="tableSetting"
      :defaultCheckedSetting="currentTableSetting"
      :show.sync="dialogPrintVisible"
      @confirm="confirmPrintDialog"
    ></print-setting>
  </div>
</template>

<script>
import { BANK_FLOW_DETAILS, BANK_FLOW_REFUND_DETAILS, PAYWAY_DEFAULT, PAYWAY_CHONGZHI, PAYWAY_XIAOFEI } from './constantsConfig'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import report from '@/mixins/report' // 混入
import { mapGetters } from 'vuex'
import { to, deepClone } from '@/utils'

export default {
  name: 'BankFlowDetails',
  mixins: [exportExcel, report],
  data() {
    return {
      isLoading: false,
      tabType: '',
      tabTypeList: [
        { label: '交易订单', value: 'flow', permissions: 'background_order.finance_report.sub_mch_order_detail_list' },
        { label: '退款订单', value: 'refund', permissions: 'background_order.finance_report.sub_mch_order_detail_list' }
      ],
      tableSetting: [],
      flowSetting: [
        // { label: '序号', key: 'index', type: 'index' },
        { label: '交易时间', key: 'create_time' },
        { label: '总单号', key: 'order_trade_no' },
        { label: '订单号', key: 'order_no' },
        { label: '订单支付金额', key: 'total_fee', type: 'money' },
        { label: '手续费', key: 'rate_fee', type: 'money' },
        { label: '订单入账金额', key: 'real_outpay_fee', type: 'money' },
        { label: '支付方式', key: 'order_type_alias' },
        { label: '订单状态', key: 'status_alias' }
      ],
      refundSetting: [
        // { label: '序号', key: 'index', type: 'index' },
        { label: '交易时间', key: 'create_time' },
        { label: '总单号', key: 'order_trade_no' },
        { label: '订单号', key: 'order_no' },
        { label: '退款订单号', key: 'refund_order_no' },
        { label: '订单支付金额', key: 'total_fee', type: 'money' },
        { label: '退款金额', key: 'refund_fee', type: 'money' },
        { label: '支付方式', key: 'order_type_alias' }
      ],
      tableData: [],
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页

      searchFormSetting: deepClone(BANK_FLOW_DETAILS),
      collect: [
        { key: 'total_pay_fee', value: 0, label: '订单支付金额：￥', type: 'money' },
        { key: 'total_rate_fee', value: 0, label: '手续费：￥', type: 'money' },
        { key: 'total_income_fee', value: '', label: '订单入账金额：￥', type: 'money' }
      ], // 统计
      printType: 'BankFlowDetailsFlow',
      isFirstSearch: true,
      payWayMode: '' // 消费清分模式，
    }
  },
  computed: {
    ...mapGetters(['userInfo', 'organization', 'allPermissions'])
  },
  created() {
    this.initLoad()
  },
  mounted() {
    if (this.tabType === 'flow') {
      this.tableSetting = this.flowSetting
    } else {
      this.tableSetting = this.refundSetting
    }
    this.initPrintSetting()
    this.initTabList()
  },
  methods: {
    async initLoad() {
      // this.getOrderList()
      await this.getSubMchList()
      this.modifyOrGetSettlementType()
    },
    // 初始页面权限
    initTabList() {
      // let result = this.tabTypeList.slice(0)
      let result = []
      this.tabTypeList.forEach(v => {
        if (this.allPermissions.includes(v.permissions)) {
          result.push(v)
        }
      })
      this.tabTypeList = result
      this.tabType = this.tabTypeList.length ? this.tabTypeList[0].value : ''
      this.setTabDataHandle(this.tabType)
    },
    async refreshHandle() {
      this.$refs.searchRef.resetForm()
      this.tableData = []
      this.currentPage = 1
      await this.getSubMchList()
      this.initTabList()
      this.isFirstSearch = true
      // this.getOrderList()
    },

    async searchHandle(e) {
      if (e && e === 'search') {
        this.currentPage = 1
        this.getOrderList()
        this.isFirstSearch = false
      }
    },
    changeTabHandle(e) {
      let subMachId = this.searchFormSetting.sub_mch_id.value
      this.setTabDataHandle(e)
      this.searchFormSetting.sub_mch_id.value = subMachId
      this.initPrintSetting()
      this.isFirstSearch = true
      this.tableData = []
      // 等
      // this.$nextTick(() => {
      //   this.getOrderList()
      // })
    },
    setTabDataHandle(e) {
      if (e === 'flow') {
        this.tableSetting = this.flowSetting
        this.searchFormSetting = deepClone(BANK_FLOW_DETAILS)
        this.printType = 'BankFlowDetailsFlow'
      } else if (e === 'refund') {
        this.tableSetting = this.refundSetting
        this.searchFormSetting = deepClone(BANK_FLOW_REFUND_DETAILS)
        this.printType = 'BankFlowDetailsRefund'
      }
      this.searchFormSetting.sub_mch_id.dataList = this.subMchList
      this.setPayWayList(this.payWayMode)
      this.initPrintSetting()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null) {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 请求账户数据
    async getSubMchList() {
      // const params = formatQueryParams(this.searchFormSetting, this.currentPage, this.pageSize)
      const params = {
        company_id: this.userInfo.company_id,
        organization_id: this.organization
      }
      this.isLoading = true
      const res = await this.$apis.apiBackgroundAbcOutputOutputGetAbcPayinfoPost(params)
      this.isLoading = false
      if (res.code === 0) {
        if (res.data && res.data.length) {
          this.subMchList = res.data.map((v, i) => {
            if (i === 0) {
              this.searchFormSetting.sub_mch_id.value = v.sub_mch_id
              this.selectPayinfo = v
            }
            return { label: v.sub_mch_id, id: v.payinfo_id, value: v.sub_mch_id }
          })
          this.searchFormSetting.sub_mch_id.dataList = this.subMchList
          console.log('请求完的数据', this.searchFormSetting.sub_mch_id.dataList)
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    getOrderList() {
      if (this.tabType === 'flow') {
        this.getSubMchOrder()
      } else if (this.tabType === 'refund') {
        this.getSubMchRefundOrder()
      }
    },
    // 请求列表数据
    async getSubMchOrder() {
      if (!this.searchFormSetting.sub_mch_id.value) {
        return this.$message.error('账号数据为空！')
      }
      this.isLoading = true
      const res = await this.$apis.apiBackgroundReportCenterDataReportSubMchOrderDetailListPost({
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      })
      this.isLoading = false
      this.tableData = []
      if (res.code === 0) {
        this.totalCount = res.data.count
        this.tableData = res.data.results
        // 统计
        this.setCollectData(res)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取退款订单数据
    async getSubMchRefundOrder() {
      if (!this.searchFormSetting.sub_mch_id.value) {
        return this.$message.error('账号数据为空！')
      }
      this.isLoading = true
      const res = await this.$apis.apiBackgroundReportCenterDataReportSubMchOrderDetailRefundListPost({
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      })
      this.isLoading = false
      this.tableData = []
      if (res.code === 0) {
        this.totalCount = res.data.count
        this.tableData = res.data.results
        // 统计
        this.setCollectData(res)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getOrderList()
    },
    // 导出
    gotoExport() {
      const option = {
        type: this.printType,
        params: {
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        }
      }
      if (this.tabType === 'flow') {
        option.url = 'apiBackgroundReportCenterDataReportSubMchOrderDetailListExportPost'
      } else {
        option.url = 'apiBackgroundReportCenterDataReportSubMchOrderDetailRefundListExportPost'
      }
      this.exportHandle(option)
    },
    gotoPrint() {
      const params = this.formatQueryParams(this.searchFormSetting)
      const { href } = this.$router.resolve({
        name: "Print",
        query: {
          print_date_state: true,
          print_type: this.printType,
          print_title: '交易流水明细',
          result_key: 'results', // 返回的数据处理的data keys
          api: this.tabType === 'flow' ? 'apiBackgroundReportCenterDataReportSubMchOrderDetailListPost' : 'apiBackgroundReportCenterDataReportSubMchOrderDetailRefundListPost', // 请求的api
          show_summary: false, // 合计
          show_print_header_and_footer: true, // 打印页头页尾
          table_setting: JSON.stringify(this.tableSetting),
          current_table_setting: JSON.stringify(this.currentTableSetting),
          collect: JSON.stringify(this.collect),
          push_summary: false, // 合计添加到到table数据最后
          params: JSON.stringify({
            ...params,
            page: 1,
            page_size: this.totalCount || 10
          })
        }
      });
      window.open(href, "_blank");
    },
    // 获取清分
    async modifyOrGetSettlementType() {
      const [err, res] = await to(this.$apis.apiBackgroundReportCenterDataReportGetComSettlementTypePost({}))
      if (err) {
        this.setPayWayList()
        return this.$message.success(err.message)
      }
      if (res && res.code === 0) {
        var data = res.data || {}
        if (data && Reflect.has(data, "name")) {
          this.payWayMode = data.name || ''
          console.log("this.payWayMode", this.payWayMode);
        }
        this.setPayWayList(this.payWayMode)
      } else {
        this.$message.error(res.msg)
        this.setPayWayList()
      }
    },
    //  设置支付方式
    setPayWayList(type) {
      switch (type) {
        case "充值清分":
          this.searchFormSetting.order_type.dataList = deepClone(PAYWAY_CHONGZHI)
          break;
        case "消费清分":
          this.searchFormSetting.order_type.dataList = deepClone(PAYWAY_XIAOFEI)
          break;
        default:
          this.searchFormSetting.order_type.dataList = deepClone(PAYWAY_DEFAULT)
          break;
      }
    }
  }
}
</script>
<style lang="scss" scoped>
@import './style/common.scss';
.settlement-details-wrapper{
  .tab-item-setting{
    padding-bottom: 10px;
    background-color: #fff;
  }
  .font-14{
    font-size: 14px;
  }
}
</style>
