import * as dayjs from 'dayjs'

export const recentSevenDayTime = [
  dayjs()
    .subtract(7, 'day')
    .format('YYYY-MM-DD HH:mm:ss'),
  dayjs().format('YYYY-MM-DD HH:mm:ss')
]

export const recentSevenDay = [
  dayjs()
    .subtract(7, 'day')
    .format('YYYY-MM-DD'),
  dayjs().format('YYYY-MM-DD')
]

const bankStatus = [
  { label: '出金中', value: 'ORDER_OUTPAYING' },
  { label: '出金失败', value: 'ORDER_OUTPAY_FAILED' },
  { label: '出金状态未知', value: 'ORDER_OUTPAY_UNKNOWN' },
  { label: '出金成功', value: 'ORDER_OUTPAY' },
  { label: '未出金', value: 'ORDER_OUTPAY_NO' },
  { label: '无需出金', value: 'ORDER_OUTPAY_NO_NEED' }
]

const orderStatus = [
  { label: '支付成功', value: 'ORDER_SUCCESS' },
  // { label: '支付中', value: 'ORDER_PAYING' },
  { label: '支付失败', value: 'ORDER_FAILED' }
  // { label: '退款中', value: 'ORDER_REFUNDING' },
  // { label: '全额退款', value: 'ORDER_ALL_REFUND' }
]

const refundOrderStatus = [
  // { label: '退款中', value: 'ORDER_REFUNDING' },
  { label: '部分退款', value: 'ORDER_PART_REFUND' },
  { label: '全额退款', value: 'ORDER_ALL_REFUND' }
]

const orderType = [
  { label: '农行支付（用于充值）', value: 'merchant' },
  { label: '微信支付（JSAPI）', value: 'jsapi' },
  { label: '微信支付（B扫C）', value: 'wechat_micropay' },
  { label: '支付宝支付（B扫C）', value: 'ali_micropay' },
  { label: '农行掌银付款码（B扫C）', value: 'abc_micropay' },
  { label: '微信/支付宝支付（B扫C）', value: 'wechat_alipay' },
  { label: '授权支付', value: 'agent' },
  { label: '出金', value: 'out_payment' },
  { label: '快e付', value: 'fast_e_pay' }
]

// 结算明细表
export const SETTLEMENT_DETAILS = {
  select_time: {
    type: 'daterange',
    label: '清算时间',
    value: recentSevenDay
  },
  sub_mch_id: {
    type: 'select',
    value: '',
    label: '账户',
    dataList: []
  },
  status: {
    type: 'select',
    value: '',
    label: '状态',
    dataList: bankStatus,
    clearable: true
  }
}
console.log(recentSevenDay)
// 账户结账明细
export const ACCOUNT_BILLING_DETAILS = {
  select_time: {
    type: 'daterange',
    label: '经营时间',
    value: recentSevenDay
  },
  sub_mch_id: {
    type: 'select',
    value: '',
    label: '账户',
    dataList: []
  }
}

// 交易流水明细
export const BANK_FLOW_DETAILS = {
  select_time: {
    type: 'daterange',
    label: '日期',
    value: recentSevenDay
  },
  order_type: {
    type: 'select',
    value: '',
    label: '支付方式',
    dataList: orderType,
    clearable: true
  },
  status: {
    type: 'select',
    value: '',
    label: '订单状态',
    dataList: orderStatus,
    clearable: true
  },
  order_no: {
    type: 'input',
    value: '',
    label: '订单号',
    dataList: [],
    clearable: true
  },
  sub_mch_id: {
    type: 'select',
    value: '',
    label: '账户',
    dataList: []
  },
  org_ids: {
    type: 'organizationSelect',
    value: [],
    label: '组织',
    listNameKey: 'name',
    listValueKey: 'id',
    dataList: [],
    checkStrictly: true,
    multiple: true,
    collapseTags: true,
    clearable: true
  }
}

export const BANK_FLOW_REFUND_DETAILS = {
  select_time: {
    type: 'daterange',
    label: '日期',
    value: recentSevenDay
  },
  order_type: {
    type: 'select',
    value: '',
    label: '支付方式',
    dataList: orderType,
    clearable: true
  },
  status: {
    type: 'select',
    value: '',
    label: '订单状态',
    dataList: refundOrderStatus,
    clearable: true
  },
  order_no: {
    type: 'input',
    value: '',
    label: '订单号',
    dataList: [],
    clearable: true
  },
  refund_no: {
    type: 'input',
    value: '',
    label: '退款订单号',
    dataList: [],
    clearable: true
  },
  sub_mch_id: {
    type: 'select',
    value: '',
    label: '账户',
    dataList: []
  },
  org_ids: {
    type: 'organizationSelect',
    value: [],
    label: '组织',
    listNameKey: 'name',
    listValueKey: 'id',
    dataList: [],
    checkStrictly: true,
    multiple: true,
    collapseTags: true,
    clearable: true
  }
}
// 默认清分支付方式
export const PAYWAY_DEFAULT = [
  { label: '农行支付（用于充值）', value: ['merchant'] },
  // { label: '农行支付（授权代扣支付）', value: [''] },
  { label: '农行支付（农行卡授权代扣支付）', value: ['abc_micropay'] },
  { label: '农行支付（快E付支付）', value: ['fast_e_pay'] }
  // { label: '农行支付（数币C扫B支付）', value: [''] },
  // { label: '农行支付（B扫C）', value: ['wechat_micropay', 'ali_micropay', 'abc_micropay'], type: 'wechat_micropay' }
]
// 充值清分支付方式
export const PAYWAY_CHONGZHI = [
  { label: '农行支付（用于充值）', value: ['merchant'] }
]

// 消费清分支付方式
export const PAYWAY_XIAOFEI = [
  { label: '储值支付', value: ['chuzhi_pay'] },
  // { label: '农行支付（授权代扣支付）', value: [''] },
  { label: '农行支付（农行卡授权代扣支付)', value: ['abc_micropay'] },
  { label: '农行支付（快E付支付）', value: ['fast_e_pay'] }
  // { label: '农行支付（数币C扫B支付）', value: [''] },
  // { label: '农行支付（B扫C）', value: ['wechat_micropay', 'ali_micropay', 'abc_micropay'], type: 'wechat_micropay' }
]
