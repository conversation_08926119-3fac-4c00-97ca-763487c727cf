<template>
  <div>
    <div class="booking-meal-wrapper container-wrapper">
      <refresh-tool @refreshPage="refreshHandle" />
      <search-form
        ref="searchRef"
        :loading="isLoading"
        @search="searchHandle"
        label-width="120px"
        :form-setting="searchFormSetting"
        :autoSearch="false"
      ></search-form>

      <div class="table-wrapper">
        <div class="table-header">
          <div class="table-title">数据列表</div>
          <div class="align-r">
            <el-button size="mini" @click="gotoExport" v-permission="['background_order.finance_report.sub_mch_order_list_export']">导出Excel</el-button>
            <button-icon color="plain" @click="gotoPrint">打印</button-icon>
            <button-icon color="plain" @click="openPrintSetting">报表设置</button-icon>
          </div>
        </div>

        <!-- table-content start -->
        <div class="table-content">
          <custom-table
            border
            v-loading="isLoading"
            :table-data="tableData"
            :table-setting="currentTableSetting"
            ref="tableData"
            style="width: 100%"
            stripe
            :isFirst="isFirstSearch"
            header-row-class-name="ps-table-header-row" />
        </div>
        <!-- table content end -->
        <!-- 统计 start -->
        <table-statistics :statistics="collect" />
        <!-- 显示层级 -->
        <div class="m-l-20 total-orgs font-size-14">账户适用组织：{{orgs}}</div>
        <!-- end -->
         <!-- 提示-->
         <div v-if="payWayMode == '消费清分'" class="total font-14">
          订单金额为当天消费订单的储值钱包动账金额+使用农行渠道支付的消费订单实付金额，退款金额为当日消费退款的储值动账金额+原路退回农行账户的金额
         </div>
         <div v-if="payWayMode == '默认清分'" class="total font-14">
          订单金额为当天充值+消费时，使用农行渠道支付的实付金额，退款金额为原路退回农行账户的金额
         </div>
        <!-- 分页 start -->
        <pagination
          :onPaginationChange="onPaginationChange"
          :current-page.sync="currentPage"
          :page-size.sync="pageSize"
          :layout="'total, prev, pager, next, jumper'"
          :total="totalCount"
        ></pagination>
        <!-- 分页 end -->
      </div>
    </div>
    <print-setting
      v-if="dialogPrintVisible"
      :extraParams="{ printType: printType }"
      :tableSetting="tableSetting"
      :defaultCheckedSetting="currentTableSetting"
      :show.sync="dialogPrintVisible"
      @confirm="confirmPrintDialog"
    ></print-setting>
  </div>
</template>

<script>
import { ACCOUNT_BILLING_DETAILS } from './constantsConfig'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import report from '@/mixins/report' // 混入
import { mapGetters } from 'vuex'
import { deepClone, to } from '@/utils'

export default {
  name: 'AccountBillingDetails',
  mixins: [exportExcel, report],
  data() {
    return {
      isLoading: false,
      tableSetting: [
        { label: '序号', key: 'index', type: 'index' },
        { label: '经营时间', key: 'date' },
        { label: '订单金额', key: 'success_total_fee', type: 'money' },
        { label: '订单金额费率', key: 'success_rate_fee', type: 'money' },
        { label: '退款金额', key: 'refund_total_fee', type: 'money' },
        { label: '退款金额费率', key: 'refund_rate_fee', type: 'money' },
        { label: '营收金额', key: 'real_total_fee', type: 'money' },
        { label: '营收费率', key: 'rate_real_total_fee', type: 'money' },
        { label: '实际营收金额', key: 'fin_total_fee', type: 'money' }
      ],
      tableData: [],
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页

      searchFormSetting: deepClone(ACCOUNT_BILLING_DETAILS),
      collect: [ // 统计
        { key: 'total_income_fee', value: 0, label: '营收金额=订单金额-退款金额=¥', type: 'money' },
        { key: 'total_income_rate', value: 0, label: '营收费率=订单金额费率-退款金额费率=￥', type: 'money' },
        { key: 'total_pay_fee', value: '', label: '实际营收金额：营收金额-营收费率', block: "true" }
      ],
      printType: 'AccountBillingDetails',
      isFirstSearch: true,
      payWayMode: '', // 清分模式
      orgs: '' // 适用组织
    }
  },
  computed: {
    ...mapGetters(['userInfo', 'organization'])
  },
  watch: {
    'searchFormSetting.sub_mch_id.value': function(val) {
      this.getPayInfo(val)
    }
  },
  created() {
    // this.initLoad()
  },
  async mounted() {
    this.initPrintSetting()
    await this.getSubMchList()
  },
  methods: {
    async initLoad() {
      // this.currentTableSetting = this.tableSetting
      this.getSubMachOrderList()
      this.modifyOrGetSettlementType()
    },

    refreshHandle() {
      this.$refs.searchRef.resetForm()
      this.tableData = []
      this.currentPage = 1
      this.isFirstSearch = true
      // this.getSubMachOrderList()
    },

    async searchHandle(e) {
      if (e && e === 'search') {
        this.currentPage = 1
        this.getSubMachOrderList()
        this.isFirstSearch = false
      }
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null) {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 请求账户数据
    async getSubMchList() {
      // const params = formatQueryParams(this.searchFormSetting, this.currentPage, this.pageSize)
      const params = {
        company_id: this.userInfo.company_id,
        organization_id: this.organization
      }
      this.isLoading = true
      const res = await this.$apis.apiBackgroundAbcOutputOutputGetAbcPayinfoPost(params)
      this.isLoading = false
      if (res.code === 0) {
        if (res.data && res.data.length) {
          this.subMchList = res.data.map((v, i) => {
            if (i === 0) {
              this.searchFormSetting.sub_mch_id.value = v.sub_mch_id
              this.getOrgsList(v)
            }
            return { label: v.sub_mch_id, id: v.payinfo_id, value: v.sub_mch_id, use_orgs: v.use_orgs }
          })
          this.searchFormSetting.sub_mch_id.dataList = this.subMchList
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    // 请求列表数据
    async getSubMachOrderList() {
      if (!this.searchFormSetting.sub_mch_id.value) {
        return this.$message.error('账号数据为空！')
      }
      this.isLoading = true
      const res = await this.$apis.apiBackgroundReportCenterDataReportSubMchOrderListPost({
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      })
      this.isLoading = false
      if (res.code === 0) {
        this.totalCount = res.data.count
        this.tableData = res.data.results
        // 统计
        this.setCollectData(res)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getSubMachOrderList()
    },
    // 导出
    gotoExport() {
      const option = {
        type: 'AccountBillingDetails',
        url: 'apiBackgroundReportCenterDataReportSubMchOrderListExportPost',
        params: {
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        }
      }
      this.exportHandle(option)
    },
    gotoPrint() {
      const params = this.formatQueryParams(this.searchFormSetting)
      const { href } = this.$router.resolve({
        name: "Print",
        query: {
          print_date_state: true,
          print_type: this.printType,
          print_title: '账户结账明细',
          result_key: 'results', // 返回的数据处理的data keys
          api: 'apiBackgroundReportCenterDataReportSubMchOrderListPost', // 请求的api
          show_summary: false, // 合计
          show_print_header_and_footer: true, // 打印页头页尾
          table_setting: JSON.stringify(this.tableSetting),
          current_table_setting: JSON.stringify(this.currentTableSetting),
          collect: JSON.stringify(this.collect),
          push_summary: false, // 合计添加到到table数据最后
          params: JSON.stringify({
            ...params,
            page: 1,
            page_size: this.totalCount || 10
          })
        }
      });
      window.open(href, "_blank");
    },
    // 获取清分
    async modifyOrGetSettlementType() {
      const [err, res] = await to(this.$apis.apiBackgroundReportCenterDataReportGetComSettlementTypePost({}))
      if (err) {
        return this.$message.success(err.message)
      }
      if (res && res.code === 0) {
        var data = res.data || {}
        if (data && Reflect.has(data, "name")) {
          this.payWayMode = data.name || ''
          console.log("this.payWayMode", this.payWayMode);
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    // 根据账户获取层级
    async getOrgsList(v) {
      if (!v) {
        return ''
      }
      var list = v.use_orgs || []
      if (list) {
        this.orgs = list.join(',')
      }
    },
    // 获取选择的数据
    getPayInfo(id) {
      console.log("getPayInfo", id);
      let payinfo = {}
      for (let index = 0; index < this.subMchList.length; index++) {
        const item = this.subMchList[index]
        if (item.value === id) {
          payinfo = item
          break
        }
      }
      this.getOrgsList(payinfo)
      return payinfo
    }
  }
}
</script>
<style lang="scss" scoped>
  .font-14{
    font-size: 14px;
  }
.total-orgs {
  color: #606266;
  font-weight: bold
}
</style>
