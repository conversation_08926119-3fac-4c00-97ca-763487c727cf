<template>
  <div>
    <div class="booking-meal-wrapper container-wrapper">
      <refresh-tool @refreshPage="refreshHandle" />
      <search-form
        ref="searchRef"
        :loading="isLoading"
        @search="searchHandle"
        :form-setting="searchFormSetting"
        :autoSearch="false"
      ></search-form>

      <div class="table-wrapper">
        <div class="table-header">
          <div class="table-title">数据列表</div>
        </div>
        <div class="align-r">
          <el-button size="mini" @click="gotoExport" v-permission="['background_order.finance_report.person_payment_collect_list_export']">导出Excel</el-button>
          <button-icon color="plain" @click="gotoPrint">打印</button-icon>
          <button-icon color="plain" @click="openPrintSetting">报表设置</button-icon>
        </div>

        <!-- table-content start -->
        <div class="table-content">
          <custom-table
            border
            v-loading="isLoading"
            :table-data="tableData"
            :table-setting="currentTableSetting"
            ref="tableData"
            style="width: 100%"
            stripe
            :span-method="arraySpanMethod"
            :isFirst="isFirstSearch"
            header-row-class-name="ps-table-header-row"
          />
        </div>
        <!-- table content end -->
        <!-- 分页 start -->
        <pagination
          :onPaginationChange="onPaginationChange"
          :current-page.sync="currentPage"
          :page-size.sync="pageSize"
          :layout="'total, prev, pager, next, sizes, jumper'"
          :total="total"
        ></pagination>
        <!-- 分页 end -->
      </div>
    </div>
    <print-setting
      v-if="dialogPrintVisible"
      :extraParams="{ printType: printType }"
      :tableSetting="tableSetting"
      :defaultCheckedSetting="currentTableSetting"
      :show.sync="dialogPrintVisible"
      @confirm="confirmPrintDialog"
    ></print-setting>
  </div>
</template>

<script>
import { PERSONAL_CONSUMPTION_SUMMARY } from './constantsConfig'
import { debounce, deepClone } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import { mergeHandle, mergeRowAction } from '@/utils/table'
import report from '@/mixins/report' // 混入

export default {
  name: 'PersonalConsumptionSummary',
  mixins: [exportExcel, report],
  data() {
    return {
      isLoading: false,
      tableSetting: [
        { label: '姓名', key: 'name' },
        { label: '人员编号', key: 'person_no' },
        { label: '手机号', key: 'phone' },
        { label: '分组', key: 'payer_group' },
        { label: '部门', key: 'payer_department_group' },
        {
          label: '合计',
          key: 'collect',
          children: [
            { label: '消费总额', key: 'pay_fee', type: 'money' },
            { label: '餐补金额', key: 'food_subsidy_fee', type: 'money' },
            { label: '优惠金额', key: 'discount_fee', type: 'money' },
            { label: '抵扣金额', key: 'deduction_fee', type: 'money' },
            { label: '消费笔数', key: 'consume_count' },
            { label: '次数消费', key: 'jc_count' }
          ]
        }
      ],
      tableData: [],
      currentPage: 1,
      pageSize: 10,
      total: 0,
      searchFormSetting: deepClone(PERSONAL_CONSUMPTION_SUMMARY),
      rowMergeArrs: [],
      mergeOpts: {
        useKeyList: {
          person_no: ['name', 'person_no', 'phone', 'payer_group', 'payer_department_group']
        } // 是否根據固定key進行合并, 有值則根據key為唯一性進行字段表格合并，無則不做處理
        // mergeKeyList: [] // 通用的合并字段，根據值合并
      },
      printType: 'PersonalConsumptionSummary',
      isFirstSearch: true
    }
  },
  created() {
    this.initLoad(true)
  },
  async mounted() {
    await this.getMealType()
    this.getPechargeMethod()
  },
  methods: {
    async initLoad(isFirst) {
      if (!isFirst) {
        // await this.getOrgWallet()
        this.getPersonPaymentList()
      }
    },
    refreshHandle() {
      this.$refs.searchRef.resetForm()
      this.tableData = []
      this.currentPage = 1
      this.initLoad()
      this.isFirstSearch = true
    },
    // 节下流咯
    searchHandle: debounce(function(e) {
      if (e && e === 'search') {
        this.currentPage = 1
        this.getPersonPaymentList()
        this.isFirstSearch = false
      }
    }, 300),
    // 充值方式
    async getPechargeMethod() {
      const res = await this.$apis.apiBackgroundReportCenterDataReportPechargeMethod({
        page: 1,
        page_size: 999,
        org_ids: []
      })
      if (res.code === 0) {
        const result = []
        res.data.forEach(d => {
          Object.keys(d).forEach(key => result.push({ label: d[key], value: key }))
        })
        this.searchFormSetting.payway.dataList = result
      } else {
        this.$message.error(res.msg)
      }
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '') {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value && data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 获取餐段
    async getMealType() {
      const res = await this.$apis.apiBackgroundReportCenterDataReportGetMealTypePost({
        org_id: this.$store.getters.organization
      })
      if (res.code === 0) {
        if (res.data.meal_type_info) {
          let mealList = res.data.meal_type_info.map(v => {
            return {
              key: v.meal_type,
              label: v.meal_type_alias,
              children: [
                { label: '消费总额', key: v.meal_type + '_pay_fee', type: 'money' },
                { label: '餐补金额', key: v.meal_type + '_food_subsidy_fee', type: 'money' },
                { label: '优惠金额', key: v.meal_type + '_discount_fee', type: 'money' },
                { label: '抵扣金额', key: v.meal_type + '_deduction_fee', type: 'money' },
                { label: '消费笔数', key: v.meal_type + '_consume_count' },
                { label: '次数消费', key: v.meal_type + '_jc_count' }
              ]
            }
          })
          this.tableSetting.push(...mealList)
          // this.currentTableSetting = this.tableSetting
          this.initPrintSetting()
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取列表数据
    async getPersonPaymentList() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundReportCenterDataReportPersonPaymentCollectListPost(
        {
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        }
      )
      this.isLoading = false
      if (res.code === 0) {
        this.total = res.data.count
        this.tableData = res.data.results
        if (res.data.results.length) {
          // 设置合计的值
          this.setSummaryData(res)
        }
        this.rowMergeArrs = mergeHandle(this.tableData, this.mergeOpts)
      } else {
        this.$message.error(res.msg)
      }
    },
    arraySpanMethod({ row, column, rowIndex, columnIndex }) {
      let keys = Object.keys(this.mergeOpts.useKeyList)
      let useKey = this.mergeOpts.useKeyList && keys.length
      if (useKey) {
        for (const key in this.mergeOpts.useKeyList) {
          if (this.mergeOpts.useKeyList[key].includes(column.property)) {
            return mergeRowAction(this.rowMergeArrs, column.property, rowIndex, columnIndex)
          }
        }
      }
      if (
        this.mergeOpts.mergeKeyList &&
        this.mergeOpts.mergeKeyList.length &&
        this.mergeOpts.mergeKeyList.includes(column.property)
      ) {
        return mergeRowAction(this.rowMergeArrs, column.property, rowIndex, columnIndex)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getPersonPaymentList()
    },
    gotoExport() {
      const option = {
        type: 'PersonalConsumptionSummary',
        params: {
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        }
      }
      this.exportHandle(option)
    },
    gotoPrint() {
      const params = this.formatQueryParams(this.searchFormSetting)
      const { href } = this.$router.resolve({
        name: "Print",
        query: {
          print_date_state: true,
          print_type: this.printType,
          print_title: '个人消费汇总',
          result_key: 'results', // 返回的数据处理的data keys
          api: 'apiBackgroundReportCenterDataReportPersonPaymentCollectListPost', // 请求的api
          show_summary: false, // 合计
          show_print_header_and_footer: true, // 打印页头页尾
          table_setting: JSON.stringify(this.tableSetting),
          current_table_setting: JSON.stringify(this.currentTableSetting),
          push_summary: false, // 合计添加到到table数据最后
          params: JSON.stringify({
            ...params,
            page: 1,
            page_size: this.total ? this.total : 10
          })
        }
      });
      window.open(href, "_blank");
    }
  }
}
</script>
<style lang="scss" scoped>
.el-table {
  text-align: center;
  font-size: 12px;
}
</style>
