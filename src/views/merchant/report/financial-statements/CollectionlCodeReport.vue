<template>
  <div>
    <div class="booking-meal-wrapper container-wrapper">
      <refresh-tool @refreshPage="refreshHandle" />
      <search-form
        ref="searchRef"
        :loading="isLoading"
        @search="searchHandle"
        :form-setting="searchFormSetting"
        :autoSearch="false"
      ></search-form>

      <div class="table-wrapper">
        <div class="table-header">
          <div class="table-title">数据列表</div>
          <div class="align-r">
            <button-icon color="plain" type="export" @click="gotoExport" v-permission="['background_order.finance_report.instore_payment_detail_list_export']">导出Excel</button-icon>
            <button-icon color="plain" @click="gotoPrint">打印</button-icon>
            <button-icon color="plain" @click="openPrintSetting">报表设置</button-icon>
          </div>
        </div>
        <!-- table-content start -->
        <div class="table-content">
          <custom-table
            border
            v-loading="isLoading"
            :table-data="tableData"
            :table-setting="currentTableSetting"
            ref="tableData"
            style="width: 100%"
            stripe
            :index="indexMethod"
            :isFirst="isFirstSearch"
            header-row-class-name="ps-table-header-row"
          />
        </div>
        <!-- table content end -->
        <!-- 统计 start -->
        <table-statistics :statistics="collect" />
        <!-- end -->
        <common-pagination
          ref="pagination"
          :total="total"
          :onPaginationChange="onPaginationChange"
        ></common-pagination>
      </div>
    </div>
    <print-setting
      v-if="dialogPrintVisible"
      :extraParams="{ printType: printType }"
      :tableSetting="tableSetting"
      :defaultCheckedSetting="currentTableSetting"
      :show.sync="dialogPrintVisible"
      @confirm="confirmPrintDialog"
    ></print-setting>
  </div>
</template>

<script>
import CommonPagination from '../../meal-management/booking-setting/CommonPagination.vue'
import { CollectionlCodeReportSearchForm } from './constantsConfig'
import { getRequestParams, deepClone } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import report from '@/mixins/report' // 混入

export default {
  name: 'DetailTotalList',
  components: {
    CommonPagination
  },
  mixins: [exportExcel, report],
  data() {
    return {
      isLoading: false,
      tableSetting: [
        { label: '订单号', key: 'trade_no' },
        { label: '消费点', key: 'organization_name' },
        { label: '收款时间', key: 'pay_time' },
        { label: '消费类型', key: 'payment_order_type_alias' },
        { label: '餐段', key: 'meal_type_verbose' },
        { label: '支付方式', key: 'pay_method_alias' },
        { label: '收款金额', key: 'pay_fee', type: 'money' }
      ],
      tableData: [],
      page: 1,
      pageSize: 10,
      total: 0,
      searchFormSetting: deepClone(CollectionlCodeReportSearchForm),
      collect: [
        // 统计
        { key: 'total_pay_fee', value: 0, label: '收款总金额:￥', type: 'money' }
      ],
      currentTableSetting: [],
      dialogPrintVisible: false,
      printType: 'CollectionlCodeReport',
      isFirstSearch: true
    }
  },
  created() {
    this.initLoad(true)
  },
  mounted() {
    this.initPrintSetting()
  },
  methods: {
    initLoad(isFirst) {
      if (!isFirst) {
        this.getInstorePaymentDetailList()
        this.requestDeviceType()
        // this.userGroupList() // 分组
        // this.getWalletList() // 动账钱包
      }
    },

    refreshHandle() {
      this.$refs.searchRef.resetForm()
      this.$refs.pagination.handleCurrentChange(1, true)
      this.$refs.pagination.handleSizeChange(10, true)
      this.tableData = []
      this.isFirstSearch = true
      // this.onPaginationChange({ current: 1, pageSize: 10 })
    },

    async searchHandle(e) {
      if (e && e === 'search') {
        this.$refs.pagination.handleCurrentChange(1, true)
        this.$refs.pagination.handleSizeChange(10, true)
        this.onPaginationChange({ current: 1, pageSize: 10 })
        this.isFirstSearch = false
      }
    },
    // 导出 列表
    handleExport() {
      this.$confirm(`确定导出？`, '提示', {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true
      })
        .then(e => {
          const params = getRequestParams(this.searchFormSetting, 1, 9999999)
          this.$router.push({
            name: 'Excel',
            query: {
              type: 'PaymentOrderTotal',
              params: JSON.stringify(params)
            }
          })
        })
        .catch(e => {})
    },
    // 获取列表数据
    async getInstorePaymentDetailList() {
      const params = getRequestParams(this.searchFormSetting, this.page, this.pageSize)
      this.isLoading = true
      const res = await this.$apis.apiBackgroundReportCenterDataReportInstorePaymentDetailListPost(params)
      this.isLoading = false
      if (res.code === 0) {
        this.total = res.data.count
        this.tableData = res.data.result
        // 统计
        this.setCollectData(res)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 翻页
    onPaginationChange(data) {
      this.page = data.current
      this.pageSize = data.pageSize
      this.getInstorePaymentDetailList()
    },
    // 导出
    gotoExport() {
      const option = {
        type: 'ExportCollectionlCodeReport',
        params: getRequestParams(this.searchFormSetting, this.page, this.pageSize)
      }
      this.exportHandle(option)
    },
    gotoPrint() {
      const params = getRequestParams(this.searchFormSetting)
      const { href } = this.$router.resolve({
        name: 'Print',
        query: {
          print_date_state: true,
          print_type: this.printType,
          print_title: '收款码明细表',
          result_key: 'result', // 返回的数据处理的data keys
          api: 'apiBackgroundReportCenterDataReportInstorePaymentDetailListPost', // 请求的api
          show_print_header_and_footer: true, // 打印页头页尾
          table_setting: JSON.stringify(this.tableSetting),
          current_table_setting: JSON.stringify(this.currentTableSetting),
          collect: JSON.stringify(this.collect),
          push_summary: false, // 合计添加到到table数据最后
          params: JSON.stringify({
            ...params,
            page: 1,
            page_size: this.total ? this.total : 10
          })
        }
      })
      window.open(href, '_blank')
    }
  }
}
</script>
<style lang="scss" scoped></style>
