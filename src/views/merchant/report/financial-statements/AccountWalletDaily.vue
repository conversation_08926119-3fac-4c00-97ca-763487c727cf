<template>
  <div class="report-wrapper container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form
      ref="searchRef"
      :loading="isLoading"
      @search="searchHandle"
      :form-setting="searchFormSetting"
      :autoSearch="false"
    ></search-form>

    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <el-button size="mini" @click="gotoExport" v-permission="['background_order.finance_report.wallet_daily_list_export']">导出Excel</el-button>
          <button-icon color="plain" @click="gotoPrint">打印</button-icon>
          <button-icon color="plain" @click="openPrintSetting">报表设置</button-icon>
        </div>
      </div>

      <!-- table-content start -->
      <div class="table-content">
        <!-- <custom-table
          border
          v-loading="isLoading"
          :table-data="tableData"
          :table-setting="currentTableSetting"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
        /> -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
          :empty-text="isFirstSearch ? '暂无数据，请查询' : ''"
        >
          <table-column v-for="item in currentTableSetting" :key="item.key" :col="item">
            <template #dateStartFee="{ row }">
              <span :class="{'danger bold': row.error_date_start_fee}">{{ row.date_start_fee | formatMoney }}</span>
            </template>
            <template #dateEndFee="{ row }">
              <span :class="{'danger bold': row.error_date_end_fee}">{{ row.date_end_fee | formatMoney }}</span>
            </template>
          </table-column>
        </el-table>
      </div>
      <!-- table content end -->
      <!-- 统计 start -->
      <table-statistics :statistics="collect" />
      <!--个性化提示语 -->
      <div class="ps-red m-l-20 font-size-14">
        注: 如部分数据标红，请联系客服。
      </div>
      <!-- end -->
      <!-- 分页 start -->
      <pagination
        :onPaginationChange="onPaginationChange"
        :current-page.sync="currentPage"
        :page-size.sync="pageSize"
        :layout="'total, prev, pager, next, jumper'"
        :total="total"
      ></pagination>
      <!-- 分页 end -->
    </div>
    <print-setting
      v-if="dialogPrintVisible"
      :extraParams="{ printType: printType }"
      :tableSetting="tableSetting"
      :defaultCheckedSetting="currentTableSetting"
      :show.sync="dialogPrintVisible"
      @confirm="confirmPrintDialog"
    ></print-setting>
  </div>
</template>

<script>
import { ACCOUNT_WALLET_DAILY } from './constantsConfig'
import { debounce, deepClone } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import report from '@/mixins/report' // 混入
import * as dayjs from 'dayjs'

export default {
  name: 'AccountWalletDaily',
  mixins: [exportExcel, report],
  data() {
    return {
      isLoading: false,
      tableSetting: [
        { label: '日期', key: 'date', width: '100px' },
        { label: '期初余额', key: 'date_start_fee', type: 'slot', slotName: 'dateStartFee' },
        { label: '期末余额', key: 'date_end_fee', type: 'slot', slotName: 'dateEndFee' },
        { label: '充值金额', key: 'charge_fee', type: 'money' },
        { label: '充值退款', key: 'charge_refund_fee', type: 'money' },
        { label: '储值钱包消费金额', key: 'wallet_fee', type: 'money' },
        { label: '储值钱包消费退款', key: 'wallet_refund_fee', type: 'money' },
        { label: '提现金额', key: 'withdraw', type: 'money' },
        // { label: '退户金额（未提现）', key: 'xxxxx', type: 'money' },
        { label: '补贴发放金额', key: 'subsidy_add_fee', type: 'money' },
        { label: '补贴钱包消费金额', key: 'subsidy_fee', type: 'money' },
        { label: '补贴钱包退款金额', key: 'subsidy_refund_fee', type: 'money' },
        { label: '补贴钱包清零金额', key: 'subsidy_clear_fee', type: 'money' },
        { label: '补贴钱包冲销金额', key: 'charge_off_fee', type: 'money' },
        { label: '赠送金额', key: 'complimentary_add_fee', type: 'money' },
        { label: '赠送钱包消费金额', key: 'complimentary_fee', type: 'money' },
        { label: '赠送钱包清零金额', key: 'complimentary_clear_fee', type: 'money' },
        { label: '赠送钱包退款金额', key: 'complimentary_refund_fee', type: 'money' },
        { label: '储值钱包工本费', key: 'wallet_flat_cost_fee', type: 'money' },
        { label: '储值钱包工本费退款', key: 'wallet_flat_cost_refund_fee', type: 'money' }

        // { label: '消费金额', key: 'consume_fee', type: 'money' },
        // { label: '消费退款', key: 'refund_fee', type: 'money' },
        // { label: '提现金额', key: 'withdraw', type: 'money' },
        // { label: '赠送清零金额', key: 'complimentary_clear_fee', type: 'money' },
        // { label: '补贴清零金额', key: 'subsidy_clear_fee', type: 'money' }
      ],
      tableData: [],
      currentPage: 1,
      pageSize: 10,
      total: 0,
      searchFormSetting: deepClone(ACCOUNT_WALLET_DAILY),
      collect: [
        // 统计
        {
          key: 'total_consume_fee',
          value: '',
          label: '总消费合计：',
          class: 'origin',
          type: 'money'
        },
        {
          key: 'total_refund_fee',
          value: '',
          label: '总退款合计：',
          class: 'origin',
          type: 'money'
        },
        {
          key: 'text',
          value:
            '期末余额=期初余额+充值金额-充值退款-储值钱包消费金额+储值钱包退款金额-提现金额+补贴发放金额-补贴钱包消费金额+补贴钱包退款金额-补贴钱包清零金额-补贴钱包冲销金额+赠送金额-赠送钱包消费金额-赠送钱包清零金额+赠送钱包退款金额-储值钱包工本费+储值钱包工本费退费。',
          label: '',
          block: true
        }
      ],
      printType: 'AccountWalletDaily',
      dateList: {}, // 记录期初期末数据，用于分页的收尾数据
      isFirstSearch: true
    }
  },
  created() {
    this.initLoad(true)
  },
  mounted() {
    this.searchFormSetting.org_ids.value = [this.$store.getters.organization]
    this.initPrintSetting()
  },
  methods: {
    async initLoad(isFirst) {
      if (!isFirst) {
        // this.currentTableSetting = this.tableSetting
        // await this.getOrgWallet()
        this.getPechargeMethod()
        this.getPersonPaymentList()
      }
    },
    refreshHandle() {
      this.$refs.searchRef.resetForm()
      this.tableData = []
      this.currentPage = 1
      this.initLoad()
      this.isFirstSearch = true
    },
    // 节下流咯
    searchHandle: debounce(function(e) {
      if (e && e === 'search') {
        this.currentPage = 1
        this.getPersonPaymentList()
        this.isFirstSearch = false
      }
    }, 300),
    // 充值方式
    async getPechargeMethod() {
      const res = await this.$apis.apiBackgroundReportCenterDataReportPechargeMethod({
        page: 1,
        page_size: 999,
        org_ids: []
      })
      if (res.code === 0) {
        const result = []
        res.data.forEach(d => {
          Object.keys(d).forEach(key => result.push({ label: d[key], value: key }))
        })
        this.searchFormSetting.payway.dataList = [
          {
            label: '全部',
            value: ''
          },
          ...result
        ]
      } else {
        this.$message.error(res.msg)
      }
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '') {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value && data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 获取列表数据
    async getPersonPaymentList() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundReportCenterDataReportWalletDailyListPost({
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      })
      this.isLoading = false
      if (res.code === 0) {
        this.total = res.data.count
        let len = res.data.result.length
        let result = res.data.result
        result.map((item, index) => {
          // 手动存下当前分页首末的期初期末数据
          if (index === 0 || index === (len - 1)) {
            this.dateList[item.date] = {
              date_start_fee: item.date_start_fee,
              date_end_fee: item.date_end_fee
            }
          }
          // 上一条的日期，因为后台返回的数据是倒序
          let nextDate = dayjs(item.date).add(1, 'day').format('YYYY-MM-DD')
          // 计算当前期初的金额与上一天的期末金额是否相等，如果不相等则证明数据有误，需标红显示
          if (index > 0) {
            // 对比上一条的数据
            let nextItem = result[index - 1]
            // 当日期符合时进行金额的判断
            if (nextItem) {
              // 当上一条期末金额与当前的期初金额不同时
              if (nextItem.date === nextDate && nextItem.date_start_fee !== item.date_end_fee) {
                item.error_date_end_fee = true
                nextItem.error_date_start_fee = true
              }
            } else {
              // nextItem = this.dateList[nextDate]
              // if (nextItem && nextItem.date_start_fee !== item.date_end_fee) {
              //   item.error_date_end_fee = true
              //   nextItem.error_date_start_fee = true
              // }
            }
          } else { // 当index为0的时候，应该是分页的数据了
            let nextItem = this.dateList[nextDate]
            // 当日期符合时进行金额的判断
            if (nextItem) {
              // 当上一条期末金额与当前的期初金额不同时
              if (nextItem.date_start_fee !== item.date_end_fee) {
                item.error_date_end_fee = true
                // nextItem.error_date_start_fee = true
              }
            }
          }
          return item
        })
        this.tableData = deepClone(result)
        this.setCollectData(res)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getPersonPaymentList()
    },
    gotoExport() {
      const option = {
        type: 'AccountWalletDaily',
        params: {
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        }
      }
      this.exportHandle(option)
    },
    gotoPrint() {
      const params = this.formatQueryParams(this.searchFormSetting)
      const { href } = this.$router.resolve({
        name: 'Print',
        query: {
          print_date_state: true,
          print_type: this.printType,
          print_title: '账户钱包日报表',
          result_key: 'result', // 返回的数据处理的data keys
          api: 'apiBackgroundReportCenterDataReportWalletDailyListPost', // 请求的api
          show_summary: false, // 合计
          show_print_header_and_footer: true, // 打印页头页尾
          table_setting: JSON.stringify(this.tableSetting),
          current_table_setting: JSON.stringify(this.currentTableSetting),
          collect: JSON.stringify(this.collect),
          push_summary: false, // 合计添加到到table数据最后
          params: JSON.stringify({
            ...params,
            page: 1,
            page_size: this.total ? this.total : 10
          })
        }
      })
      window.open(href, '_blank')
    }
  }
}
</script>
<style lang="scss" scoped>
.el-table {
  text-align: center;
  font-size: 12px;
}
.bold{
  font-weight: bold;
}
</style>
