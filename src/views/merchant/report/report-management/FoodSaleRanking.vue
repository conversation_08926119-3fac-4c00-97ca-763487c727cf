<template>
  <div>
    <div class="food-sale-ranking container-wrapper">
      <refresh-tool @refreshPage="refreshHandle" />
      <search-form ref="searchRef" @search="searchHandle" :loading="isLoading" :form-setting="searchFormSetting" :autoSearch="false" @reset="resetHandler">
        <div class="searchref_top" slot="perv">
          <el-button
            v-for="(item, index) in tabsList"
            :key="index"
            :class="{ active: item.type === currentType }"
            @click="tabHandler(item)"
          >
            {{ item.name }}
          </el-button>
        </div>
        <div slot="append" class="ps-flex">
        <div class="el-form-item__label w-80 m-b-20">菜品分类</div>
        <food-category ref='foodCateGory' @input="changeCategory" style='width:200px' />
      </div>
      </search-form>
      <div class="table-wrapper">
        <div class="table-header">
          <div class="table-title">数据列表</div>
          <div class="align-r">
            <button-icon color="plain" type="export" @click="gotoExport" v-permission="['background_order.manage_report.food_payment_ranking_list_export']">导出Excel</button-icon>
            <button-icon color="plain" @click="gotoPrint">打印</button-icon>
            <button-icon color="plain" @click="openPrintSetting">报表设置</button-icon>
          </div>
        </div>
        <!-- table-content start -->
        <div class="table-content">
          <custom-table
            border
            v-loading="isLoading"
            :table-data="tableData"
            :table-setting="currentTableSetting"
            ref="tableData"
            style="width: 100%"
            stripe
            :index="indexMethod"
            @sort-change="sortChange"
            :isFirst="isFirstSearch"
            :default-sort="{ prop: 'sell_count', order: 'descending' }"
            header-row-class-name="ps-table-header-row"
          />
        </div>
        <!-- table content end -->
        <!-- 统计 start -->
        <table-statistics :statistics="collect" />
        <!-- end -->
        <!-- 分页 start -->
        <pagination
          :onPaginationChange="onPaginationChange"
          :current-page.sync="currentPage"
          :page-size.sync="pageSize"
          :layout="'total, prev, pager, next, jumper'"
          :total="total"
        ></pagination>
        <!-- 分页 end -->
      </div>
    </div>
    <print-setting
      v-if="dialogPrintVisible"
      :extraParams="{ printType: printType }"
      :tableSetting="tableSetting"
      :defaultCheckedSetting="currentTableSetting"
      :show.sync="dialogPrintVisible"
      @confirm="confirmPrintDialog"
    ></print-setting>
  </div>
</template>

<script>
import { FoodSaleRanking } from './constantsConfig'
import { deepClone, debounce } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import report from '@/mixins/report' // 混入
import FoodCategory from '../../home-page/components/FoodCategory.vue'

export default {
  name: 'DetailTotalList',
  mixins: [exportExcel, report],
  components: {
    FoodCategory
  },
  data() {
    return {
      isLoading: false,
      tableSetting: [
        { label: '消费点', key: 'org_name' },
        { label: '一级分类', key: 'sort_name' },
        { label: '二级分类', key: 'category_name' },
        { label: '菜品名称', key: 'food_name' },
        { label: '规格', key: 'spec' },
        { label: '单价', key: 'price', type: 'money' },
        {
          label: '销售金额',
          key: 'sell_money',
          type: 'money',
          sortable: 'custom'
        },
        { label: '销售量（份）', key: 'sell_count', sortable: 'custom' }
      ],
      tableData: [],
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      currentPage: 1,
      page: 1,
      pageSize: 10,
      total: 0,
      searchFormSetting: deepClone(FoodSaleRanking),
      collect: [
        // 统计
        { key: 'total_sell_fee', value: 0, label: '总销售金额:￥', type: 'money' },
        { key: 'total_count', value: 0, label: '总销售量:', type: '' },
        {
          key: 'text',
          value: '因涉及消费规则与套餐规则，销售数据仅做参考，销售数据没有减去退款。',
          label: '',
          block: true
        }
      ],
      currentTableSetting: [],
      dialogPrintVisible: false,
      printType: 'FoodSaleRanking',
      sort: {
        sort_type: 'descending',
        sort_name: 'sell_count'
      },
      tabsList: [
        {
          type: 'instore',
          name: '堂食'
        },
        {
          type: 'reservation',
          name: '预约'
        }
        // {
        //   type: 'report_meal',
        //   name: '报餐'
        // },
        // {
        //   type: 'buffet',
        //   name: '称重'
        // },
        // {
        //   type: 'other',
        //   name: '其他'
        // }
      ],
      currentType: 'instore',
      isFirstSearch: true,
      chooseCategory: []
    }
  },
  mounted() {
    // this.initLoad()
    this.currentTableSetting = this.tableSetting
  },
  methods: {
    initLoad() {
      this.initPrintSetting()
      this.getFoodPaymentRankingList()
      this.getFoodSortList()
      this.getFoodCategoryList()
    },
    refreshHandle() {
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.tableData = []
      this.isFirstSearch = true
      if (this.$refs.foodCateGory) {
        this.chooseCategory = null
        this.$refs.foodCateGory.reset()
      }
      this.initLoad()
    },
    // 节下流咯
    searchHandle: debounce(function(e) {
      if (e && e === 'search') {
        this.currentPage = 1
        this.isFirstSearch = false
        this.initLoad()
      }
    }, 300),
    // 排序
    sortChange(row) {
      if (row.order) {
        // this.$refs.searchRef.resetForm()
        this.sort = {
          sort_type: row.order ? row.order : 'descending',
          sort_name: row.prop
        }
        this.page = 1
        this.pageSize = 10
        this.getFoodPaymentRankingList()
      }
    },
    // tabs
    tabHandler(data) {
      this.currentType = data.type
      this.tableData = []
      this.searchHandle()
    },
    // 菜品二级分类
    async getFoodCategoryList() {
      const res = await this.$apis.apiBackgroundFoodFoodCategoryListPost({
        page: 1,
        page_size: 9999
      })
      if (res.code === 0) {
        this.searchFormSetting.food_category.dataList = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    // 导出 列表
    handleExport() {
      this.$confirm(`确定导出？`, '提示', {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true
      })
        .then(e => {
          this.$router.push({
            name: 'Excel',
            query: {
              type: 'PaymentOrderTotal',
              params: {
                ...this.formatQueryParams(this.searchFormSetting),
                page: this.currentPage,
                page_size: this.pageSize
              }
            }
          })
        })
        .catch(e => {})
    },
    // 获取列表数据
    async getFoodPaymentRankingList() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundReportCenterManageReportFoodPaymentRankingListPost({
        ...this.formatQueryParams(this.searchFormSetting),
        ...this.sort,
        payment_order_type: this.currentType,
        page: this.currentPage,
        page_size: this.pageSize
      })
      this.isLoading = false
      if (res.code === 0) {
        this.total = res.data.count
        this.tableData = res.data.result
        // 统计
        this.setCollectData(res)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 翻页
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getFoodPaymentRankingList()
    },
    // 导出
    gotoExport() {
      const option = {
        type: 'ExportFoodSaleRanking',
        params: {
          payment_order_type: this.currentType,
          ...this.sort,
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        }
      }
      this.exportHandle(option)
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '') {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value && data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      if (this.chooseCategory && this.chooseCategory.length > 0) {
        params.food_category = this.chooseCategory
      }
      return params
    },
    gotoPrint() {
      let setting = deepClone(this.tableSetting)
      setting.forEach(v => {
        if (v.sortable) {
          v.sortable = true
        }
      })
      let currentTableSettingDeepClone = deepClone(this.currentTableSetting)
      currentTableSettingDeepClone.forEach(v => {
        if (v.sortable) {
          v.sortable = true
        }
      })
      const { href } = this.$router.resolve({
        name: 'Print',
        query: {
          print_date_state: true,
          print_type: this.printType,
          print_title: '菜品销售排行',
          result_key: 'result', // 返回的数据处理的data keys
          api: 'apiBackgroundReportCenterManageReportFoodPaymentRankingListPost', // 请求的api
          show_print_header_and_footer: true, // 打印页头页尾
          table_setting: JSON.stringify(setting),
          current_table_setting: JSON.stringify(currentTableSettingDeepClone),
          collect: JSON.stringify(this.collect),
          push_summary: false, // 合计添加到到table数据最后
          params: JSON.stringify({
            ...this.formatQueryParams(this.searchFormSetting),
            payment_order_type: this.currentType,
            ...this.sort,
            page: 1,
            page_size: this.total ? this.total : 10
          })
        }
      })
      window.open(href, '_blank')
    },
    // 菜品选择修改
    changeCategory(value) {
      console.log("value", value);
      this.chooseCategory = value
    },
    /**
     * 重置筛选列表
     */
    resetHandler() {
      console.log("resetHandler");
      this.currentPage = 1;
      this.tableData = []
      if (this.$refs.foodCateGory) {
        this.chooseCategory = null
        this.$refs.foodCateGory.reset()
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.food-sale-ranking {
  .active {
    background-color: #ff9b45;
    color: #fff;
  }
  .searchref_top {
    margin-bottom: 10px;
    .el-button {
      width: 120px;
    }
  }
  .el-form-item__label {
    font-weight: 700;
  }
}
</style>
