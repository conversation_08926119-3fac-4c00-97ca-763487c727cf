<template>
  <!-- 添加/编辑 -->
  <dialog-message
    :show.sync="visible"
    :title="title"
    :loading.sync="isLoading"
    :width="width"
    class="FormDialog"
    @close="handlerClose"
    @cancel="clickCancleHandle"
    @confirm="clickConfirmHandle"
  >
    <el-form ref="formRef" :model="formData" :rules="formRules" label-width="0" :size="formSize">
      <el-form-item label="" class="" prop="date_type">
        <el-radio-group v-model="formData.date_type" @change="changeCycleType">
          <el-radio v-for="item in cycleTypes" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <!-- 按季度 -->
      <el-form-item v-if="formData.date_type !== 'DAY'" label="" prop="date">
        <el-select v-model="formData.date" placeholder="请选择">
          <el-option
            v-for="(item, index) in formData.date_type === 'WEEK' ? weekList : monthList"
            :key="index"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
    </el-form>
  </dialog-message>
  <!-- end -->
</template>

<script>
// 草稿箱
// import { integer } from '@/utils/validata'
import formData from '@/filters/modules/formData';
import { CYCLE_TYPE_LIST, QUARTER_LIST, WEEK_LIST, MONTH_LIST } from '../constants'

export default {
  name: 'CycleDialog',
  components: {},
  props: {
    showdialog: Boolean,
    loading: Boolean,
    type: {
      type: String,
      default: 'add' // add：添加物资，purchase: 采购单
    },
    title: {
      type: String,
      default: '结算周期'
    },
    width: {
      type: String,
      default: '460px'
    },
    formSize: {
      type: String,
      default: 'medium'
    },
    InfoData: {
      type: Object,
      default() {
        return {}
      }
    },
    // 调用接口的默认参数
    params: {
      type: Object,
      default() {
        return {}
      }
    },
    supplierManageId: {
      type: Number,
      default: 0
    },
    oldFormData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  // mixins: [activatedLoadData],
  data() {
    return {
      isLoading: false,
      selectList: [],
      formData: {
        date_type: 'DAY',
        date: ''
      },
      formRules: {
        date_type: [{ required: true, message: '请选择', trigger: 'change' }],
        date: [{ required: true, message: '请选择', trigger: 'change' }]
      },
      // 查看弹窗
      showViewDialog: false,
      remoteLoading: false,
      cycleTypes: CYCLE_TYPE_LIST, // 周期列表
      quarterList: QUARTER_LIST, // 季度列表
      datePickerType: 'date', // 日期组件类型 date、week、month
      weekList: WEEK_LIST, // 周列表
      monthList: []
    }
  },
  computed: {
    visible: {
      get() {
        return this.showdialog
      },
      set(val) {
        this.$emit('update:showdialog', val)
      }
    },
    // 当前选择数量
    selectLenght() {
      return this.selectList.length
    }
  },
  watch: {
    showdialog(val) {
      if (val) {
        this.init()
      }
    }
  },
  created() {
  },
  mounted() {},
  methods: {
    // 初始化
    async init() {
      // this.getDraftBoxList()
      // 设置选中table状态
      // this.$refs.tableData.toggleRowSelection(row, true)
      if (this.oldFormData && Object.keys(this.oldFormData).length) {
        this.formData = { ...this.oldFormData }
      } else {
        this.formData.date_type = 'DAY'
        this.formData.date = ''
      }
      setTimeout(() => {
        this.$refs.formRef.clearValidate()
      }, 100)
      this.setMonthList()
    },
    clickConfirmHandle() {
      this.$refs.formRef.validate(valid => {
        if (valid) {
          console.log('通过了')
          this.$apis.apiBackgroundDrpSupplierManageSetSettlementIntervalPost({
            supplier_manage_id: this.supplierManageId,
            settlement_interval: {
              date_type: this.formData.date_type,
              date: this.formData.date || undefined
            }
          }).then(res => {
            if (res.code === 0) {
              this.$message.success('设置成功')
              this.clickCancleHandle()
            } else {
              this.$message.error(res.msg)
            }
          })
        }
      })
    },
    clickCancleHandle() {
      this.formData = {
        date_type: 'DAY',
        date: ''
      }
      this.$refs.formRef.clearValidate()
      this.visible = false
      this.$emit('confirmForm')
    },
    handlerClose(e) {
      this.isLoading = false
      if (this.$refs.formRef) {
        // 请除table选中状态
        this.$refs.formRef.resetFields()
      }
      // this.visible = false
      // this.$emit('close')
    },
    async changeCycleType(e) {
      switch (e) {
        case 'DAY':
          this.datePickerType = 'date'
          break;
        case 'WEEK':
          this.datePickerType = 'week'
          break;
        case 'MONTH':
        this.datePickerType = 'month'
          break;
        case 'QUARTER':

          break;
      }
    },
    setMonthList() {
      for (let i = 1; i <= 31; i++) {
        let obj = {
          label: `${i}日`,
          value: i
        }
        this.monthList.push(obj)
      }
    }
  }
}
</script>

<style lang="scss" scope>
.FormDialog {
  .w-220 {
    width: 220px;
  }
  .w-136 {
    width: 136px;
  }
  .w-120 {
    width: 120px;
  }
  .w-auto {
    width: auto;
  }
}
</style>
