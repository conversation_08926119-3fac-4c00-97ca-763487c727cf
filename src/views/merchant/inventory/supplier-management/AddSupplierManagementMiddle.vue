<template>
  <div class="AddSupplierManagementMiddle form-container">
    <h3>基本信息</h3>
    <el-form v-loading="isLoading" ref="formRef" :model="formData" :rules="formRuls" label-width="142px" label-position="left" size="small" class="m-t-10">
      <div class="m-l-20">
        <el-form-item label="统一社会信用代码" prop="creditCode">
          <el-input v-model="formData.creditCode" :maxlength="20" class="w-200" :disabled="type === 'modify'"></el-input>
          <el-button class="ps-origin-btn m-l-20" size="medium" @click="submitFormHandle">查询</el-button>
        </el-form-item>
      </div>
    </el-form>
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, to, getSuffix, getToken, deepClone } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
// import report from '@/mixins/report' // 混入
import { validateTelphone } from '@/assets/js/validata'
import { defaultNo } from '@/utils/validata'
import addAptitudeDialog from '../components/addAptitudeDialog'
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
import md5 from 'js-md5';
import { APTITUDE_LIST } from '../constants'
import { closeCurrentTabNoBack, backVisitedViewsPath } from '@/utils/layout-tab'

export default {
  name: 'AddSupplierManagement',
  mixins: [exportExcel],
  components: { addAptitudeDialog, ElImageViewer },
  data() {
    return {
      type: 'add',
      isLoading: false, // 刷新数据
      // form表单数据
      formData: {
        id: '',
        name: '', // 供应商名称
        creditCode: '', // 工商营业执照（社会统一信用代码）
      },
      formRuls: {
        creditCode: [{ required: true, message: '亲请输入统一社会信用代码', trigger: 'change' }],
      },
      systemSupplierList: []
    }
  },
  computed: {
  },
  created() {
    this.type = this.$route.params.type
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
    },

    // 保存
    async submitFormHandle(type) {
      this.$refs.formRef.validate(valid => {
        if (valid) {
          this.getSystemSupplierList()
        }
      })
    },
    // 发送请求
    async sendFormData(xhr) {
      this.isLoading = false
      const [err, res] = await this.$to(this.$apis.apiBackgroundDrpSupplierManageSelectAdd({
        
      }))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.visible = false
        this.$message.success('成功')
        this.$router.replace({
          name: 'SupplierManagement'
        })
      } else {
        this.$message.error(res.msg)
      }
    },
    // 菜品远程搜索
    async getSystemSupplierList(e) {
      // this.showFirshDialog()
      // return
      if (this.isLoading) {
        return
      }
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundDrpSupplierManageGetCreateSupplierManagePost({
          credit_code: this.formData.creditCode
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        this.systemSupplierList = []
        return
      }
      if (res.code === 0) {
        if (res.data.results.length > 0) {
          this.systemSupplierList = res.data.results
        } else {
          this.systemSupplierList = []
        }
        this.showFirshDialog()
      } else {
        this.$message.error(res.msg)
        this.systemSupplierList = []
      }
    },
    showFirshDialog() {
      let content = '暂无该供应商，请继续添加供应商信息'
      if (this.systemSupplierList.length > 0) {
        content = `<h4 style="margin: 0 0 10px 0;">供应商已注册，是否直接使用？</h4>
        统一社会信用代码：${this.systemSupplierList[0].credit_code}<br />
        供应商名称：${this.systemSupplierList[0].name}
        `
      }
      this.$confirm(content, `查询结果`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: false,
        dangerouslyUseHTMLString: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            // instance.confirmButtonLoading = true
            done()
            await this.$sleep(300)
            if (this.systemSupplierList.length > 0) {
              this.showSecondDialog()
            } else {
              closeCurrentTabNoBack(this.$route.path)
              this.$router.replace({
                name: 'AddSupplierManagement',
                params: this.$route.params,
                query: this.$route.query
              })
            }
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {

        })
        .catch(e => {})
    },
    showSecondDialog() {
      let content = ''
      if (this.systemSupplierList.length > 0) {
        content = `<h4 style="margin: 0 0 10px 0;">再次确认，添加以下供应商？</h4>
        统一社会信用代码：${this.systemSupplierList[0].credit_code}<br />
        供应商名称：${this.systemSupplierList[0].name}
        `
      }
      this.$confirm(content, `提示`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: false,
        dangerouslyUseHTMLString: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            const [err, res] = await to(
              this.$apis.apiBackgroundDrpSupplierManageSelectAddPost({
                supplier_manage_id: this.systemSupplierList[0].id
              })
            )
            instance.confirmButtonLoading = false
            // instance.cancelButtonLoading = false
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              done()
              this.$message.success(res.msg)
              backVisitedViewsPath(this.$route.path, 'SupplierManagement')

            } else {
              this.$message.error(res.msg)
            }
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    }
  }
}
</script>

<style lang="scss">
.AddSupplierManagementMiddle {
  height: 100%;
  h3{
    margin: 0;
  }
  .w-200 {
    width: 220px;
  }
  .w-auto{
    width: 300px;
  }
  .error {
    ::v-deep .el-input__inner {
      border-color: red;
    }
  }
  .upload-food{
    overflow: hidden;
    max-height: 830px;
    &.hide-upload{
      .el-upload--picture-card{
        display: none;
      }
    }
    .el-upload--picture-card{
      border: none;
    }
    .el-upload-dragger{
      width: 148px;
      height: 148px;
    }
    .upload-food-img{
      display: flex;
      align-items: center;
      justify-content: center;
      width: 145px;
      height: 145px;
      img{
        max-width: 145px;
        max-height: 145px;
      }
    }
  }
  .aptitude-management {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .font-weight-600 {
      font-weight: 600;
    }
  }
}
</style>
