<template>
  <div class="PurchaseDetail container-wrapper">
    <h3 class="m-t-20">申购单列表/详情</h3>
    <div class="table-wrapper">
      <div class="align-r p-t-20 p-b-20">
      </div>
      <!-- 需打印的内容部分 start -->
      <div id="print-box" class="p-20">
        <div class="text-center m-b-20 font-size-26">申购单</div>
        <div class="header m-b-20">
          <span class="inline-block m-r-30">单据编号：{{ detailData.trade_no }}</span>
          <span class="inline-block m-r-30">创建时间：{{ detailData.create_time }}</span>
          <span class="inline-block m-r-30">处理结果：{{ detailData.deal_result_alias }}</span>
          <span class="inline-block m-r-30">申请人：{{ detailData.subscribe_person_name }}</span>
        </div>
        <div class="">
          <!-- table start -->
          <el-table
            v-loading="isLoading"
            :data="tableData"
            ref="tableData"
            style="width: 100%"
            stripe
            border
            size="small"
            header-row-class-name="ps-table-header-row"
          >
            <table-column v-for="item in tableSettings" :key="item.key" :col="item"></table-column>
          </el-table>
          <!-- table end -->
        </div>
        <div class="m-t-20">申购原因：{{detailData.remark}}</div>
        <div class="footer m-t-20">
          <span class="inline-block m-r-30">制单人：{{ detailData.account_name }}</span>
          <span class="inline-block m-r-30">制单日期：{{ new Date() | formatDate('YYYY-MM-DD HH:mm') }}</span>
          <span class="inline-block m-r-30">审核人：{{ detailData.approve_account_name }}</span>
          <span class="inline-block m-r-30">审核日期：{{ detailData.approve_time | formatDate('YYYY-MM-DD HH:mm') }}</span>
        </div>
      </div>
      <!-- 需打印的内容部分 end -->
      <div class="p-20">
        <el-button class="ps-btn" size="small" @click="backHandle">返回</el-button>
        <el-button class="ps-btn" size="small" @click="handleExport">导出</el-button>
        <el-button class="ps-btn" size="small" v-print="printObj">打印</el-button>
        <el-button class="ps-btn" size="small" v-if="detailData.deal_result === 'to_purchase_info' || detailData.deal_result === 'to_exit_info'" @click="showVieWHandle">查看单据</el-button>
      </div>
    </div>
    <view-dialog
      :showview.sync="showViewDialog"
      type="static"
      title="选择单据"
      :tableSettings="viewTableSettings"
      :staticList="viewStaticList"
      @operation="operationViewHandle"
    ></view-dialog>
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { to, getSevenDateRange } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import printUtils from '@/mixins/printUtils' // 导出混入
// import report from '@/mixins/report' // 混入
import print from 'vue-print-nb'
import ViewDialog from '../components/ViewDialog'

export default {
  name: 'ProcureOfferList',
  mixins: [exportExcel, printUtils],
  directives: {
    print
  },
  components: { ViewDialog },
  data() {
    return {
      isLoading: false, // 刷新数据
      accountName: this.$store.getters.userInfo.member_name,
      warehouseId: '',
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      detailData: {},
      tableData: [],
      tableSettings: [
        { label: '物资名称', key: 'materials_name' },
        { label: '物资分类', key: 'materail_classification_name' },
        { label: '申购数量', key: 'count' },
        { label: '最小单位', key: 'unit_name' }
      ],
      searchFormSetting: {
        date_type: {
          type: 'select',
          label: '',
          value: 'create_time',
          maxWidth: '130px',
          placeholder: '请选择',
          dataList: [
            {
              label: '创建时间',
              value: 'create_time'
            },
            {
              label: '入库时间',
              value: 'entry_time'
            }
          ]
        },
        select_time: {
          type: 'daterange',
          format: 'yyyy-MM-dd',
          label: '',
          clearable: true,
          value: getSevenDateRange(7)
        },
        trade_no: {
          type: 'input',
          value: '',
          label: '单据编号',
          placeholder: '请输入'
        },
        order_status: {
          type: 'select',
          label: '入库类型',
          clearable: true,
          value: '',
          dataList: [
            {
              label: '全部',
              value: ''
            },
            {
              label: '采购入库',
              value: 'PURCHASE_ENTRY'
            },
            // {
            //   label: '赠予入库',
            //   value: 'BESTOW_ENTRY'
            // },
            {
              label: '调拨入库',
              value: 'entry_time'
            },
            {
              label: '其他入库',
              value: 'OTHER_ENTRY'
            }
          ]
        }
      },
      // 导入的弹窗数据
      importDialogTitle: '',
      showImportDialog: false,
      importTemplateUrl: '',
      importHeaderLen: 2,
      importApi: 'apiBackgroundFoodIngredientSupplierBatchAddOfferPost',
      showDraft: false, // 打开草稿箱
      printObj: {
        id: '#print-box', // 这里是要打印元素的ID
        popTitle: '&nbsp', // 打印的标题
        extraCss: '', // 打印可引入外部的一个 css 文件
        extraHead: '' // 打印头部文字
      },
      showViewDialog: false,
      viewDialogType: '',
      viewStaticList: [],
      viewTableSettings: []
    }
  },
  created() {
    this.warehouseId = this.$route.query.warehouse_id
    this.initLoad()
  },
  mounted() {
  },
  methods: {
    async initLoad() {
      this.getSubscriptionOrderDetail()
    },
    // 获取接口数据
    async getSubscriptionOrderDetail() {
      if (this.isLoading) return
      this.isLoading = true
      let params = {
        id: this.$route.query.id
      }
      const [err, res] = await to(this.$apis.apiBackgroundDrpSubscribeInfoDetailsPost(params))
      this.tableData = []
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        if (!res.data) return
        this.detailData = res.data
        this.tableData = res.data.materials_info
        // resetTableColgroup.call(this)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 操作点击
    clickOperationHandle(type, data) {
      let title = ''
      let apiUrl = 'apiBackgroundDrpPurchaseInfoSupplierPurchaseModifyPost'
      let params = {}
      switch (type) {
        case 'refuse':
          params = {
            id: data.id,
            supplier_refuse: true
          }
          title = '确定拒收订单吗？'
          break
        case 'accept':
          params = {
            id: data.id,
            accept_order: true
          }
          title = '确定接收订单吗？'
          break
      }
      this.$confirm(title, `提示`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            const [err, res] = await to(this.$apis[apiUrl](params))
            instance.confirmButtonLoading = false
            // instance.cancelButtonLoading = false
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              done()
              this.$message.success(res.msg || '成功')
              this.getSubscriptionOrderDetail()
            } else {
              this.$message.error(res.msg)
            }
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    gotoAddInboundOrder(type, data) {
      this.$router.push({
        name: 'AddInboundOrder',
        params: {
          type
        },
        query: {
          ...this.$route.query
        }
      })
    },
    openImport(type) {
      this.importDialogTitle = '批量导入'
      this.showImportDialog = true
    },
    gotoHandle(type, row) {
      this.$router.push({
        name: 'ModifyPurchaseOrder',
        query: {
          type,
          id: row.id
        }
      })
    },
    handleExport() {
      const option = {
        type: 'PurchaseDetail',
        url: 'apiBackgroundDrpSubscribeInfoDetailsExportPost',
        params: {
          id: this.detailData.id
        }
      }
      this.exportHandle(option)
    },
    backHandle() {
      this.$backVisitedViewsPath(this.$route.path, 'PurchaseOrderList')
    },
    showVieWHandle() {
      this.viewDialogType = this.detailData.deal_result
      if (this.viewDialogType === 'to_purchase_info') {
        this.viewTableSettings = [
          { label: '单据编号', key: 'trade_no' },
          { label: '操作', key: 'operation', type: 'slot', slotName: 'operation', operationList: [{ label: '查看', key: 'check' }] }
        ]
        this.viewStaticList = this.detailData.purchase_info
      }
      if (this.viewDialogType === 'to_exit_info') {
        this.viewTableSettings = [
          { label: '单据编号', key: 'trade_no' },
          { label: '操作', key: 'operation', type: 'slot', slotName: 'operation', operationList: [{ label: '查看', key: 'check' }] }
        ]
        this.viewStaticList = this.detailData.exit_info
      }
      if (this.viewStaticList.length > 1) {
        this.showViewDialog = true
      } else if (this.viewStaticList.length === 1) {
        this.operationViewHandle(this.viewDialogType, this.viewStaticList[0])
      } else {
        this.$message.error('数据为空！')
      }
    },
    operationViewHandle(type, data) {
      let query = {
        id: data.id,
        warehouse_id: data.warehouse_id,
        warehouse_name: data.warehouse_name
      }
      let pageName = ''
      if (this.viewDialogType === 'to_purchase_info') {
        pageName = 'PurchaseDetail'
      }
      if (this.viewDialogType === 'to_exit_info') {
        pageName = 'OutboundOrderDetail'
      }
      this.$router.push({
        name: pageName,
        query: query
      })
    }
  }
}
</script>

<style lang="scss">
@import '~@/styles/print.scss';
.PurchaseDetail {
  width: 100%;
  .w-medium {
    width: 140px;
    height: 40px;
    &.m-r-20 {
      margin-right: 20px;
    }
  }
  table {
    width: 100% !important;
    font-weight: 500;
    th {
      font-weight: 500;
    }
  }
  #print-box {
    width: 100%;
    div {
      width: 100%;
    }
    .el-table thead,
    .ps-table-header-row {
      width: 100%;
    }
    .footer {
      p {
        margin: 10px 0 0;
      }
    }
  }
  .footer {
    .inline-block {
      min-width: 120px;
    }
  }
}
</style>
