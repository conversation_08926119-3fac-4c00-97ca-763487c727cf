import { getSevenDateRange } from '@/utils'
// 库存类型
export const INVENTORY_TYPE = [
  { label: '采购入库', value: 'PURCHASE_ENTRY' },
  { label: '借调入库', value: 'BORROW_ENTRY' },
  { label: '导入入库', value: 'IMPORT_ENTRY' },
  // { label: '赠予入库', value: 'BESTOW_ENTRY' },
  { label: '消耗出库', value: 'EXPEND_EXIT' },
  { label: '借调出库', value: 'BORROW_EXIT' },
  // { label: '自动出库', value: 'AUTO_EXIT' },
  { label: '过期出库', value: 'OVERDUE_EXIT' },
  { label: '退货出库', value: 'REFUND_EXIT' }
]

// 入库类型
export const ENTRY_TYPE = [
  { label: '采购入库', value: 'PURCHASE_ENTRY' },
  { label: '借调入库', value: 'BORROW_ENTRY' },
  { label: '导入入库', value: 'IMPORT_ENTRY' },
  { label: '赠送物资', value: 'BESTOW_ENTRY' }
  // { label: '其他', value: 'value3' }
]

// 出库类型
export const OUT_TYPE = [
  { label: '过期出库', value: 'OVERDUE_EXIT' },
  { label: '损耗出库', value: 'EXPEND_EXIT' },
  // { label: '自动出库', value: 'AUTO_EXIT' },
  { label: '借调出库', value: 'BORROW_EXIT' },
  { label: '退货出库', value: 'REFUND_EXIT' }
  // { label: '其他', value: 'value3' }
]

// 待办事项
export const APPROVAL_DAIBAN_SEARCHFORMSETTINGS = {
  // date_type: {
  //   type: 'select',
  //   label: '',
  //   value: 'create_time',
  //   maxWidth: '100px',
  //   placeholder: '请选择',
  //   dataList: [{
  //     label: '申请时间',
  //     value: 'create_time'
  //   }]
  // },
  select_time: {
    type: 'daterange',
    format: 'yyyy-MM-dd',
    label: '申请时间',
    clearable: false,
    value: getSevenDateRange(7)
  },
  name: {
    type: 'input',
    label: '申请人',
    value: '',
    placeholder: '请输入'
  },
  trade_no: {
    type: 'input',
    label: '单据编号',
    value: '',
    placeholder: '请输入'
  }
}
export const APPROVAL_DAIBAN_TABLESETTINGS = [
  { label: '申请时间', key: 'create_time' },
  { label: '申请人', key: 'name' },
  { label: '单据编号', key: 'trade_no' },
  { label: '单据类型', key: 'approve_type_alias' },
  { label: '操作', key: 'operation', type: "slot", slotName: "operation", fixed: "right" }
]

// 已办事项
export const APPROVAL_YIBAN_SEARCHFORMSETTINGS = {
  date_type: {
    type: 'select',
    label: '',
    value: 'create_time',
    maxWidth: '100px',
    placeholder: '请选择',
    dataList: [{
      label: '申请时间',
      value: 'create_time'
    }, {
      label: '审批时间',
      value: 'approve_time'
    }]
  },
  select_time: {
    type: 'daterange',
    format: 'yyyy-MM-dd',
    label: '',
    clearable: false,
    value: getSevenDateRange(7)
  },
  name: {
    type: 'input',
    label: '申请人',
    value: '',
    placeholder: '请输入'
  },
  trade_no: {
    type: 'input',
    label: '单据编号',
    value: '',
    placeholder: '请输入'
  }
  // approve_status: {
  //   type: 'select',
  //   label: '审批结果',
  //   clearable: true,
  //   value: '',
  //   dataList: [
  //     {
  //       label: '全部',
  //       value: ''
  //     },
  //     {
  //       label: '同意',
  //       value: 'AGREE'
  //     },
  //     {
  //       label: '驳回',
  //       value: 'REJECT'
  //     }
  //   ]
  // },
  // order_st1atus: {
  //   type: 'select',
  //   label: '审批进程',
  //   clearable: true,
  //   value: '',
  //   dataList: [
  //     {
  //       label: '全部',
  //       value: ''
  //     },
  //     {
  //       label: '审批中',
  //       value: 'PENDING'
  //     },
  //     {
  //       label: '审批结束',
  //       value: 'FINISH'
  //     }
  //   ]
  // }
}
export const APPROVAL_YIBAN_TABLESETTINGS = [
  { label: '申请时间', key: 'create_time' },
  { label: '申请人', key: 'name' },
  { label: '单据编号', key: 'trade_no' },
  { label: '审批结果', key: 'approve_status_alias' },
  { label: '审批意见', key: 'reject_reason' },
  { label: '审批时间', key: 'approve_time' },
  { label: '审批详情', key: 'record_list', type: 'slot', slotName: "record" },
  { label: '审批项进程', key: 'deal_status_alias' },
  { label: '操作', key: 'operation', type: "slot", slotName: "operation", fixed: "right" }
]

// 审批查看
export const APPROVAL_DETAIL_TABLESETTINGS = {
  // 采购单
  'purchase_info': [
    { label: '物资名称', key: 'name' },
    { label: '数量', key: 'count' },
    { label: '最小单位', key: 'unit_name' },
    { label: '参考单价', key: 'ref_unit_price', type: 'money' },
    { label: '合计', key: 'total', type: 'money' },
    { label: '供应商', key: 'supplier_manage_name' }
  ],
  // 入库单
  'entry_info': [
    { label: '物资名称', key: 'materials_name' },
    { label: '入库数量', key: 'expected_entry_count' },
    { label: '最小单位', key: 'unit_name' },
    { label: '参考单价', key: 'ref_unit_price', type: 'money' },
    { label: '入库价', key: 'entry_price', type: 'money' },
    { label: '供应商', key: 'supplier_manage_name' }
  ],
  // 出库单
  'exit_info': [
    { label: '物资名称', key: 'materials_name' },
    { label: '出库数量', key: 'count' },
    { label: '最小单位', key: 'unit_name' },
    { label: '供应商', key: 'supplier_manage_name' }
  ],
  // 退货单
  'return_info': [
    { label: '物资名称', key: 'materials_name' },
    { label: '出库数量', key: 'refund_count' },
    { label: '最小单位', key: 'unit_name' },
    { label: '入库价', key: 'ref_unit_price', type: 'money' },
    // { label: '金额合计', key: 'total_price', type: 'money' },
    { label: '退货金额', key: 'refund_fee', type: 'money' }
  ],
  // 申购单
  'subscribe_info': [
    { label: '物资名称', key: 'materials_name' },
    { label: '申购数量', key: 'count' },
    { label: '最小单位', key: 'unit_name' }
  ]
}

// 周期类型
export const CYCLE_TYPE_LIST = [
  { label: '按天', value: 'DAY' },
  { label: '按周', value: 'WEEK' },
  { label: '按月', value: 'MONTH' }
  // { label: '按季度', value: 'QUARTER' }
]

export const WEEK_LIST = [
  {
    label: '星期一',
    value: 1
  },
  {
    label: '星期二',
    value: 2
  },
  {
    label: '星期三',
    value: 3
  },
  {
    label: '星期四',
    value: 4
  },
  {
    label: '星期五',
    value: 5
  },
  {
    label: '星期六',
    value: 6
  },
  {
    label: '星期日',
    value: 7
  }
]

// 季度
export const QUARTER_LIST = [
  { label: '第1季度', value: '1' },
  { label: '第2季度', value: '2' },
  { label: '第3季度', value: '3' },
  { label: '第4季度', value: '4' }
]

// 供应商资质类型
export const APTITUDE_LIST = [
  { label: '营业执照', value: '1' },
  { label: '食品经营许可证', value: '2' },
  { label: '食品生产许可证', value: '3' }
]

// 配送状态
export const DELIVERY_STATUS = [
  { label: '待配送', value: 'wait_delivery' },
  { label: '配送中', value: 'delivering' },
  { label: '货物送达待确认', value: 'arrive' },
  { label: '货物送达已确认', value: 'confirmed' }
]

// 规格

export const UNIT_TYPE = [
  { label: '重量', value: 'weight' },
  { label: '容量', value: 'volume' }
]

export const WEIGHT_TYPE = [
  { label: 'kg', value: 'kg' },
  { label: 'g', value: 'g' }
]

export const VOLUME_TYPE = [
  { label: 'ml', value: 'ml' },
  { label: 'L', value: 'L' }
]

export const NET_CONTENT_LIST = {
  'weight': WEIGHT_TYPE,
  'volume': VOLUME_TYPE
}
