<template>
  <div class="acticity-recharges container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form ref="searchRef" :form-setting="searchFormSetting" label-width="80px" @search="searchHandle"></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="plain" type="export" v-permission="['background_marketing.recharge.report_export']" @click="handleExport">导出EXCEL</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          :empty-text="isFirstSearch ? '暂无数据，请查询' : ''"
          header-row-class-name="ps-table-header-row"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="index" :index="indexMethod" label="编号" align="center"></el-table-column>
          <el-table-column prop="org_name" label="所属组织" align="center"></el-table-column>
          <el-table-column prop="coupon_name" label="活动名" align="center"></el-table-column>
          <el-table-column prop="trade_no" label="充值订单号" align="center"></el-table-column>
          <el-table-column prop="person_no" label="用户编号" align="center"></el-table-column>
          <el-table-column prop="name" label="用户名" align="center"></el-table-column>
          <el-table-column prop="payer_group_name" label="分组" align="center"></el-table-column>
          <el-table-column prop="pay_time" label="充值时间" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.pay_time | formatDate }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="wallet_fee" label="充值金额" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.wallet_fee | formatMoney }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="complimentary_fee" label="赠送内容" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.complimentary_fee | formatMoney }}</span>
            </template>
          </el-table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-size="pageSize"
          layout="total, prev, pager, next, jumper"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, getSevenDateRange, to } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入

export default {
  name: 'ActicityRecharges',
  mixins: [exportExcel],
  data() {
    const defaultdate = getSevenDateRange(7);
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [],
      searchFormSetting: {
        select_date: {
          type: 'datetimerange',
          label: '充值时间',
          value: [defaultdate[0] + ' 00:00:00', defaultdate[1] + ' 23:59:59']
        },
        name: {
          type: 'input',
          label: '用户名',
          value: '',
          placeholder: '请输入用户名'
        },
        person_no: {
          type: 'input',
          label: '用户编号',
          value: '',
          placeholder: '请输入用户编号'
        },
        out_trade_no: {
          type: 'input',
          label: '订单号',
          value: '',
          placeholder: '请输入订单号'
        },
        coupon_name: {
          type: 'input',
          label: '活动名',
          value: '',
          placeholder: '请输入活动名'
        },
        group_no: {
          type: 'groupSelect',
          label: '分组',
          value: '',
          placeholder: '请选择分组'
        }
      },
      isFirstSearch: false
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getActicityRechargesListStatistics()
    },
    // 节下流咯
    searchHandle: debounce(function(e) {
      if (e && e === 'search') {
        // console.log(this.searchFormSetting.card_group_id.value)
        this.isFirstSearch = false
        this.currentPage = 1
        this.initLoad()
      }
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.isFirstSearch = false
      this.tableData = []
      // this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value) {
          if (key !== 'select_date') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_time = data[key].value[0]
            params.end_time = data[key].value[1]
          }
        }
      }
      return params
    },
    async getActicityRechargesListStatistics() {
      this.isLoading = true
      let params = {
        page: this.currentPage,
        page_size: this.pageSize,
        ...this.formatQueryParams(this.searchFormSetting)
      }
      const [err, res] = await to(this.$apis.apiBackgroundMarketingRechargeReportPost(params));
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.totalCount = res.data.count
        this.tableData = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    indexMethod(index) {
      return (this.currentPage - 1) * this.pageSize + (index + 1)
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val;
      this.getActicityRechargesListStatistics()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getActicityRechargesListStatistics()
    },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {
    },
    // 导出报表
    handleExport() {
      let params = {
        page: this.currentPage,
        page_size: this.totalCount,
        ...this.formatQueryParams(this.searchFormSetting)
      }
      const option = {
        type: 'ActivityRechargeStatistics',
        url: 'apiBackgroundMarketingRechargeReportExportPost',
        params: params
      }
      this.exportHandle(option)
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
