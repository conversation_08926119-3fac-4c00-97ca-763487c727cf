<template>
  <div class="add-banner-wrapper">
    <add-banner :type="type" />
  </div>
</template>

<script>
import AddBanner from '@/views/public/banner/add'
export default {
  name: 'MerchantBanner',
  components: {
    AddBanner
  },
  data() {
    return {
      type: 'merchant'
    }
  },
  created() {
    // this.initLoad()
  },
  mounted() {
  },
  computed: {
  },
  methods: {
    initLoad() {
    }
  }
}
</script>

<style lang="scss" scoped>
.add-banner-wrapper{
}
</style>
