<template>
  <div id="NoticeAdd">
    <div class="form-wrapper container-wrapper">
      <el-form
        :model="noticeForm"
        label-width="120px"
        :rules="noticeInfoRules"
        ref="noticeInfoForm"
      >
        <el-form-item label="公告名称：" prop="name">
          <el-input
            v-model="noticeForm.name"
            placeholder="请输入公告名称"
            class="ps-input"
            style="width:400px;"
            maxlength="20"
          ></el-input>
        </el-form-item>
        <el-form-item label="首页广播：" prop="title">
          <el-input
            v-model="noticeForm.title"
            placeholder="不能超过20字"
            maxlength="20"
            class="ps-input"
            style="width:400px;"
          ></el-input>
        </el-form-item>
        <el-form-item label="公告类型：" prop="title">
          <el-select v-model="noticeForm.notice_type" placeholder="请选择">
            <el-option
              :label="'日常公告'"
              :value="'daily'">
            </el-option>
            <el-option
              :label="'紧急公告'"
              :value="'urgent'">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否需要签名：" prop="title">
          <el-select v-model="noticeForm.is_sign" placeholder="请选择">
            <el-option
              :label="'是'"
              :value="true">
            </el-option>
            <el-option
              :label="'否'"
              :value="false">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="" label-width="50px" prop="display_limit">
          <el-radio-group v-model="noticeForm.display_limit" class="ps-radio">
            <el-radio label="group">指定分组可查看</el-radio>
            <el-radio label="all">所有人</el-radio>
            <el-radio label="user">指定人员</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="用户分组：" prop="user_groups_ids" v-if="noticeForm.display_limit === 'group'">
          <div class="ps-flex">
          <user-group-select
            ref="groupSelect"
            :multiple="true"
            :collapse-tags="true"
            class="ps-input"
            style="width: 400px;"
            v-model="noticeForm.user_groups_ids"
            placeholder="请选择分组"
          ></user-group-select>
        </div>
        </el-form-item>
        <el-form-item label="" label-width="50px" prop="use_template">
          <el-radio-group v-model="noticeForm.use_template" class="ps-radio">
            <el-radio :label="true">使用模板</el-radio>
            <el-radio :label="false">自定义</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="noticeForm.use_template" label="公告内容编辑：" label-width="160px">
          <el-input
            type="textarea"
            placeholder="请输入公告内容"
            v-model="noticeForm.templateContent"
            rows="23"
            class="m-750 ps-border"
          >
          </el-input>
        </el-form-item>
        <el-form-item v-if="!noticeForm.use_template" label="公告详情：" prop="content">
          <TinymceUeditor
            class="m-750"
            v-model="noticeForm.content"
            listener="focus"
            :custom-handle="blurSelsectHandle"
          ></TinymceUeditor>
        </el-form-item>
        <el-form-item>
          <el-button
            :disabled="isLoading"
            plain
            @click="closeTab"
            style="width: 120px;"
          >
            取消
          </el-button>
          <el-button
            :disabled="isLoading"
            class="ps-btn"
            type="primary"
            @click="submitForm"
            style="width: 120px;"
          >
            确认发布
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="table-wrapper m-20" v-if="noticeForm.display_limit === 'user'">
      <div class="table-header">
        <div class="table-title">
          <span>选择人员列表</span>
        </div>
        <div class="align-r">
          <button-icon color="origin" @click="chooseSomeone">选择人员</button-icon>
        </div>
      </div>
      <div class="table-content">
        <el-table
          :data="tableData"
          v-loading="isLoading"
          ref="tableData"
          stripe
          header-row-class-name="ps-table-header-row">
          <table-column v-for="(item, index) in tableSetting" :key="index" :col="item">
            <template #operation="{ row }">
              <el-button
                type="text"
                size="small"
                class="ps-text"
                @click="deleteMember(row)"
                >删除
              </el-button>
            </template>
          </table-column>
        </el-table>
      </div>

      <!-- 选择人员弹窗 -->
      <el-dialog
        title="选择人员"
        :visible.sync="memberDialog"
        width="700px"
        top="15vh"
        custom-class="ps-dialog"
        :close-on-click-modal="false"
      >
        <div class="dialog-content">
          <el-form ref="cardruleForm" label-width="90px" class="demo-ruleForm">
            <div style="display: flex">
              <el-form-item label="部门">
                <el-cascader
                  class="ps-select"
                  popper-class="ps-popper-cascader"
                  v-model="memberOpts.selectGroup"
                  :options="memberOpts.departmentList"
                  :props="groupOpts"
                  clearable
                  style="width: 180px"
                  @change="changeGroupHandle"
                  collapse-tags
                ></el-cascader>
              </el-form-item>
              <el-form-item label="人员编号">
                <el-input
                  class="ps-input"
                  @change="changePersonNo"
                  v-model="memberOpts.personNo"
                  placeholder="请输入"
                ></el-input>
              </el-form-item>
              <div style="margin-left: 20px;">
                <el-button type="primary" class="ps-btn" @click="changeGroupHandle">搜索</el-button>
              </div>
            </div>
          </el-form>
          <el-table
            ref="selectTultipleTable"
            :data="memberOpts.tableData"
            tooltip-effect="dark"
            header-row-class-name="table-header-row"
            :row-class-name="tableRowClassName"
            v-loading="isLoading"
            :row-key="getRowKey"
            reserve-selection
            @select-all="selectSelectionAll"
            @select="selectSelection"
          >
            <el-table-column class-name="ps-checkbox" type="selection" width="37"></el-table-column>
            <el-table-column prop="person_no" label="人员编号" align="center"></el-table-column>
            <el-table-column prop="name" label="名称" align="center"></el-table-column>
            <el-table-column
              prop="department_group_name"
              label="部门"
              align="center"
            ></el-table-column>
            <el-table-column prop="card_no" label="卡号" align="center"></el-table-column>
          </el-table>
          <div class="block ps-pagination" style="text-align: right; padding-top: 20px">
            <el-pagination
              @size-change="memberSizeChange"
              @current-change="memberCurrentChange"
              :current-page="memberOpts.currentPage"
              :page-sizes="[5, 10, 15, 20]"
              :page-size="memberOpts.pageSize"
              layout="total, prev, pager, next"
              :total="memberOpts.totalCount"
              background
              class="ps-text"
              popper-class="ps-popper-select"
            ></el-pagination>
          </div>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button class="ps-cancel-btn" type="primary" @click="closeMemberDialog">
            {{ $t('dialog.cancel_btn') }}
          </el-button>
          <el-button class="ps-btn" type="primary" @click="submitMemberDialog">确定</el-button>
        </span>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { to, escapeHTML, unescapeHTML, getSessionStorage, deepClone } from '@/utils'
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import TinymceUeditor from '@/components/Tinymce/Tinymce.vue'
import { mapActions } from 'vuex'
import UserGroupSelect from '@/components/UserGroupSelect'

export default {
  name: 'NoticeAdd',
  components: {
    TinymceUeditor, UserGroupSelect
  },
  props: {},
  // mixins: [activatedLoadData],
  data() {
    return {
      noticeForm: {
        title: '',
        name: '',
        content: '',
        templateContent: '',
        fileLists: [],
        notice_type: 'daily',
        is_sign: true,
        use_template: false,
        user_groups_ids: [], // 用户分组
        display_limit: 'group'
      },
      treeList: [],
      organizationOpts: {
        multiple: true,
        checkStrictly: true,
        value: 'id',
        label: 'name',
        children: 'children_list'
      },
      noticeInfoRules: {
        title: [{ required: true, message: '请输入首页广播内容', trigger: 'blur' }],
        name: [{ required: true, message: '请输入公告名称', trigger: 'blur' }],
        type: [{ required: true, message: this.$t('placeholder.notice_title'), trigger: 'blur' }],
        content: [{ required: true, message: '请输入公告详情', trigger: 'blur' }],
        use_template: [{ required: true, message: '请选择公告内容类型', trigger: 'blur' }],
        templateContent: [{ required: true, message: '请输入公告内容', trigger: 'blur' }],
        user_groups_ids: [{ required: true, message: '请选择用户分组', trigger: 'blur' }]
      },
      type: '',
      msg_id: '',
      isLoading: false,
      treeIds: [],
      organizationIds: [],
      chooseAllOrgs: false, // 全选
      tableData: [],
      tableSetting: [
        {
          label: '人员编号',
          key: 'person_no'
        },
        {
          label: '名称',
          key: 'name'
        },
        {
          label: '部门',
          key: 'department_group_name'
        },
        {
          label: '卡号',
          key: 'card_no'
        },
        {
          label: '操作',
          key: 'operation',
          type: 'slot',
          slotName: 'operation'
        }
      ],
      page: '',
      pageSize: '',
      totalCount: '',
      memberDialog: false,
      memberOpts: {
        tableData: [],
        isall: false,
        personNo: '',
        selectGroup: [],
        departmentList: [],
        pageSize: 5, // 每页数量
        totalCount: 0, // 总条数
        currentPage: 1, // 第几页
        selectData: [], // 当前表格选中的数据
        allSelectData: [], // 所有选中的数据，包括导入到selectSubsidyData中的
        person_no: ''
      },
      // 搜索分组配置
      groupOpts: {
        value: 'id',
        label: 'group_name',
        children: 'children_list',
        checkStrictly: true
      },
      selectListPersonNo: [] // 选中的数据PersonNo
    }
  },
  created() {
    // this.getCompanyList()
    if (this.$route.params.type) {
      this.type = this.$route.params.type
    }
    if (this.$route.query.id) {
      this.msg_id = this.$route.query.id
      // this.getMessagesDetails(this.msg_id)
      let detail = this.$decodeQuery(getSessionStorage(this.$route.query.key))
      console.log('detail', detail)
      this.initLoad(detail)
    }
  },
  mounted() {},
  methods: {
    ...mapActions({}),
    initLoad(data) {
      if (data) {
        //
        this.noticeForm.name = data.name
        this.noticeForm.title = data.title
        this.noticeForm.use_template = data.use_template
        // 赋值分组
        this.noticeForm.display_limit = data.display_limit || ''
        this.noticeForm.user_groups_ids = data.user_groups_ids || []
        this.noticeForm.is_sign = data.is_sign
        if (data.use_template) {
          this.noticeForm.templateContent = data.content
        } else {
          this.noticeForm.content = unescapeHTML(data.content)
        }
        // 获取全部分组
        if (this.noticeForm.display_limit === 'all') {
          this.noticeForm.user_groups_ids = this.getSelectAllIds()
        }
        this.tableData = data.card_info_list
        this.memberOpts.allSelectData = deepClone(this.tableData)
      }
    },
    // 创建公告
    async messagesAdd(params) {
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundMarketingMarketingNoticeAddPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.closeTab()
        this.$router.push({ name: 'MerchantMobileNotice' })
      } else {
        this.$message.error(res.msg)
      }
    },
    // 编辑公告
    async messagesEdit(params) {
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundMarketingMarketingNoticeModifyPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.closeTab()
        this.$router.push({ name: 'MerchantMobileNotice' })
      } else {
        this.$message.error(res.msg)
      }
    },
    // 编辑获取详情
    async getMessagesDetails(id) {
      const [err, res] = await to(
        this.$apis.apiBackgroundMessagesMessagesDetailsPost({
          msg_no: id
        })
      )
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.noticeForm.title = res.data.title
        this.noticeForm.content = unescapeHTML(res.data.content)
      } else {
        this.$message.error(res.msg)
      }
    },
    submitForm() {
      this.$refs.noticeInfoForm.validate(async valid => {
        if (valid) {
          if (this.noticeForm.display_limit === 'user' && this.tableData.length === 0) {
            this.$message.error('请选择人员')
            return
          }
          let ids = []
          if (this.noticeForm.display_limit !== 'group') {
            ids = await this.getSelectAllIds()
          }
          let params = {
            name: this.noticeForm.name,
            title: this.noticeForm.title,
            use_template: this.noticeForm.use_template,
            status: 'publish',
            notice_type: this.noticeForm.notice_type,
            is_sign: this.noticeForm.is_sign,
            display_limit: this.noticeForm.display_limit,
            user_groups_ids: this.noticeForm.display_limit !== 'user' ? (this.noticeForm.display_limit === 'group' ? this.noticeForm.user_groups_ids : ids) : [],
            card_info: this.noticeForm.display_limit === 'user' ? this.tableData.map(item => item.id) : [],
            content: this.noticeForm.use_template ? this.noticeForm.templateContent : escapeHTML(this.noticeForm.content) // 为了安全转下码
          }
          if (this.type === 'modify') {
            params.id = Number(this.msg_id)
            this.messagesEdit(params)
          } else {
            this.messagesAdd(params)
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    getFileLists(fileLists) {
      this.noticeForm.fileLists = fileLists
    },
    beforeUpload(file) {
      let unUploadType = ['.bat', '.sh']
      if (unUploadType.includes(this.getSuffix(file.name))) {
        this.$message.error('请检查上传文件格式！')
        return false
      }
      const isLt20M = (file.size / 1024 / 1024) <= 20
      if (!isLt20M) {
        this.$message.error('上传附件大小不能超过 20M')
      }
      return isLt20M
    },
    // 获取文件后缀名
    getSuffix(filename) {
      let pos = filename.lastIndexOf('.')
      let suffix = ''
      if (pos !== -1) {
        suffix = filename.substring(pos)
      }
      return suffix
    },
    // 全部商户
    blurSelsectHandle(e) {
      // this.$refs.organizationRef.blur()
    },
    closeTab() {
      this.$closeCurrentTab(this.$route.path)
    },
    // 获取全选的id
    getSelectAllIds() {
      return new Promise((resolve) => {
        this.$apis.apiCardServiceCardUserGroupListPost({
          is_show_other: false,
          status: 'enable',
          page: 1,
          page_size: 99999
        }).then(res => {
          let data = res.data || {}
          let result = data.results || []
          let ids = []
          result.forEach(item => {
            ids.push(item.id)
          })
          resolve(ids || [])
        }).catch(() => {
          resolve([])
        })
      })
    },
    // 添加表格样式
    tableRowClassName({ row, rowIndex }) {
      let str = ''
      if (row.row_color) {
        str = 'table-header-row'
      }
      return str
    },
    chooseSomeone() {
      this.memberDialog = true
      this.getCardUserList()
      this.getDepartmentList()
    },
    changePersonNo() {
      this.memberOpts.currentPage = 1
      this.getCardUserList()
    },
    changeGroupHandle(list) {
      this.memberOpts.currentPage = 1
      this.getCardUserList()
    },
    uniqueMemberselData(data) {
      let keys = [] // 去重的key
      let result = [] // 去重后的结果
      // 去重前需重置
      data.selkeyvaldata = {}
      for (let i = 0; i < data.selectData.length; i++) {
        if (keys.indexOf(data.selectData[i].person_no) < 0) {
          keys.push(data.selectData[i].person_no)
          result.push(data.selectData[i])
        }
        data.selkeyvaldata[data.selectData[i].person_no] = data.selectData[i]
      }
      this.memberOpts.selectData = deepClone(result)
    },
    // 获取人员
    async getCardUserList() {
      this.isLoading = true
      let params = {
        page_size: this.memberOpts.pageSize,
        page: this.memberOpts.currentPage,
        card_department_group_id:
          this.memberOpts.selectGroup[this.memberOpts.selectGroup.length - 1]
      }
      if (this.type === 'edit') {
        params.edit = true
        params.card_subsidy_id = this.subsidyId
      }
      if (this.memberOpts.personNo) {
        params.person_no = this.memberOpts.personNo
      }
      const res = await this.$apis.apiCardServiceCardSubsidyUserListPost(params)
      this.isLoading = false
      if (res.code === 0) {
        this.memberOpts.tableData = res.data.results
        this.memberOpts.totalCount = res.data.count
      } else {
        this.$message.error(res.msg)
      }
      this.setSelMemberData()
    },
    // 人员弹窗获取部门信息
    async getDepartmentList() {
      this.isLoading = true
      const res = await this.$apis.apiCardServiceCardDepartmentGroupTreeListPost()
      this.isLoading = false
      if (res.code === 0) {
        this.memberOpts.departmentList = this.deleteEmptyGroup(res.data)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 处理下没有children_list
    deleteEmptyGroup(treeData) {
      let _that = this
      function traversal(data) {
        data.map(item => {
          if (item.children_list) {
            if (item.children_list.length > 0) {
              traversal(item.children_list)
            } else {
              _that.$delete(item, 'children_list')
            }
          } else {
            _that.$delete(item, 'children_list')
          }
        })
      }
      traversal(treeData)
      return treeData
    },
    // 设置人员选择table表格的选中状态
    setSelMemberData() {
      console.log('this.memberOpts.allSelectData', this.memberOpts.allSelectData)
      this.memberOpts.tableData.forEach(item => {
        let arr = this.memberOpts.allSelectData.filter(itemIn => itemIn.id === item.id)
        this.selectTultipleTable = this.$refs.selectTultipleTable
        if (arr.length) {
          this.$nextTick(() => {
            this.selectTultipleTable.toggleRowSelection(item, true)
          })
        } else {
          this.$nextTick(() => {
            this.selectTultipleTable.toggleRowSelection(item, false)
          })
        }
      })
    },
    selectSelection(selection, row) {
      let index = this.memberOpts.allSelectData.findIndex(item => item.id === row.id)
      if (index === -1) {
        this.memberOpts.allSelectData.push(row)
      } else {
        this.memberOpts.allSelectData.splice(index, 1)
      }
    },
    selectSelectionAll(selection) {
      selection.forEach(item => {
        let index = this.memberOpts.allSelectData.findIndex(itemIn => itemIn.id === item.id)
        if (index === -1) {
          this.memberOpts.allSelectData.push(item)
        }
      })
    },
    // memberList 分页
    memberSizeChange(val) {
      this.memberOpts.pageSize = val
      this.getCardUserList()
      this.setSelMemberData()
    },
    memberCurrentChange(val) {
      this.memberOpts.currentPage = val
      this.getCardUserList()
      this.setSelMemberData()
    },
    // 弹框确定按钮
    submitMemberDialog() {
      this.tableData = deepClone(this.memberOpts.allSelectData)
      this.memberDialog = false
    },
    closeMemberDialog() {
      this.$refs.selectTultipleTable.clearSelection()
      this.memberOpts.allSelectData = deepClone(this.tableData)
      this.memberDialog = false
    },
    getRowKey(row) {
      return row.id;
    },
    // 表格删除人员
    deleteMember(data) {
      this.tableData = this.tableData.filter(item => {
        return item.id !== data.id
      })
      this.memberOpts.allSelectData = deepClone(this.tableData)
    }
  }
}
</script>

<style lang="scss">
#NoticeAdd {
  .m-750{
    max-width: 750px;
  }
  .form-wrapper {
    margin: 20px;
    padding: 30px 20px;
    background-color: #fff;
    border-radius: 5px;
  }
}
</style>
