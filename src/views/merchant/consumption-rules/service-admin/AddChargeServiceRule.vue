<template>
  <div class="add-conrules-wrapper container-wrapper circular-bead">
    <el-form
      ref="consumptionFormRef"
      v-loading="isLoading"
      :rules="formDataRule"
      :model="formData"
      class="consumption-form-wrapper ps-small-box"
      size="small"
    >
      <el-form-item label="规则名称：" prop="name" class="name-b" label-width="130px">
        <el-input
          class="ps-input"
          v-model="formData.name"
          placeholder="请输入规则名称"
          style="width: 215px"
          maxlength="15"
        ></el-input>
      </el-form-item>
      <el-form-item
        class="min-label-w"
        label="适用分组："
        key="groupNos"
        prop="groupNos"
        label-width="130px"
      >
        <user-group-select
          :multiple="true"
          :collapse-tags="true"
          class="ps-input"
          style="width: 215px"
          v-model="formData.groupNos"
          placeholder="请选择分组"
        ></user-group-select>
      </el-form-item>
      <el-form-item label="选择需要收取手续费的充值方式：" prop="selectListId" class="block-label">
        <!-- table start -->
        <div class="m-t-20">
          <el-table
            :data="historytableData"
            ref="historytableData"
            style="width: 800px"
            stripe
            height="300"
            header-row-class-name="ps-table-header-row"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" class-name="ps-checkbox" width="55"></el-table-column>
            <el-table-column
              prop="pay_scene_alias"
              label="支付类型"
              align="center"
            ></el-table-column>
            <el-table-column prop="payway_alias" label="充值渠道" align="center"></el-table-column>
            <el-table-column
              prop="sub_payway_alias"
              label="充值类型"
              align="center"
            ></el-table-column>
            <el-table-column show-overflow-tooltip label="手续费" align="center">
              <template slot-scope="scope">
                <el-button
                  type="text"
                  size="small"
                  class="ps-text"
                  v-if="
                    !scope.row.service_fee_value &&
                    formData.selectListId.length &&
                    formData.selectListId.includes(scope.row.id)
                  "
                  @click="serviceSetting(scope.row)"
                >
                  设置
                </el-button>
                <span
                  v-else-if="
                    formData.selectListId.length &&
                    formData.selectListId.includes(scope.row.id) &&
                    scope.row.service_fee_type === 1 &&
                    scope.row.service_fee_value
                  "
                  @click="serviceSetting(scope.row)"
                  class="ps-origin"
                >
                  {{ scope.row.service_fee_value }}元
                </span>
                <span
                  v-else-if="
                    formData.selectListId.length &&
                    formData.selectListId.includes(scope.row.id) &&
                    scope.row.service_fee_type === 0 &&
                    scope.row.service_fee_value
                  "
                  @click="serviceSetting(scope.row)"
                  class="ps-origin"
                >
                  {{ scope.row.service_fee_value }}%
                </span>
                <span
                  v-else-if="
                    formData.selectListId.length &&
                    formData.selectListId.includes(scope.row.id) &&
                    scope.row.service_fee_type === 2 &&
                    scope.row.service_fee_value
                  "
                  @click="serviceSetting(scope.row)"
                  class="ps-origin"
                >
                  {{ scope.row.service_fee_value }}%
                </span>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <!-- table end -->
      </el-form-item>
      <div class="footer">
        <div style="color: red" class="m-b-20">注：禁用状态才支持修改规则信息</div>
        <el-button
          class="ps-origin-plain-btn"
          :disabled="modifyData.status === 'enable' ? true : false"
          type="primary"
          @click="saveConsumHandle('stop')"
        >
          保存
        </el-button>
        <el-button
          class="ps-origin-btn"
          :disabled="modifyData.status === 'enable' ? true : false"
          type="primary"
          @click="saveConsumHandle('enable')"
        >
          保存并启用
        </el-button>
      </div>
    </el-form>
    <el-dialog
      title="手续费设置"
      :visible.sync="serviceSettingDialog"
      width="400px"
      custom-class="ps-dialog"
    >
      <el-form
        ref="serviceSettingForm"
        :rules="serviceSettingDialogRuls"
        :model="serviceSettingDialogFormData"
      >
        <div class="ps-flex">
          <el-form-item class="p-r-20">
            <el-radio
              class="ps-radio"
              v-model="serviceSettingDialogFormData.service_fee_type"
              :label="1"
            >
              定额
            </el-radio>
          </el-form-item>
          <el-form-item prop="quota" v-if="serviceSettingDialogFormData.service_fee_type == 1">
            <div class="ps-flex">
              <el-input
                class="ps-input w-150 p-r-10"
                size="small"
                v-model="serviceSettingDialogFormData.quota"
              ></el-input>
              <span>元</span>
            </div>
            <span>实收金额=订单金额+定额</span>
          </el-form-item>
        </div>
        <div class="ps-flex">
          <el-form-item label="" class="p-r-20">
            <el-radio
              class="ps-radio"
              v-model="serviceSettingDialogFormData.service_fee_type"
              :label="0"
            >
            百分比A
            </el-radio>
          </el-form-item>
          <el-form-item prop="discount" v-if="serviceSettingDialogFormData.service_fee_type == 0">
            <div class="ps-flex">
              <el-input
                class="ps-input w-150 p-r-10"
                size="small"
                v-model="serviceSettingDialogFormData.discount"
              ></el-input>
              <span>%</span>
            </div>
            <span>实收金额=订单金额+（订单金额*百分比）</span>
          </el-form-item>
        </div>
        <div class="ps-flex">
          <el-form-item label="" class="p-r-20">
            <el-radio
              class="ps-radio"
              v-model="serviceSettingDialogFormData.service_fee_type"
              :label="2"
            >
              百分比B
            </el-radio>
          </el-form-item>
          <el-form-item prop="reduced" v-if="serviceSettingDialogFormData.service_fee_type == 2">
            <div class="ps-flex">
              <el-input
                class="ps-input w-150 p-r-10"
                size="small"
                v-model="serviceSettingDialogFormData.reduced"
              ></el-input>
              <span>%</span>
            </div>
            <span>到账金额=订单金额-（订单金额*百分比）</span>
          </el-form-item>
        </div>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="serviceSettingDialog = false">取 消</el-button>
        <el-button class="ps-btn" type="primary" @click="determineServiceSettingDialog">
          确 定
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { mapGetters } from 'vuex'
import { to } from '@/utils'
import UserGroupSelect from '@/components/UserGroupSelect'
import { validataPrice, validateStock } from '@/assets/js/validata'
import NP from 'number-precision'

export default {
  components: {
    UserGroupSelect
  },
  data() {
    // 校验金额小于100
    let validatePrice100 = (rule, value, callback) => {
      if (!value) {
        callback()
      }
      if (value >= 100) {
        callback(new Error('折扣不能大于或等于100%'))
      } else {
        callback()
      }
    }
    return {
      type: '',
      isLoading: false,
      historytableData: [],
      formDataRule: {
        name: [{ required: true, message: '消费规则名称不能为空', trigger: 'blur' }],
        groupNos: [{ required: true, message: '适用分组不能为空', trigger: 'change' }],
        selectListId: [{ required: true, message: '扣款方式不能为空', trigger: 'change' }]
      },
      formData: {
        name: '',
        groupNos: [],
        selectListId: [] // table 选择id
      },
      serviceSettingDialog: false,
      serviceSettingDialogFormData: {
        service_fee_type: 1,
        quota: '',
        discount: '',
        reduced: ''
      },
      serviceSettingDialogRuls: {
        quota: [
          { required: true, message: '请输入金额', trigger: 'blur' },
          { validator: validataPrice, trigger: 'blur' }
        ],
        discount: [
          { required: true, message: '请输入折扣', trigger: 'blur' },
          { validator: validateStock, trigger: 'blur' }
        ],
        reduced: [
          { required: true, message: '请输入折扣', trigger: 'blur' },
          { validator: validateStock, trigger: 'blur' },
          { validator: validatePrice100, trigger: 'blur' }
        ]
      },
      serviceSettingData: {}, // 手续费
      modifyData: {}
    }
  },
  computed: {
    ...mapGetters(['organization'])
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    async initLoad() {
      this.type = this.$route.params.type
      if (this.type === 'modify') {
        this.modifyData = this.$decodeQuery(this.$route.query.data)
        this.formData = {
          name: this.modifyData.name,
          groupNos: this.modifyData.rule_info.group_names
            ? Object.values(this.modifyData.rule_info.group_names)
            : [],
          selectListId: [] // table 选择id
        }
        console.log(this.modifyData, 22)
      }
      this.getChargeGetPayinfoList()
    },
    // 获取扣款方式
    async getChargeGetPayinfoList() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundMarketingCommissionChargeGetPayinfoListPost({
          type: 'charge',
          organizations: [this.organization]
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.historytableData = res.data.map(v => {
          v.service_fee_type = 1
          v.service_fee_value = ''
          // 编辑的时候需要返现
          if (this.type === 'modify') {
            this.modifyData.rule.forEach(ruleItem => {
              if (v.id === ruleItem.payinfo) {
                // 回显table
                this.$nextTick(() => {
                  this.$refs.historytableData.toggleRowSelection(v)
                })
                if (Reflect.has(ruleItem, "charge_reduced")) {
                  // 如果包含charge_reduced 就是百分比B
                  v.service_fee_value = ruleItem.charge_reduced
                  v.service_fee_type = 2
                }
                if (Reflect.has(ruleItem, "charge_percent")) {
                  // 如果包含charge_reduced 就是百分比A
                  v.service_fee_value = ruleItem.charge_percent
                  v.service_fee_type = 0
                }
                if (Reflect.has(ruleItem, "charge_fixed")) {
                  // 如果包含charge_reduced 就是定额
                  v.service_fee_value = NP.divide(ruleItem.charge_fixed, 100)
                  v.service_fee_type = 1
                }
              }
            })
          }
          return v
        })
        console.log(this.formData.selectListId)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 新增手续费规则
    async getCommissionChargeChargeAdd(params) {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundMarketingCommissionChargeChargeAddPost(params)
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.$closeCurrentTab(this.$route.path)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 编辑手续费规则
    async getChargeModify(params) {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundMarketingCommissionChargeChargeModifyPost(params)
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.$closeCurrentTab(this.$route.path)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 列表选择
    handleSelectionChange(val) {
      this.formData.selectListId = []
      let data = Object.freeze(val) // 解除下监听吧，节约点资源
      data.map(item => {
        this.formData.selectListId.push(item.id)
      })
    },
    // 手续费
    serviceSetting(data) {
      this.serviceSettingDialogFormData = {
        service_fee_type: 1,
        quota: '',
        discount: '',
        reduced: ''
      }
      this.serviceSettingData = data
      this.serviceSettingDialogFormData.service_fee_type = data.service_fee_type
      if (data.service_fee_type === 1) {
        this.serviceSettingDialogFormData.discount = ''
        this.serviceSettingDialogFormData.reduced = ''
        this.serviceSettingDialogFormData.quota = data.service_fee_value
          ? data.service_fee_value
          : ''
      }
      if (data.service_fee_type === 0) {
        this.serviceSettingDialogFormData.discount = data.service_fee_value
        this.serviceSettingDialogFormData.reduced = ''
        this.serviceSettingDialogFormData.quota = ''
      }
      if (data.service_fee_type === 2) {
        this.serviceSettingDialogFormData.discount = ''
        this.serviceSettingDialogFormData.reduced = data.service_fee_value
        this.serviceSettingDialogFormData.quota = ''
      }

      this.serviceSettingDialog = true
    },
    determineServiceSettingDialog() {
      this.$refs.serviceSettingForm.validate(valid => {
        if (valid) {
          // 塞到列表里面
          this.serviceSettingData.service_fee_type =
            this.serviceSettingDialogFormData.service_fee_type
          this.serviceSettingData.service_fee_value =
          this.serviceSettingData.service_fee_value = this.getServiceValue()
          this.serviceSettingDialog = false
        } else {
          console.log(valid)
        }
      })
    },
    /**
     * 根据类型获取服务费内容
     */
    getServiceValue() {
      var serviceFeeValue = ''
      switch (this.serviceSettingDialogFormData.service_fee_type) {
        case 1:
          serviceFeeValue = Number(this.serviceSettingDialogFormData.quota)
          break;
        case 0:
          serviceFeeValue = this.serviceSettingDialogFormData.discount
          break;
        case 2:
          serviceFeeValue = this.serviceSettingDialogFormData.reduced
          break;
        default:
          break;
      }
      return serviceFeeValue
    },
    saveConsumHandle(type) {
      this.$refs.consumptionFormRef.validate(valid => {
        if (valid) {
          let params = {
            name: this.formData.name,
            group_nos: this.formData.groupNos,
            org_nos: [this.organization],
            rules: [],
            status: type
          }
          let rulesFlag = false
          for (let index = 0; index < this.historytableData.length; index++) {
            let payway = this.historytableData[index]
            if (
              this.formData.selectListId.length &&
              this.formData.selectListId.includes(payway.id)
            ) {
              if (payway.service_fee_value) {
                params.rules.push({
                  payway_alias: payway.payway_alias,
                  sub_payway_alias: payway.sub_payway_alias,
                  pay_scene_alias: payway.pay_scene_alias,
                  payinfo_id: payway.id,
                  type: payway.service_fee_type,
                  numerical:
                    payway.service_fee_type === 1
                      ? NP.times(Number(payway.service_fee_value), 100)
                      : Number(payway.service_fee_value)
                })
              } else {
                rulesFlag = true
              }
            }
          }
          if (rulesFlag) return this.$message.error('请输入手续费')
          if (this.type === 'add') {
            this.getCommissionChargeChargeAdd(params)
          } else if (this.type === 'modify') {
            this.getChargeModify({ rule_no: this.modifyData.rule_no, ...params })
          }
        }
      })
    }
  }
}
</script>

<style lang="scss">
.add-conrules-wrapper {
  margin-top: 20px;
  padding: 20px;
  .consumption-form-wrapper {
    // max-width: 1106px;
  }
}
.block-label {
  width: 100%;
  padding-left: 50px;
  .el-form-item__label {
    display: block;
    text-align: left;
    line-height: 1.5;
    float: none;
  }
}
.footer {
  margin-top: 30px;
  margin-left: 110px;
  .el-button {
    min-width: 180px;
  }
}
</style>
