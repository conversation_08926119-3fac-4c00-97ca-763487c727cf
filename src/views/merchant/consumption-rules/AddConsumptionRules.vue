<template>
  <div class="add-conrules-wrapper container-wrapper circular-bead">
    <el-form
      ref="consumptionFormRef"
      v-loading="isLoading"
      :rules="consumtionFormRule"
      :model="consumptionFormData"
      class="consumption-form-wrapper ps-small-box"
      size="small"
      v-if="type==='modify'&&initLoading || type==='add'"
    >
    <el-form-item label="名称：" prop="name" class="name-b">
      <el-input  class="ps-input w-300" v-model="consumptionFormData.name" placeholder="请输入规则名称"></el-input>
    </el-form-item>

    <div class="l-title clearfix">
      <span class="float-l min-title-h">基本设置</span>
    </div>
    <div class="form-line margin-button"></div>
    <el-form-item class="min-label-w" label="适用分组（可多选）：" prop="groupNos">
      <user-group-select :multiple="true" :collapse-tags="true" class="ps-input" style="width: 215px;" v-model="consumptionFormData.groupNos" placeholder="请选择分组"></user-group-select>
    </el-form-item>
    <el-form-item class="min-label-w" label="适用消费点：" prop="organization">
      <consume-select v-model="consumptionFormData.organization" multiple collapse-tags />
    </el-form-item>
    <el-form-item class="min-label-w is-required" label="支付限制：" :error="paymentLimitText">
      <el-select
        v-model="consumptionFormData.walletType"
        placeholder="请选择"
        class="ps-select"
        multiple
        size="small"
        :collapse-tags="true"
        @change="changePaymentLimit"
      >
        <el-option
          v-for="item in walletList"
          :key="item.key"
          :label="item.name"
          :value="item.key"
        ></el-option>
      </el-select>
      /
      <el-select
        v-model="consumptionFormData.payway"
        placeholder="请选择"
        class="ps-select"
        multiple
        size="small"
        :collapse-tags="true"
        @change="changePaymentLimit"
      >
        <el-option
          v-for="item in paywayList"
          :key="item.payway"
          :label="item.name"
          :value="item.payway"
        ></el-option>
      </el-select>
    </el-form-item>
    <el-form-item  class="min-label-w" label="日期限制：">
      <div>
        <div class="">注：勾选或选择的日期，消费规则不生效，若无需限制日期，则不勾选 </div>
        <el-checkbox-group class="ps-checkbox" v-model="consumptionFormData.exceptWeekday" >
          <el-checkbox v-for="week in weekList" :key="week.value" :label="week.value" :name="week.name">{{ week.name }}</el-checkbox>
        </el-checkbox-group>
        <!-- <el-date-picker
          type="dates"
          v-model="consumptionFormData.date"
          class="ps-picker"
          popper-class="ps-poper-picker"
          placeholder="选择具体日期">
        </el-date-picker> -->
        <div class="time-button">
          <el-button class="ps-origin-btn fixed-btn-except" type="primary" icon="el-icon-date">选择日期</el-button>
          <el-date-picker
            v-model="consumptionFormData.pickerDate"
            style="opacity: 0; position: relative; z-index: 999"
            type="daterange"
            value-format="yyyy-MM-dd"
            :picker-options="pickerOptions"
            range-separator="至"
            @change="changePackerDate"
            start-placeholder="开始日期"
            end-placeholder="结束日期">
          </el-date-picker>
        </div>
        <div style="margin-top: 5px;">
          <el-tag
            v-for="(time, index) in consumptionFormData.exceptDateRange"
            :key="time.join('-')"
            closable
            @close="closeExceptHandle(index)"
            style="margin-right: 10px; margin-bottom: 5px;"
            type="info">
            {{time.join('~')}}
          </el-tag>
        </div>
      </div>
    </el-form-item>
    <el-form-item class="min-label-w" label="消费类型：">
      <el-radio-group class="ps-radio" v-model="consumptionFormData.consumeType">
        <el-radio :label="0">计次</el-radio>
        <el-radio :label="1">扣费</el-radio>
      </el-radio-group>
    </el-form-item>
    <div class="l-title clearfix">
      <span class="float-l min-title-h">消费规则</span>
    </div>
    <div class="form-line margin-button"></div>
    <!-- 计次 start -->
    <template v-if="consumptionFormData.consumeType === 0">
      <div class="item-box-wrapper" v-for="(item, i) in consumptionFormData.countList" :key="i">
        <el-button v-if="consumptionFormData.countList.length > 1" class="delete-box" type="danger" icon="el-icon-delete" circle @click="deleteHandle(consumptionFormData.countList, i)"></el-button>
        <el-form-item  class="min-label-w" label="餐段限制：" :rules="consumtionFormRule.meal" :prop="'countList['+i+'].meal'">
          <el-select
            v-model="item.meal"
            placeholder="请选择"
            class="ps-select"
            multiple
            collapse-tags
            size="small"
            style="width:280px;"
            @change="changeJCMealHandle($event, i)"
          >
            <el-option
              v-for="meal in item.mealList"
              :key="meal.value"
              :label="meal.name"
              :value="meal.value"
              :disabled="meal.disabled"
            ></el-option>
          </el-select>
        </el-form-item>
        <div style="margin-left: 70px; margin-bottom: 15px; font-size: 13px; opacity: .5;">说明：餐段可自由组合，组合在一起的餐段共用一套规则，且已选择的餐段无法重复选择；</div>
        <div>
          <el-form-item  class="min-label-w" label="可记账次数：">
            <el-radio class="ps-radio" v-model="item.countType" label="0">不限制</el-radio>
            <el-form-item :key="item.countType+'q'" class="no-label" label="" :rules="item.countType==='1'?consumtionFormRule.countNum:[]" :prop="'countList['+i+'].countNum'">
              <el-radio class="ps-radio" v-model="item.countType" label="1">上限</el-radio>
              <el-input class="ps-input" :disabled="item.countType==='0'" v-model="item.countNum" style="width: 60px;"></el-input>
            </el-form-item>
          </el-form-item>
          <el-form-item v-if="item.countType==='1'"  class="min-label-w" label="次数达到上限时：" :prop="'countList['+i+'].reach'">
            <el-radio class="ps-radio" v-model="item.reach" label="disable">禁止消费</el-radio>
            <div>
              <el-radio class="ps-radio" v-model="item.reach" label="original">原价扣款</el-radio>
            </div>
          </el-form-item>
        </div>
        <div class="form-line margin-button"></div>
      </div>
      <div v-if="consumptionFormData.countList.length < mealList.length-1 && !consumptionFormData.allSelectMeal.includes('all')" class="" style="margin-left: 70px;">
        <el-button @click="addCountRuleHandle" type="text">新增餐段</el-button>
      </div>
    </template>
    <!-- 计次 end -->

    <!-- 扣费 start -->
    <template v-if="consumptionFormData.consumeType === 1">
      <div class="item-box-wrapper" v-for="(item, i) in consumptionFormData.deductionList" :key="i">
        <el-button v-if="consumptionFormData.deductionList.length > 1" class="delete-box" type="danger" icon="el-icon-delete" circle @click="deleteHandle(consumptionFormData.deductionList, i)"></el-button>
        <el-form-item  class="min-label-w" label="餐段限制：" :rules="consumtionFormRule.meal" :prop="'deductionList['+i+'].meal'">
          <el-select
            v-model="item.meal"
            placeholder="请选择"
            class="ps-select"
            multiple
            collapse-tags
            size="small"
            style="width:280px;"
            @change="changeDTMealHandle($event, i)"
          >
            <el-option
              v-for="meal in item.mealList"
              :key="meal.value"
              :label="meal.name"
              :value="meal.value"
              :disabled="meal.disabled"
            ></el-option>
          </el-select>
        </el-form-item>
        <!-- 扣费类型 start -->
        <div style="margin-left: 70px; margin-bottom: 15px; font-size: 13px;">说明：餐段可自由组合，组合在一起的餐段共用一套规则，且已选择的餐段无法重复选择；若选择全天，则无法再选择其他餐段。</div>
        <el-form-item  class="min-label-w" label="扣费类型：" :prop="'deductionList['+i+'].deductionType'">
          <el-radio-group class="ps-radio" v-model="item.deductionType">
            <el-radio label="WZK">无折扣</el-radio>
            <el-radio label="GD">固定</el-radio>
            <el-radio label="ZK">折扣</el-radio>
          </el-radio-group>
        </el-form-item>
        <!-- 扣费类型 end -->

        <!-- 固定 start -->
        <div style="margin-top: 15px;" v-if="item.deductionType === 'GD'">
          <el-form-item  class="min-label-w" label="固定金额：">
            <span v-if="item.fixedList.length===1" class="">
              <i @click="addMealSubsidy(i, item.fixedList, 'fixed')" class="li-icon el-icon-circle-plus"></i>
              <br />
            </span>
            <ul class="form-ul">
              <div class="clearfix form-li-box" v-for="(fixed, index) in item.fixedList" :key="index">
                <li class="form-li float-l">{{ fixed.name }}</li>
                <li class="form-li float-l li-center">
                  <el-form-item  class="no-label" label="" :show-message="false" :rules="consumtionFormRule.money1" :prop="'deductionList['+i+'].fixedList['+index+'].value'">
                    <el-input v-model="fixed.value"></el-input>
                  </el-form-item>
                </li>
                <li class="form-li float-l">{{ fixed.unit }}</li>
                <i @click="addMealSubsidy(i, item.fixedList, 'fixed')" v-if="index === item.fixedList.length-2" class="add-icon li-icon el-icon-circle-plus"></i>
                <i v-if="index === item.fixedList.length-2" @click="deleteMealSubsidy(index, item.fixedList)" class="remove-icon li-icon el-icon-remove"></i>
              </div>
            </ul>
            <div style="color: red;">说明：不填写金额则按原价扣费</div>
          </el-form-item>
          <el-form-item class="min-label-w" label="次数限制：">
            <div>
              <el-radio class="ps-radio" v-model="item.fixedCountType" label="0">不限制</el-radio>
            </div>
            <div>
              <el-radio class="ps-radio" v-model="item.fixedCountType" label="1">最多固定金额消费</el-radio>
              <el-form-item :key="item.fixedCountType" class="no-label" style="width: 80px; display: inline-block;" label="" :rules="item.fixedCountType==='1'?consumtionFormRule.count:[]" :prop="'deductionList['+i+'].fixedLimit'">
                <el-input class="ps-input" :disabled="item.fixedCountType!=='1'" v-model="item.fixedLimit"></el-input>
              </el-form-item>
              次
            </div>
          </el-form-item>
          <!-- 次数达到上限时 start -->
          <el-form-item v-if="item.fixedCountType==='1'" class="min-label-w" label="次数达到上限时：">
            <div>
              <el-radio class="ps-radio" v-model="item.fixedMaxCountType" label="disable">禁止消费</el-radio>
            </div>
            <div>
              <el-radio class="ps-radio" v-model="item.fixedMaxCountType" label="original">原价扣款</el-radio>
            </div>
          </el-form-item>
          <!-- 次数达到上限时 end -->
        </div>
        <!-- 固定 end -->

        <!-- 折扣 start -->
        <div v-if="item.deductionType==='ZK'">
          <el-form-item class="min-label-w" label="订单金额：" :prop="'deductionList['+i+'].countType'">
            <el-select
              v-model="item.discountFeeType"
              placeholder="请选择"
              class="ps-select"
              size="small"
              style="width:180px;"
            >
              <el-option
                v-for="meal in discountOrderList"
                :key="meal.value"
                :label="meal.name"
                :value="meal.value"
                :disabled="meal.disabled"
              ></el-option>
            </el-select>
          </el-form-item>
          <!-- 扣费方式 start -->
          <el-form-item  class="min-label-w" label="扣费方式：" :prop="'deductionList['+i+'].deductionType'">
            <el-radio-group class="ps-radio" v-model="item.debit_type" >
              <el-radio label="normal">普通折扣</el-radio>
              <el-radio label="take_meal_type">按取餐方式</el-radio>
            </el-radio-group>
          </el-form-item>
          <!-- 扣费方式 end -->
          <!-- 取餐方式 start -->
          <el-form-item v-if="item.debit_type === 'take_meal_type'"  class="m-l-100" label="取餐方式：">
            <el-form-item label="堂食取餐" class="m-b-0">
              <el-switch
                v-model="item.isCanteen"
                active-color="#ff9b45"
                inactive-color="#ffcda2"
              ></el-switch>
              <el-form-item class="form-item--inline m-b-0" label="" label-width="0">
                <el-checkbox-group v-model="item.canteen_take_meal_type.take_out_type" style="margin-left: 24px" :disabled="!item.isCanteen" @change="changeTakeMealType($event, item.canteen_take_meal_type)">
                  <el-checkbox label="on_scene" name="take_out_type" class="ps-checkbox">
                    堂食
                  </el-checkbox>
                  <el-checkbox label="bale" name="take_out_type" class="ps-checkbox">
                    食堂自提
                  </el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-form-item>
            <el-form-item label="外卖" class="m-l-110 m-b-0">
              <el-switch
                v-model="item.isTakeOut"
                active-color="#ff9b45"
                inactive-color="#ffcda2"
              ></el-switch>
              <el-form-item class="form-item--inline m-b-0" label="" label-width="0">
                <el-checkbox-group v-model="item.takeOut_take_meal_type.take_out_type" style="margin-left: 24px" :disabled="!item.isTakeOut" @change="changeTakeMealType($event, item.takeOut_take_meal_type)">
                  <el-checkbox label="cupboard" name="take_out_type" class="ps-checkbox">
                    取餐柜自取
                  </el-checkbox>
                  <el-checkbox label="waimai" name="take_out_type" class="ps-checkbox">
                    外卖配送
                  </el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-form-item>
          </el-form-item>
          <!-- 取餐方式 end -->
          <!-- 仅首单折扣 start -->
          <el-form-item v-if="item.debit_type === 'take_meal_type' && (item.canteen_take_meal_type.take_out_type.length || item.takeOut_take_meal_type.take_out_type.length)" class="min-label-w" label="仅首单折扣：">
            <el-switch
                v-model="item.takeMealFirst"
                active-color="#ff9b45"
                inactive-color="#ffcda2"
              ></el-switch>
          </el-form-item>
          <!-- 仅首单折扣 end -->
          <el-form-item v-if="item.discountFeeType !== 'normal'" class="min-label-w" :label="`餐段内${item.discountFeeType==='once'?'':'累计'}消费`">
            <el-select
              v-model="item.discountMealConsumption"
              placeholder="请选择"
              class="ps-select"
              size="small"
              style="width:180px;"
            >
              <el-option
                v-for="meal in discountMealConsumptionList"
                :key="meal.value"
                :label="meal.name"
                :value="meal.value"
                :disabled="meal.disabled"
              ></el-option>
            </el-select>
            <el-form-item  class="no-label" style="margin-left: 10px; width: 80px; display: inline-block;" label="" :rules="consumtionFormRule.money" :prop="'deductionList['+i+'].discountFee'">
              <el-input class="ps-input"  v-model="item.discountFee"></el-input>
            </el-form-item>
          </el-form-item>
          <el-form-item v-if="item.discountFeeType !== 'normal' && item.discountMealConsumption === 'gt'" class="min-label-w" label="" :prop="'deductionList['+i+'].discountRang'" :rules="consumtionFormRule.requiredKey">
            <el-select
              v-model="item.discountRang"
              placeholder="请选择"
              class="ps-select"
              size="small"
              style="width:180px;"
            >
              <el-option
                v-for="meal in discountRangList"
                :key="meal.value"
                :label="meal.name"
                :value="meal.value"
                :disabled="meal.disabled"
              ></el-option>
            </el-select>
            进行打折
          </el-form-item>
          <el-form-item v-if="item.debit_type === 'normal'" class="min-label-w" label="扣费类型：">
            <span v-if="item.discountList.length===1" class="">
              <i @click="addMealSubsidy(i, item.discountList, 'discount')" class="li-icon el-icon-circle-plus"></i>
              <br />
            </span>
            <ul class="form-ul">
              <div class="clearfix form-li-box" v-for="(subsidy, index) in item.discountList" :key="index">
                <li class="form-li float-l">{{ subsidy.name }}</li>
                <li class="form-li float-l li-center">
                  <el-form-item  class="no-label" label="" :show-message="false" :rules="consumtionFormRule.money1" :prop="'deductionList['+i+'].discountList['+index+'].value'">
                    <el-input v-model="subsidy.value"></el-input>
                  </el-form-item>
                </li>
                <li class="form-li float-l">{{ subsidy.unit }}</li>
                <i @click="addMealSubsidy(index, item.discountList, 'discount')" v-if="index === item.discountList.length-2" class="add-icon li-icon el-icon-circle-plus"></i>
                <i v-if="index === item.discountList.length-2" @click="deleteMealSubsidy(index, item.discountList)" class="remove-icon li-icon el-icon-remove"></i>
              </div>
            </ul>
            <div style="color: red;">说明：不填写折扣比例则按原价扣费</div>
          </el-form-item>
          <div v-if="item.debit_type !== 'normal'" class="m-l-70">
            <div v-if="item.isCanteen">
              <el-form-item v-for="(take_meal_discount, j) in item.canteen_take_meal_type.discountList" :key="take_meal_discount.meal_type" class="min-label-w" :label="takeMealTypeKeys[take_meal_discount.meal_type]">
                <div>
                  <span v-if="take_meal_discount.discount.length===1" class="">
                    <i @click="addMealSubsidy(i, take_meal_discount.discount, 'discount')" class="li-icon el-icon-circle-plus"></i>
                    <br />
                  </span>
                  <ul class="form-ul">
                    <div class="clearfix form-li-box" v-for="(subsidy, index) in take_meal_discount.discount" :key="index">
                      <li class="form-li float-l">{{ subsidy.name }}</li>
                      <li class="form-li float-l li-center">
                        <el-form-item  class="no-label" label="" :show-message="false" :rules="consumtionFormRule.money1" :prop="'deductionList['+i+'].canteen_take_meal_type.discountList['+j+'].discount['+index+'].value'">
                          <el-input v-model="subsidy.value"></el-input>
                        </el-form-item>
                      </li>
                      <li class="form-li float-l">{{ subsidy.unit }}</li>
                      <i @click="addMealSubsidy(index, take_meal_discount.discount, 'discount')" v-if="index === take_meal_discount.discount.length-2" class="add-icon li-icon el-icon-circle-plus"></i>
                      <i v-if="index === take_meal_discount.discount.length-2" @click="deleteMealSubsidy(index, take_meal_discount.discount)" class="remove-icon li-icon el-icon-remove"></i>
                    </div>
                  </ul>
                  <div style="color: red;">说明：不填写折扣比例则按原价扣费</div>
                </div>
                <div>
                  <!-- 取餐方式每人最大折扣金额 -->
                  <el-form-item class="min-label-140" label="每人最大折扣金额：">
                    <el-select
                      v-model="take_meal_discount.discountMaxFeeType"
                      placeholder="请选择"
                      class="ps-select"
                      size="small"
                      style="width:180px;"
                      @change="changeMealDiscountMaxFeeTypeHandle($event, take_meal_discount, i)"
                    >
                      <el-option
                        v-for="meal in discountMaxList"
                        :key="meal.value"
                        :label="meal.name"
                        :value="meal.value"
                        :disabled="meal.disabled"
                      ></el-option>
                    </el-select>
                    <el-form-item :key="take_meal_discount.discountMaxFeeType+'x'" class="no-label" style="margin-left: 10px; width: 80px; display: inline-block;" label="" :rules="take_meal_discount.discountMaxFeeType!=='normal'?consumtionFormRule.money:[]" :prop="'deductionList['+i+'].canteen_take_meal_type.discountList['+j+'].discountMaxFee'">
                      <el-input class="ps-input" :disabled="take_meal_discount.discountMaxFeeType==='normal'"  v-model="take_meal_discount.discountMaxFee"></el-input>
                    </el-form-item>
                  </el-form-item>
                  <el-form-item v-if="take_meal_discount.discountMaxFeeType==='o_once'" key="o_once2" class="min-label-140" label="每人最大折扣金额上限：">
                    <el-radio class="ps-radio" v-model="take_meal_discount.discountMaxOnceFeeType" label="o_once">整单原价扣款</el-radio>
                    <el-radio class="ps-radio" v-model="take_meal_discount.discountMaxOnceFeeType" label="o_all">超出部分原价扣款</el-radio>
                  </el-form-item>
                  <el-form-item v-if="take_meal_discount.discountMaxFeeType==='t_all'" key="t_all2" class="min-label-140" label="每人最大折扣金额上限：">
                    <el-radio class="ps-radio" v-model="take_meal_discount.discountMaxOnceFeeType" label="t_once">整单原价扣款</el-radio>
                    <el-radio class="ps-radio" v-model="take_meal_discount.discountMaxOnceFeeType" label="t_all">超出部分原价扣款</el-radio>
                  </el-form-item>
                  <!-- 取餐方式限额 start -->
                  <el-form-item v-if="take_meal_discount.discountMaxFeeType==='normal'" class="min-label-140" label="限额：">
                    <el-radio class="ps-radio" v-model="take_meal_discount.maxLimitType" label="0">不限额</el-radio>
                    <el-radio class="ps-radio" v-model="take_meal_discount.maxLimitType" label="1">最高</el-radio>
                    <el-form-item  class="no-label" style="width: 80px; display: inline-block;" :key="take_meal_discount.deductionType+take_meal_discount.maxLimitType" label="" :rules="take_meal_discount.maxLimitType==='1'?consumtionFormRule.money:[]" :prop="'deductionList['+i+'].canteen_take_meal_type.discountList['+j+'].maxLimitFee'">
                      <el-input class="ps-input" :disabled="take_meal_discount.maxLimitType!=='1'" v-model="take_meal_discount.maxLimitFee"></el-input>
                    </el-form-item>
                    <span style="margin-left: 15px;">元</span>
                    <div style="color: red;">说明：消费金额超过限额时，不允许消费</div>
                  </el-form-item>
                  <!-- 取餐方式限额 end -->
                </div>
              </el-form-item>
            </div>
            <div v-if="item.isTakeOut">
              <el-form-item v-for="(take_meal_discount, j) in item.takeOut_take_meal_type.discountList" :key="take_meal_discount.meal_type" class="min-label-w" :label="takeMealTypeKeys[take_meal_discount.meal_type]">
                <div>
                  <span v-if="take_meal_discount.discount.length===1" class="">
                    <i @click="addMealSubsidy(i, take_meal_discount.discount, 'discount')" class="li-icon el-icon-circle-plus"></i>
                    <br />
                  </span>
                  <ul class="form-ul">
                    <div class="clearfix form-li-box" v-for="(subsidy, index) in take_meal_discount.discount" :key="index">
                      <li class="form-li float-l">{{ subsidy.name }}</li>
                      <li class="form-li float-l li-center">
                        <el-form-item  class="no-label" label="" :show-message="false" :rules="consumtionFormRule.money1" :prop="'deductionList['+i+'].takeOut_take_meal_type.discountList['+j+'].discount['+index+'].value'">
                          <el-input v-model="subsidy.value"></el-input>
                        </el-form-item>
                      </li>
                      <li class="form-li float-l">{{ subsidy.unit }}</li>
                      <i @click="addMealSubsidy(index, take_meal_discount.discount, 'discount')" v-if="index === take_meal_discount.discount.length-2" class="add-icon li-icon el-icon-circle-plus"></i>
                      <i v-if="index === take_meal_discount.discount.length-2" @click="deleteMealSubsidy(index, take_meal_discount.discount)" class="remove-icon li-icon el-icon-remove"></i>
                    </div>
                  </ul>
                  <div style="color: red;">说明：不填写折扣比例则按原价扣费</div>
                </div>
                <div>
                  <!-- 取餐方式每人最大折扣金额 -->
                  <el-form-item class="min-label-140" label="每人最大折扣金额：">
                    <el-select
                      v-model="take_meal_discount.discountMaxFeeType"
                      placeholder="请选择"
                      class="ps-select"
                      size="small"
                      style="width:180px;"
                      @change="changeMealDiscountMaxFeeTypeHandle($event, take_meal_discount, i)"
                    >
                      <el-option
                        v-for="meal in discountMaxList"
                        :key="meal.value"
                        :label="meal.name"
                        :value="meal.value"
                        :disabled="meal.disabled"
                      ></el-option>
                    </el-select>
                    <el-form-item :key="take_meal_discount.discountMaxFeeType+'x'" class="no-label" style="margin-left: 10px; width: 80px; display: inline-block;" label="" :rules="take_meal_discount.discountMaxFeeType!=='normal'?consumtionFormRule.money:[]" :prop="'deductionList['+i+'].takeOut_take_meal_type.discountList['+j+'].discountMaxFee'">
                      <el-input class="ps-input" :disabled="take_meal_discount.discountMaxFeeType==='normal'"  v-model="take_meal_discount.discountMaxFee"></el-input>
                    </el-form-item>
                  </el-form-item>
                  <el-form-item v-if="take_meal_discount.discountMaxFeeType==='o_once'" key="o_once2" class="min-label-140" label="每人最大折扣金额上限：">
                    <el-radio class="ps-radio" v-model="take_meal_discount.discountMaxOnceFeeType" label="o_once">整单原价扣款</el-radio>
                    <el-radio class="ps-radio" v-model="take_meal_discount.discountMaxOnceFeeType" label="o_all">超出部分原价扣款</el-radio>
                  </el-form-item>
                  <el-form-item v-if="take_meal_discount.discountMaxFeeType==='t_all'" key="t_all2" class="min-label-140" label="每人最大折扣金额上限：">
                    <el-radio class="ps-radio" v-model="take_meal_discount.discountMaxOnceFeeType" label="t_once">整单原价扣款</el-radio>
                    <el-radio class="ps-radio" v-model="take_meal_discount.discountMaxOnceFeeType" label="t_all">超出部分原价扣款</el-radio>
                  </el-form-item>
                  <!-- 取餐方式限额 start -->
                  <el-form-item v-if="take_meal_discount.discountMaxFeeType==='normal'" class="min-label-140" label="限额：">
                    <el-radio class="ps-radio" v-model="take_meal_discount.maxLimitType" label="0">不限额</el-radio>
                    <el-radio class="ps-radio" v-model="take_meal_discount.maxLimitType" label="1">最高</el-radio>
                    <el-form-item  class="no-label" style="width: 80px; display: inline-block;" :key="take_meal_discount.deductionType+take_meal_discount.maxLimitType" label="" :rules="take_meal_discount.maxLimitType==='1'?consumtionFormRule.money:[]" :prop="'deductionList['+i+'].takeOut_take_meal_type.discountList['+j+'].maxLimitFee'">
                      <el-input class="ps-input" :disabled="take_meal_discount.maxLimitType!=='1'" v-model="take_meal_discount.maxLimitFee"></el-input>
                    </el-form-item>
                    <span style="margin-left: 15px;">元</span>
                    <div style="color: red;">说明：消费金额超过限额时，不允许消费</div>
                  </el-form-item>
                  <!-- 取餐方式限额 end -->
                </div>
              </el-form-item>
            </div>
          </div>
          <div v-if="item.debit_type === 'normal'">
            <!-- 每人最大折扣金额 -->
            <el-form-item class="min-label-w" label="每人最大折扣金额：">
              <el-select
                v-model="item.discountMaxFeeType"
                placeholder="请选择"
                class="ps-select"
                size="small"
                style="width:180px;"
                @change="changeDiscountMaxFeeTypeHandle($event, item, i)"
              >
                <el-option
                  v-for="meal in discountMaxList"
                  :key="meal.value"
                  :label="meal.name"
                  :value="meal.value"
                  :disabled="meal.disabled"
                ></el-option>
              </el-select>
              <el-form-item :key="item.discountMaxFeeType+'x'" class="no-label" style="margin-left: 10px; width: 80px; display: inline-block;" label="" :rules="item.discountMaxFeeType!=='normal'?consumtionFormRule.money:[]" :prop="'deductionList['+i+'].discountMaxFee'">
                <el-input class="ps-input" :disabled="item.discountMaxFeeType==='normal'"  v-model="item.discountMaxFee"></el-input>
              </el-form-item>
            </el-form-item>
            <!--  -->
            <el-form-item v-if="item.discountMaxFeeType==='o_once'" key="o_once2" class="min-label-w" label="每人最大折扣金额上限：">
              <el-radio class="ps-radio" v-model="item.discountMaxOnceFeeType" label="o_once">整单原价扣款</el-radio><br />
              <el-radio class="ps-radio" v-model="item.discountMaxOnceFeeType" label="o_all">超出部分原价扣款</el-radio>
            </el-form-item>
            <el-form-item v-if="item.discountMaxFeeType==='t_all'" key="t_all2" class="min-label-w" label="每人最大折扣金额上限：">
              <el-radio class="ps-radio" v-model="item.discountMaxOnceFeeType" label="t_once">整单原价扣款</el-radio><br />
              <el-radio class="ps-radio" v-model="item.discountMaxOnceFeeType" label="t_all">超出部分原价扣款</el-radio>
            </el-form-item>
          </div>
        </div>
        <!-- 折扣 end -->

        <!-- 限额 start -->
        <el-form-item v-if="item.deductionType==='GD' && item.fixedMaxCountType==='original' || item.deductionType==='ZK' && item.discountMaxFeeType==='normal' && item.debit_type === 'normal'" class="min-label-w" label="限额：">
          <div>
            <el-radio class="ps-radio" v-model="item.maxLimitType" label="0">不限额</el-radio>
          </div>
          <div>
            <el-radio class="ps-radio" v-model="item.maxLimitType" label="1">最高</el-radio>
            <el-form-item  class="no-label" style="width: 80px; display: inline-block;" :key="item.deductionType+item.maxLimitType" label="" :rules="item.maxLimitType==='1'?consumtionFormRule.money:[]" :prop="'deductionList['+i+'].maxLimitFee'">
              <el-input class="ps-input" :disabled="item.maxLimitType!=='1'" v-model="item.maxLimitFee"></el-input>
            </el-form-item>
            <span style="margin-left: 15px;">元</span>
          </div>
          <div style="color: red;">说明：消费金额超过限额时，不允许消费</div>
        </el-form-item>
        <!-- 限额 end -->

        <!-- 餐补 start -->
        <el-form-item  class="min-label-w" label="使用餐补：" :prop="'deductionList['+i+'].useMealSubsidy'">
          <el-switch active-color="#ff9b45" v-model="item.useMealSubsidy"></el-switch>
        </el-form-item>
        <el-form-item v-if="item.useMealSubsidy"  class="min-label-w" label="餐补金额：">
          <div>
            <el-radio class="ps-radio" v-model="item.mealSubsidyType" label="total">累计餐补</el-radio>
            <el-form-item :key="item.mealSubsidyType" class="form-item--inline no-label w-300" label="" :rules="item.useMealSubsidy && item.mealSubsidyType === 'total' ? consumtionFormRule.money : []" :prop="'deductionList['+i+'].mealSubsidyFee'">
              <el-input class="ps-input" :disabled="item.mealSubsidyType !== 'total'" v-model="item.mealSubsidyFee" style="width: 80px;"></el-input>
            </el-form-item>
          </div>
          <div>
            <el-radio class="ps-radio" v-model="item.mealSubsidyType" label="once">单次餐补</el-radio>
            <span v-if="item.subsidyList.length===1" class="">
              <i @click="addMealSubsidy(i, item.subsidyList, 'subsidy')" class="li-icon el-icon-circle-plus"></i>
              <br />
            </span>
            <ul v-if="item.mealSubsidyType === 'once'" class="form-ul">
              <div class="clearfix form-li-box" v-for="(subsidy, index) in item.subsidyList" :key="index">
                <li class="form-li float-l">{{ subsidy.name }}</li>
                <li class="form-li float-l li-center">
                  <el-form-item  class="no-label" label="" :show-message="false" :rules="item.useMealSubsidy && item.mealSubsidyType === 'once' ? consumtionFormRule.money1 : []" :prop="'deductionList['+i+'].subsidyList['+index+'].value'">
                    <el-input v-model="subsidy.value"></el-input>
                  </el-form-item>
                </li>
                <li class="form-li float-l">{{ subsidy.unit }}</li>
                <i @click="addMealSubsidy(index, item.subsidyList, 'subsidy')" v-if="index === item.subsidyList.length-2" class="add-icon li-icon el-icon-circle-plus"></i>
                <i v-if="index === item.subsidyList.length-2" @click="deleteMealSubsidy(index, item.subsidyList)" class="remove-icon li-icon el-icon-remove"></i>
              </div>
            </ul>
            <div v-if="item.mealSubsidyType === 'once'" style="color: red;">说明：不填写金额则餐补为0元</div>
          </div>
        </el-form-item>
        <!-- 餐补 end -->

        <!-- 消费限制 start -->
        <template v-if="item.deductionType === 'WZK'">
          <el-form-item  class="min-label-w" label="消费限制：">
            <el-radio class="ps-radio" v-model="item.consumtionType" label="normal">不限制</el-radio>
            <el-form-item class="no-label" label="" :key="item.consumtionType" :rules="item.consumtionType === 'once' ? consumtionFormRule.money : []" :prop="'deductionList['+i+'].countUpperFee'">
              <el-radio class="ps-radio" v-model="item.consumtionType" label="once">单次最高消费</el-radio>
              <el-input class="ps-input max-input" :disabled="item.consumtionType !== 'once'" v-model="item.countUpperFee"></el-input>
              元
            </el-form-item>
            <el-form-item class="no-label" label="" :key="item.consumtionType+1" :rules="item.consumtionType === 'day' ? consumtionFormRule.money : []" :prop="'deductionList['+i+'].dayUpperFee'">
              <el-radio class="ps-radio" v-model="item.consumtionType" label="day">每日最高消费</el-radio>
              <el-input class="ps-input max-input" :disabled="item.consumtionType !== 'day'" v-model="item.dayUpperFee"></el-input>
              元
            </el-form-item>
          </el-form-item>
          <!-- <div class="form-item min-label-w">
            <span class="form-label">消费限制：</span>
            <div class="form-content" :key="item.consumtionType">
              <el-radio class="ps-radio" v-model="item.consumtionType" label="normal">不限制</el-radio>
              <el-form-item class="no-label" label="" :rules="item.consumtionType === 'once' ? consumtionFormRule.money : []" :prop="'deductionList['+i+'].countUpperFee'">
                <el-radio class="ps-radio" v-model="item.consumtionType" label="once">单次最高消费</el-radio>
                <el-input class="ps-input" :disabled="item.consumtionType !== 'once'" v-model="item.countUpperFee" style="width: 60px;"></el-input>
                元
              </el-form-item>
              <el-form-item class="no-label" label="" :rules="item.consumtionType==='day'?consumtionFormRule.money:[]" :prop="'deductionList['+i+'].dayUpperFee'">
                <el-radio class="ps-radio" v-model="item.consumtionType" label="day">每日最高消费</el-radio>
                <el-input class="ps-input" :disabled="item.consumtionType !== 'day'" v-model="item.dayUpperFee" style="width: 60px;"></el-input>
                元
              </el-form-item>
            </div>
          </div> -->
        </template>
        <!-- 消费限制 end -->
        <div class="form-line margin-button"></div>
      </div>
      <div v-if="consumptionFormData.deductionList.length < 6 && !consumptionFormData.deductionAllSelectMeal.includes('all')" class="" style="margin-left: 70px;">
        <el-button @click="addDTRuleHandle" type="text">新增餐段</el-button>
      </div>
    </template>
    <!-- 扣费 end -->
    <el-form-item label="备注" class="min-label-w">
      <el-input type="textarea" class="ps-input" style="max-width: 500px;" :autosize="{ minRows: 4, maxRows: 8}" v-model="consumptionFormData.tip" ></el-input>
    </el-form-item>
    <!-- footer start -->
    <div class="footer" style="text-align: center;">
      <el-button class="ps-origin-btn" type="primary" @click="saveConsumHandle">保存</el-button>
    </div>
    </el-form>
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, to, deepClone, divide, times, parseTime, checkArrayOverlap } from '@/utils'
import UserGroupSelect from '@/components/UserGroupSelect'
import ConsumeSelect from '@/components/ConsumeSelect'
import { WALLET_LIST, MEAL_LIST } from "./constants"
import { validataPrice, validataPriceCanEmpty } from "./validata"

export default {
  name: 'ingredientsAdmin',
  props: {},
  components: {
    UserGroupSelect,
    ConsumeSelect
  },
  data() {
    let validateCount = (rule, value, callback) => {
      let reg = /^\d+$/
      if (!value) {
        return callback();
      } else {
        if (!reg.test(value)) {
          callback(new Error("格式错误"));
        } else {
          callback();
        }
      }
    };
    return {
      type: '',
      initLoading: false,
      isLoading: false, // 刷新数据
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < (Date.now() - 24 * 60 * 60 * 1000);
        }
      },
      consumptionFormData: {
        ruleNo: '',
        name: '', // 名称
        organization: '', // 适用消费点
        groupNos: [], // 分组
        walletType: [], // 支付限制
        payway: [],
        exceptWeekday: [], // 星期限制
        exceptDateRange: [], // 日期限制
        pickerDate: [],
        consumeType: 0, // 消费类型 0 1
        tip: '',
        countList: [ // 计次的
          // {
          //   meal: [], // 餐段
          //   mealList: deepClone(MEAL_LIST),
          //   countType: '0', // 可记账次数
          //   countNum: '', // 上限次数
          //   reach: 'disable' // 次数达到上限 disable original
          // }
        ],
        allSelectMeal: [], // 计次的所有选中的餐段，方便控制
        deductionAllSelectMeal: [], // 扣费的所有的选中的餐段，方便控制
        deductionList: [ // 扣费的
          // {
          //   meal: [], // 餐段
          //   mealList: deepClone(MEAL_LIST),
          //   deductionType: 'WZK', // 扣费类型  JC - 记次, WZK - 扣费-无折扣, GD - 扣费-固定, ZK - 扣费-折扣
          //   useMealSubsidy: false, // 使用餐补
          //   mealSubsidyType: 'total', // 餐补类型
          //   mealSubsidyFee: '', // 累计金额
          //   subsidyList: [
          //     { index: 1, value: '', name: '第1次', unit: '元' },
          //     { index: -1, value: '', name: '之后每次消费', unit: '元' }
          //   ], // 单餐餐补list
          //   consumtionType: 'normal', // 消费限制 normal无限制 once单次消费限制 day每日消费限制
          //   countUpperFee: '', // 单次最高消费
          //   dayUpperFee: '', // 每日最高消费
          //   fixedList: [
          //     { index: 1, value: '', name: '第1次', unit: '元' },
          //     { index: -1, value: '', name: '之后每次消费', unit: '元' }
          //   ],
          //   fixedCountType: '0', // 次数限制
          //   fixedLimit: '', // 固定次数
          //   fixedMaxCountType: 'disable', // 次数达到上限时
          //   maxLimitType: '0', // 限额
          //   maxLimitFee: '', // 限额最高
          //   discountFeeType: 'normal', // 订单金额
          //   discountMealConsumption: 'lte',
          //   discountFee: '', // 1
          //   discountMaxFeeType: 'normal',
          //   discountMaxOnceFeeType: 'o_once',
          //   discountMaxFee: '',
          //   discountRang: '', // all, out 优惠范围；order_limit != normal and option = gt 时生效
          //   discountList: [
          //     { index: 1, value: '', name: '第1次', unit: '%' },
          //     { index: -1, value: '', name: '之后每次消费', unit: '%' }
          //   ]
          //   debit_type: '', // 扣费方式 normal 普通折扣， take_meal_type 取餐方式折扣
          //   isCanteen: false,
          //   isTakeOut: false,
          //   canteen_take_meal_type: {
          //     take_out_type: [],
          //     discountList: []
          //  },
          //  takeOut_take_meal_type: {
          //    take_out_type: [],
          //    discountList: []
          //  },
          // takeMealFirst: false // 仅首单折扣
          // },
        ]
      },
      consumtionFormRule: {
        name: [{ required: true, message: '消费规则名称不能为空', trigger: "blur" }],
        groupNos: [{ required: true, message: '适用分组不能为空', trigger: "change" }],
        organization: [{ required: true, message: '消费点不能为空', trigger: "blur" }],
        walletType: [{ required: true, message: '支付限制不能为空', trigger: "change" }],
        consumeType: [{ required: true, message: '支付类型不能为空', trigger: "change" }],
        meal: [{ required: true, message: '请选择餐段', trigger: "change" }],
        countNum: [
          { required: true, message: '可记账次数上限不能为空', trigger: "change" },
          { validator: validateCount, trigger: "change" }
        ], // 记账次数
        money: [
          { required: true, message: '请输入金额', trigger: "change" },
          { validator: validataPrice, trigger: "change" }
        ], // 金额校验
        money1: [{ validator: validataPriceCanEmpty, trigger: "change" }], // 金额校验
        count: [
          { required: true, message: '不能为空', trigger: "change" },
          { validator: validateCount, trigger: "change" }
        ],
        requiredKey: [
          { required: true, message: '请先选择', trigger: "change" }
        ]
      },
      walletList: WALLET_LIST, // 支付限制钱包
      weekList: [
        { name: '周一', value: 'Mon' },
        { name: '周二', value: 'Tue' },
        { name: '周三', value: 'Wed' },
        { name: '周四', value: 'Thu' },
        { name: '周五', value: 'Fri' },
        { name: '周六', value: 'Sat' },
        { name: '周日', value: 'Sun' }
      ],
      mealList: deepClone(MEAL_LIST),
      discountOrderList: [
        { name: '不限制', value: 'normal' },
        { name: '不累计', value: 'once' },
        { name: '累计', value: 'day' }
      ],
      discountMealConsumptionList: [
        { name: '小于等于', value: 'lte' },
        { name: '大于', value: 'gt' }
      ],
      discountMaxList: [
        { name: '不限制', value: 'normal' },
        { name: '单笔', value: 'o_once' },
        { name: '合计', value: 't_all' }
      ],
      discountRangList: [
        { name: '仅超出部分', value: 'out' },
        { name: '整笔订单', value: 'all' }
      ],
      paywayList: [],
      paymentLimitText: "",
      takeMealTypeKeys: {
        on_scene: '堂食',
        bale: '食堂自提',
        cupboard: '取餐柜自取',
        waimai: '外卖配送'
      }
    }
  },
  computed: {
  },
  created() {
    this.type = this.$route.params.type
    if (this.type === 'add' && this.$route.query.group) {
      this.consumptionFormData.groupNos = [this.$route.query.group]
    }
    this.initLoad()
    this.getPaywayList()
    if (this.$route.query.data) {
      this.initDefaul(JSON.parse(decodeURIComponent(this.$route.query.data)))
      this.initLoading = true
    }
  },
  mounted() {},
  methods: {
    initLoad() {
      if (this.type === 'add') {
        this.addCountRuleHandle()
        this.addDTRuleHandle()
      }
    },
    // 节下流咯
    searchHandle: debounce(function() {
    }, 300),
    // 编辑数据初始化
    initDefaul(data) {
      console.log('data', data)
      // 公共部分
      this.consumptionFormData.ruleNo = data.rule_no
      this.consumptionFormData.name = data.name
      this.consumptionFormData.organization = data.rule_info.org_names ? Object.values(data.rule_info.org_names) : ''
      this.consumptionFormData.groupNos = data.rule_info.group_names ? Object.values(data.rule_info.group_names) : []
      this.consumptionFormData.walletType = data.wallet_type
      this.consumptionFormData.payway = data.payway
      this.consumptionFormData.tip = data.tip
      this.consumptionFormData.consumeType = data.consume_type
      this.consumptionFormData.exceptWeekday = data.weekday ? data.weekday : []
      if (data.except_date_range) {
        this.consumptionFormData.exceptDateRange = data.except_date_range.map(time => {
          return [parseTime(time.start_date, '{y}-{m}-{d}'), parseTime(time.end_date, '{y}-{m}-{d}')]
        })
      }
      data.rule.forEach((rule, i) => {
        if (data.consume_type === 0) {
          this.consumptionFormData.countList.push(
            {
              meal: rule.meal,
              mealList: deepClone(this.mealList),
              countType: rule.discount_rule.count ? '1' : '0', // 可记账次数
              countNum: rule.discount_rule.count ? rule.discount_rule.count : '', // 上限次数
              reach: rule.discount_rule.reach // 次数达到上限 disable original
            }
          )
        } else {
          let deduction = {
            meal: rule.meal,
            mealList: deepClone(MEAL_LIST),
            deductionType: rule.rule_type, // 类型
            useMealSubsidy: rule.subsidy !== 'close', // 使用餐补
            mealSubsidyType: rule.subsidy !== 'close' ? rule.subsidy : 'total', // 餐补类型
            mealSubsidyFee: divide(rule.subsidy_rule.total), // 累计金额
            consumtionType: rule.discount_rule.consume_limit ? rule.discount_rule.consume_limit : 'normal', // 消费限制
            countUpperFee: rule.discount_rule.consume_limit === 'once' ? divide(rule.discount_rule.amount) : '', // 消费限制
            dayUpperFee: rule.discount_rule.consume_limit === 'day' ? divide(rule.discount_rule.amount) : '', // 消费限制
            discountMaxOnceFeeType: '',
            debit_type: rule.debit_type ? rule.debit_type : 'normal',
            isCanteen: false,
            isTakeOut: false,
            canteen_take_meal_type: {
              take_out_type: [],
              discountList: []
            },
            takeOut_take_meal_type: {
              take_out_type: [],
              discountList: []
            }
          }
          // 餐补
          let subsidyList = []
          if (deduction.useMealSubsidy && deduction.mealSubsidyType === 'once') {
            if (rule.subsidy_rule.rule) {
              subsidyList = rule.subsidy_rule.rule.map((v, i) => {
                return { index: i + 1, value: v > -1 ? divide(v) : '', name: `第${i + 1}次`, unit: '元' }
              })
            }
            subsidyList.push({ index: -1, value: rule.subsidy_rule.after > -1 ? divide(rule.subsidy_rule.after) : '', name: '之后每次消费', unit: '元' })
          } else {
            // 不开启餐补也要给默认数据
            subsidyList = [
              { index: 1, value: '', name: '第1次', unit: '元' },
              { index: -1, value: '', name: '之后每次消费', unit: '元' }
            ]
          }
          deduction.subsidyList = subsidyList
          let other = {}
          if (rule.rule_type === 'WZK') {
            other = {
              fixedList: [
                { index: 1, value: '', name: '第1次', unit: '元' },
                { index: -1, value: '', name: '之后每次消费', unit: '元' }
              ],
              fixedCountType: '0', // 次数限制
              fixedLimit: '', // 固定次数
              fixedMaxCountType: 'disable', // 次数达到上限时
              maxLimitType: '0', // 限额
              maxLimitFee: '', // 限额最高
              discountFeeType: 'normal', // 订单金额
              discountMealConsumption: 'lte',
              discountRang: '',
              discountFee: '', // 1
              discountMaxFeeType: 'normal',
              discountMaxOnceFeeType: 'o_once',
              discountMaxFee: '',
              discountList: [
                { index: 1, value: '', name: '第1次', unit: '%' },
                { index: -1, value: '', name: '之后每次消费', unit: '%' }
              ]
            }
          }

          // 固定
          if (rule.rule_type === 'GD') {
            deduction.fixedLimit = rule.discount_rule.count
            deduction.fixedCountType = (rule.discount_rule.count !== null && rule.discount_rule.count >= 0) ? '1' : '0'
            deduction.fixedMaxCountType = rule.discount_rule.reach ? rule.discount_rule.reach : 'disable'
            deduction.maxLimitType = rule.discount_rule.amount ? '1' : '0'
            deduction.maxLimitFee = rule.discount_rule.amount ? divide(rule.discount_rule.amount) : ''

            let fixdList = []
            if (rule.discount_rule.rule) {
              fixdList = rule.discount_rule.rule.map((v, i) => {
                return { index: i + 1, value: v > -1 ? divide(v) : '', name: `第${i + 1}次`, unit: '元' }
              })
            }
            fixdList.push({ index: -1, value: rule.discount_rule.after > -1 ? divide(rule.discount_rule.after) : '', name: '之后每次消费', unit: '元' })
            deduction.fixedList = fixdList

            other = {
              discountFeeType: 'normal', // 订单金额
              discountMealConsumption: 'lte',
              discountRang: '',
              discountFee: '', // 1
              discountMaxFeeType: 'normal',
              discountMaxOnceFeeType: 'o_once',
              discountMaxFee: '',
              discountList: [
                { index: 1, value: '', name: '第1次', unit: '%' },
                { index: -1, value: '', name: '之后每次消费', unit: '%' }
              ]
            }
          }

          if (rule.rule_type === 'ZK') {
            deduction.discountFeeType = rule.discount_rule.order_limit ? rule.discount_rule.order_limit : 'normal'
            if (rule.discount_rule.consume_limit && rule.discount_rule.consume_limit === 'o_all') {
              deduction.discountMaxFeeType = 'o_once'
            } else {
              deduction.discountMaxFeeType = rule.discount_rule.consume_limit ? rule.discount_rule.consume_limit : 'normal'
              if (deduction.discountMaxFeeType === 't_once') {
                deduction.discountMaxFeeType = 't_all'
              }
            }
            if (deduction.discountMaxFeeType !== 'normal') {
              deduction.discountMaxOnceFeeType = rule.discount_rule.consume_limit
            }
            deduction.discountMaxFee = rule.discount_rule.consume_limit !== 'normal' ? divide(rule.discount_rule.fee) : ''
            deduction.discountMealConsumption = deduction.discountFeeType !== 'normal' ? rule.discount_rule.option : 'lte'
            deduction.discountRang = (rule.discount_rule && rule.discount_rule.discount_rang) ? rule.discount_rule.discount_rang : ''
            deduction.discountFee = rule.discount_rule.money ? divide(rule.discount_rule.money) : ''

            deduction.fixedMaxCountType = rule.discount_rule.reach ? rule.discount_rule.reach : 'disable'
            deduction.maxLimitType = rule.discount_rule.amount ? '1' : '0'
            deduction.maxLimitFee = rule.discount_rule.amount ? divide(rule.discount_rule.amount) : ''

            if (rule.debit_type !== 'take_meal_type') {
              let discountList = []
              if (rule.discount_rule.rule) {
                discountList = rule.discount_rule.rule.map((v, i) => {
                  return { index: i + 1, value: v > -1 ? v : '', name: `第${i + 1}次`, unit: '%' }
                })
              }
              discountList.push({ index: -1, value: rule.discount_rule.after > -1 ? rule.discount_rule.after : '', name: '之后每次消费', unit: '%' })
              deduction.discountList = discountList
            } else {
              deduction.discountList = [
                { index: 1, value: '', name: '第1次', unit: '%' },
                { index: -1, value: '', name: '之后每次消费', unit: '%' }
              ]
              if (rule.discount_rule.rule && rule.discount_rule.take_meal_type_rule) {
                deduction.takeMealFirst = !!rule.discount_rule.take_meal_first
                let takeMealTypes = Object.keys(rule.discount_rule.take_meal_type_rule)
                let canteenKey = ['on_scene', 'bale']
                takeMealTypes.forEach(type => {
                  let mealTypeItem = rule.discount_rule.take_meal_type_rule[type]
                  if (canteenKey.includes(type)) { // 堂食
                    deduction.isCanteen = true
                    deduction.canteen_take_meal_type.take_out_type.push(type)
                    let discountList = mealTypeItem.rule.map((v, i) => {
                      return { index: i + 1, value: v > -1 ? v : '', name: `第${i + 1}次`, unit: '%' }
                    })
                    discountList.push({ index: -1, value: mealTypeItem.after > -1 ? mealTypeItem.after : '', name: '之后每次消费', unit: '%' })
                    let discountListItem = {
                      meal_type: type,
                      discount: discountList,
                      discountMaxFeeType: '', // 每人最大折扣类型
                      discountMaxFee: mealTypeItem.consume_limit !== 'normal' ? divide(mealTypeItem.fee) : '', // 每人最大折扣金额
                      discountMaxOnceFeeType: '', // 每人最大折扣金额上限
                      maxLimitType: mealTypeItem.amount ? '1' : '0', // 限额
                      maxLimitFee: mealTypeItem.amount ? divide(mealTypeItem.amount) : '' // 限额最高
                    }
                    if (mealTypeItem.consume_limit && mealTypeItem.consume_limit === 'o_all') {
                      discountListItem.discountMaxFeeType = 'o_once'
                    } else {
                      discountListItem.discountMaxFeeType = mealTypeItem.consume_limit ? mealTypeItem.consume_limit : 'normal'
                      if (discountListItem.discountMaxFeeType === 't_once') {
                        discountListItem.discountMaxFeeType = 't_all'
                      }
                    }
                    if (discountListItem.discountMaxFeeType !== 'normal') {
                      discountListItem.discountMaxOnceFeeType = mealTypeItem.consume_limit
                    }
                    deduction.canteen_take_meal_type.discountList.push(discountListItem)
                  } else { // 外卖
                    deduction.isTakeOut = true
                    deduction.takeOut_take_meal_type.take_out_type.push(type)
                    let discountList = mealTypeItem.rule.map((v, i) => {
                      return { index: i + 1, value: v > -1 ? v : '', name: `第${i + 1}次`, unit: '%' }
                    })
                    discountList.push({ index: -1, value: mealTypeItem.after > -1 ? mealTypeItem.after : '', name: '之后每次消费', unit: '%' })
                    let discountListItem = {
                      meal_type: type,
                      discount: discountList,
                      discountMaxFeeType: '', // 每人最大折扣类型
                      discountMaxFee: mealTypeItem.consume_limit !== 'normal' ? divide(mealTypeItem.fee) : '', // 每人最大折扣金额
                      discountMaxOnceFeeType: '', // 每人最大折扣金额上限
                      maxLimitType: mealTypeItem.amount ? '1' : '0', // 限额
                      maxLimitFee: mealTypeItem.amount ? divide(mealTypeItem.amount) : '' // 限额最高
                    }
                    if (mealTypeItem.consume_limit && mealTypeItem.consume_limit === 'o_all') {
                      discountListItem.discountMaxFeeType = 'o_once'
                    } else {
                      discountListItem.discountMaxFeeType = mealTypeItem.consume_limit ? mealTypeItem.consume_limit : 'normal'
                      if (discountListItem.discountMaxFeeType === 't_once') {
                        discountListItem.discountMaxFeeType = 't_all'
                      }
                    }
                    if (discountListItem.discountMaxFeeType !== 'normal') {
                      discountListItem.discountMaxOnceFeeType = mealTypeItem.consume_limit
                    }
                    deduction.takeOut_take_meal_type.discountList.push(discountListItem)
                  }
                })
              }
            }
            other = {
              consumtionType: 'normal', // 消费限制 normal无限制 once单次消费限制 day每日消费限制
              countUpperFee: '', // 单次最高消费
              dayUpperFee: '', // 每日最高消费
              fixedList: [
                { index: 1, value: '', name: '第1次', unit: '元' },
                { index: -1, value: '', name: '之后每次消费', unit: '元' }
              ],
              fixedCountType: '0', // 次数限制
              fixedLimit: '' // 固定次数
            }
          }

          deduction = Object.assign(deduction, other)
          this.consumptionFormData.deductionList.push(deduction)
        }
      })

      // 初始化下
      if (data.consume_type === 0) {
        this.addDTRuleHandle()
      } else {
        this.addCountRuleHandle()
      }
    },
    // 日期现在选择
    changePackerDate(e) {
      if (e && e.length) {
        if (!this.isRepeatDate(e)) {
          this.consumptionFormData.exceptDateRange.push(e)
        } else {
          this.$message.error('时间重叠了！')
        }
        this.consumptionFormData.pickerDate = []
      }
    },
    changeDiscountMaxFeeTypeHandle(e, item, i) {
      console.log(e)
      if (e === 'o_once') {
        item.discountMaxOnceFeeType = 'o_once'
      }
      if (e === 't_all') {
        item.discountMaxOnceFeeType = 't_once'
      }
    },
    changeMealDiscountMaxFeeTypeHandle(e, item, i) {
      console.log(e)
      if (e === 'o_once') {
        item.discountMaxOnceFeeType = 'o_once'
      }
      if (e === 't_all') {
        item.discountMaxOnceFeeType = 't_once'
      }
    },
    // 检查时间是否重叠
    isRepeatDate(arr) {
      let repeat = false
      const start = new Date(arr[0]).getTime()
      const end = new Date(arr[1]).getTime()
      for (let index = 0; index < this.consumptionFormData.exceptDateRange.length; index++) {
        const item = this.consumptionFormData.exceptDateRange[index];
        const itemStart = new Date(item[0]).getTime()
        const itemEnd = new Date(item[1]).getTime()
        if (checkArrayOverlap([start, end], [itemStart, itemEnd])) {
          repeat = true
          return repeat
        }
      }
      return repeat
    },
    // 日期的删除
    closeExceptHandle(index) {
      this.consumptionFormData.exceptDateRange.splice(index, 1)
    },
    // 计次设置餐段disabled
    isDisabledJCOtherMeal(current) {
      this.consumptionFormData.allSelectMeal = []
      this.consumptionFormData.countList.map((item, k) => {
        this.consumptionFormData.allSelectMeal = this.consumptionFormData.allSelectMeal.concat(item.meal)
      })
      this.consumptionFormData.countList.forEach((v, k) => {
        v.mealList.forEach(meal => {
          if (this.consumptionFormData.allSelectMeal.includes(meal.value) && !v.meal.includes(meal.value)) {
            meal.disabled = true;
          } else {
            meal.disabled = false;
            if (this.consumptionFormData.allSelectMeal.includes('all') && !v.meal.includes(meal.value)) {
              meal.disabled = true;
            } else {
              if (this.consumptionFormData.allSelectMeal[0] !== 'all' && meal.value === 'all' && this.consumptionFormData.countList.length > 1) {
                meal.disabled = true;
              }
            }
          }
        })
      })
    },
    // 修改计次餐段
    changeJCMealHandle(data, i) {
      this.isDisabledJCOtherMeal()
    },
    // 添加单次的餐段
    addCountRuleHandle() {
      this.consumptionFormData.countList.push(
        {
          meal: [],
          mealList: deepClone(this.mealList),
          countType: '0', // 可记账次数
          countNum: '', // 上限次数
          reach: 'disable' // 次数达到上限 disable original
        }
      )
      if (this.consumptionFormData.countList.length > 1) {
        this.isDisabledJCOtherMeal()
      }
    },
    // 餐补添加（公用）
    addMealSubsidy(index, list, type) {
      list.splice(-1, 0, { index: list.length, value: '', name: '第' + list.length + '次', unit: type === 'discount' ? '%' : '元' })
    },
    // 餐补的删除（公用）
    deleteMealSubsidy(index, list) {
      list.splice(index, 1)
    },
    // 扣费设置餐段disabled
    isDisabledDTOtherMeal(current) {
      this.consumptionFormData.deductionAllSelectMeal = []
      this.consumptionFormData.deductionList.map((item, k) => {
        this.consumptionFormData.deductionAllSelectMeal = this.consumptionFormData.deductionAllSelectMeal.concat(item.meal)
      })
      this.consumptionFormData.deductionList.forEach((v, k) => {
        v.mealList.forEach(meal => {
          if (this.consumptionFormData.deductionAllSelectMeal.includes(meal.value) && !v.meal.includes(meal.value)) {
            meal.disabled = true;
          } else {
            meal.disabled = false;
            if (this.consumptionFormData.deductionAllSelectMeal.includes('all') && !v.meal.includes(meal.value)) {
              meal.disabled = true;
            } else {
              if (this.consumptionFormData.deductionAllSelectMeal[0] !== 'all' && meal.value === 'all' && this.consumptionFormData.deductionList.length > 1) {
                meal.disabled = true;
              }
            }
          }
        })
      })
    },
    // 添加单次的餐段
    addDTRuleHandle() {
      this.consumptionFormData.deductionList.push(
        {
          meal: [], // 餐段
          mealList: deepClone(MEAL_LIST),
          deductionType: 'WZK', // 扣费类型  JC - 记次, WZK - 扣费-无折扣, GD - 扣费-固定, ZK - 扣费-折扣
          useMealSubsidy: false, // 使用餐补
          mealSubsidyType: 'total', // 餐补类型
          mealSubsidyFee: '', // 累计金额
          subsidyList: [
            { index: 1, value: '', name: '第1次', unit: '元' },
            { index: -1, value: '', name: '之后每次消费', unit: '元' }
          ], // 单餐餐补list
          consumtionType: 'normal', // 消费限制 normal无限制 once单次消费限制 day每日消费限制
          countUpperFee: '', // 单次最高消费
          dayUpperFee: '', // 每日最高消费
          fixedList: [
            { index: 1, value: '', name: '第1次', unit: '元' },
            { index: -1, value: '', name: '之后每次消费', unit: '元' }
          ],
          fixedCountType: '0', // 次数限制
          fixedLimit: '', // 固定次数
          fixedMaxCountType: 'disable', // 次数达到上限时
          maxLimitType: '0', // 限额
          maxLimitFee: '', // 限额最高
          discountFeeType: 'normal', // 订单金额
          discountMealConsumption: 'lte',
          discountRang: '',
          discountFee: '', // 1
          discountMaxFeeType: 'normal',
          discountMaxOnceFeeType: 'o_once',
          discountMaxFee: '',
          discountList: [
            { index: 1, value: '', name: '第1次', unit: '%' },
            { index: -1, value: '', name: '之后每次消费', unit: '%' }
          ],
          debit_type: 'normal', // 扣费方式 normal 普通折扣， take_meal_type 取餐方式折扣
          isCanteen: false,
          isTakeOut: false,
          canteen_take_meal_type: {
            take_out_type: [],
            discountList: []
          },
          takeOut_take_meal_type: {
            take_out_type: [],
            discountList: []
          },
          takeMealFirst: false
        }
      )
      if (this.consumptionFormData.deductionList.length > 1) {
        this.isDisabledDTOtherMeal()
      }
    },
    // 修改扣费餐段
    changeDTMealHandle(data, i) {
      this.isDisabledDTOtherMeal()
    },
    saveConsumHandle() {
      this.$refs.consumptionFormRef.validate(valid => {
        if (!this.consumptionFormData.walletType.length && !this.consumptionFormData.payway.length) {
          this.paymentLimitText = "请选选择支付限制！"
          return
        }
        this.paymentLimitText = ""
        if (valid) {
          if (this.isLoading) return;
          let params = this.formatData()
          if (this.type === 'add') {
            this.addConsumeList(params)
          } else {
            this.modifyConsumeList(params)
          }
        } else {
          console.log(valid)
        }
      })
    },
    // 格式化需要的数据
    formatData() {
      let params = {
        name: this.consumptionFormData.name,
        org_nos: this.consumptionFormData.organization,
        group_nos: this.consumptionFormData.groupNos,
        wallet_type: this.consumptionFormData.walletType,
        payway: this.consumptionFormData.payway,
        consume_type: this.consumptionFormData.consumeType,
        rules: []
      }
      if (this.consumptionFormData.exceptWeekday.length > 0) {
        params.except_weekday = this.consumptionFormData.exceptWeekday
      }
      if (this.consumptionFormData.exceptDateRange.length > 0) {
        params.except_date_range = this.consumptionFormData.exceptDateRange.map(v => {
          return {
            start_date: v[0],
            end_date: v[1]
          }
        })
      }
      if (this.consumptionFormData.tip) {
        params.tip = this.consumptionFormData.tip
      }
      if (this.consumptionFormData.consumeType === 0) { // 计次
        this.consumptionFormData.countList.forEach(item => {
          if (item.meal.length > 0) { // 选中了餐段的数据才提交
            params.rules.push({
              meal: item.meal,
              rule_type: 'JC',
              rule: {
                count: item.countType === '0' ? null : parseInt(item.countNum), // 记账次数，无上限传null，有上限传值
                reach: item.reach,
                rule: {}
              }
            })
          }
        })
        if (!params.rules.length) {
          this.$message.error('餐段不能为空！')
        }
      } else {
        // 扣费
        this.consumptionFormData.deductionList.forEach((deduction, index) => {
          let ruleObj = {
            meal: deduction.meal,
            rule_type: deduction.deductionType
          }
          let rule = {
            rule: []
          }
          if (deduction.useMealSubsidy) { // 使用餐补
            rule.subsidy = deduction.mealSubsidyType
            if (deduction.mealSubsidyType === 'total') { // 累计餐补
              rule.subsidy_total = times(deduction.mealSubsidyFee)
              if (rule.subsidy_total <= 0) { // 累计餐补金额必选大于0
                rule.subsidy = 'close'
              }
            } else { // 单次餐补
              let subsidyLen = deduction.subsidyList.length
              rule.subsidy_rule = []
              deduction.subsidyList.forEach((subsidy, i) => {
                if (subsidyLen > 1 && i < (subsidyLen - 1)) { // 数量大于1才有
                  rule.subsidy_rule.push(subsidy.value ? times(subsidy.value) : 0)
                } else {
                  rule.subsidy_after = subsidy.value ? times(subsidy.value) : 0
                }
              })
            }
          } else {
            rule.subsidy = 'close'
          }
          let fixexLen = deduction.fixedList.length
          let discountLen = deduction.discountList.length
          switch (deduction.deductionType) {
            case 'WZK': // 无折扣
              rule.consume_limit = deduction.consumtionType
              rule.amount = null
              if (deduction.consumtionType === 'once') {
                rule.amount = times(deduction.countUpperFee)
              }
              if (deduction.consumtionType === 'day') {
                rule.amount = times(deduction.dayUpperFee)
              }
              break;
            case 'GD': // GD 固定
              rule.reach = deduction.fixedMaxCountType
              // 次数达到上限时
              if (deduction.fixedMaxCountType === 'original') {
                if (deduction.maxLimitType === '1') { // 限额最高
                  rule.amount = times(deduction.maxLimitFee)
                } else {
                  rule.amount = null
                }
              }
              // 次数限制
              if (deduction.fixedCountType !== '0') {
                rule.count = parseInt(deduction.fixedLimit)
              } else {
                rule.count = null
              }
              deduction.fixedList.forEach((item, i) => {
                if (fixexLen > 1 && i < (fixexLen - 1)) { // 数量大于1才有
                  rule.rule.push(item.value ? times(item.value) : -1)
                } else {
                  rule.after = item.value ? times(item.value) : -1
                }
              })
              break;
            case 'ZK': // ZK 折扣
              // 消费金额
              rule.order_limit = deduction.discountFeeType
              if (rule.order_limit !== 'normal') {
                rule.option = deduction.discountMealConsumption
                rule.money = times(deduction.discountFee)
                if (deduction.discountMealConsumption === 'gt') {
                  rule.discount_rang = deduction.discountRang
                }
              }
              // 限额
              if (deduction.discountMaxFeeType === 'normal') {
                if (deduction.maxLimitType === '1') { // 限额最高
                  rule.amount = times(deduction.maxLimitFee)
                } else {
                  rule.amount = null
                }
              }
              // 每人最大折扣金额
              rule.consume_limit = deduction.discountMaxFeeType
              if (deduction.discountMaxFeeType !== 'normal') {
                // if (deduction.discountMaxFeeType === 'o_once') {
                rule.consume_limit = deduction.discountMaxOnceFeeType
                // }
                rule.fee = times(deduction.discountMaxFee)
              }
              ruleObj.debit_type = deduction.debit_type
              if (deduction.debit_type === 'normal') { // 普通折扣
                deduction.discountList.forEach((item, i) => {
                  if (discountLen > 1 && i < (discountLen - 1)) { // 数量大于1才有
                    rule.rule.push(item.value ? parseInt(item.value) : 100)
                  } else {
                    rule.after = item.value ? parseInt(item.value) : 100
                  }
                })
              } else { // 取餐方式
                // delete rule.rule;
                if ((deduction.isCanteen && deduction.canteen_take_meal_type.discountList.length) || (deduction.isTakeOut && deduction.takeOut_take_meal_type.discountList)) {
                  rule.take_meal_first = deduction.takeMealFirst // 仅首单优惠
                }
                if (!rule.take_meal_type_rule) rule.take_meal_type_rule = {}
                if (deduction.isCanteen) { // 堂食取餐
                  deduction.canteen_take_meal_type.discountList.forEach((v, j) => {
                    rule.take_meal_type_rule[v.meal_type] = { rule: [], after: '', consume_limit: '', fee: '', amount: '' }
                    let mealDiscountLen = v.discount.length
                    v.discount.forEach((item, i) => {
                      if (mealDiscountLen > 1 && i < (mealDiscountLen - 1)) { // 数量大于1才有
                        rule.take_meal_type_rule[v.meal_type].rule.push(item.value ? parseInt(item.value) : 100)
                      } else {
                        rule.take_meal_type_rule[v.meal_type].after = item.value ? parseInt(item.value) : 100
                      }
                    })
                    // 每人最大折扣金额
                    rule.take_meal_type_rule[v.meal_type].consume_limit = v.discountMaxFeeType
                    if (v.discountMaxFeeType !== 'normal') {
                      // if (v.discountMaxFeeType === 'o_once') {
                      rule.take_meal_type_rule[v.meal_type].consume_limit = v.discountMaxOnceFeeType
                      // }
                      rule.take_meal_type_rule[v.meal_type].fee = times(v.discountMaxFee)
                    }
                    // 限额
                    if (v.discountMaxFeeType === 'normal') {
                      if (v.maxLimitType === '1') { // 限额最高
                        rule.take_meal_type_rule[v.meal_type].amount = times(v.maxLimitFee)
                      } else {
                        rule.take_meal_type_rule[v.meal_type].amount = null
                      }
                    }
                  })
                }
                if (deduction.isTakeOut) { // 外卖
                  deduction.takeOut_take_meal_type.discountList.forEach((v, j) => {
                    rule.take_meal_type_rule[v.meal_type] = { rule: [], after: '', consume_limit: '', fee: '', amount: '' }
                    let mealDiscountLen = v.discount.length
                    v.discount.forEach((item, i) => {
                      if (mealDiscountLen > 1 && i < (mealDiscountLen - 1)) { // 数量大于1才有
                        rule.take_meal_type_rule[v.meal_type].rule.push(item.value ? parseInt(item.value) : 100)
                      } else {
                        rule.take_meal_type_rule[v.meal_type].after = item.value ? parseInt(item.value) : 100
                      }
                    })
                    // 每人最大折扣金额
                    rule.take_meal_type_rule[v.meal_type].consume_limit = v.discountMaxFeeType
                    if (v.discountMaxFeeType !== 'normal') {
                      // if (v.discountMaxFeeType === 'o_once') {
                      rule.take_meal_type_rule[v.meal_type].consume_limit = v.discountMaxOnceFeeType
                      // }
                      rule.take_meal_type_rule[v.meal_type].fee = times(v.discountMaxFee)
                    }
                    // 限额
                    if (v.discountMaxFeeType === 'normal') {
                      if (v.maxLimitType === '1') { // 限额最高
                        rule.take_meal_type_rule[v.meal_type].amount = times(v.maxLimitFee)
                      } else {
                        rule.take_meal_type_rule[v.meal_type].amount = null
                      }
                    }
                  })
                }
              }
              break;
          }
          console.log('rule', rule)
          ruleObj.rule = rule
          params.rules.push(ruleObj)
          console.log('params', params)
        })
      }
      return params
    },
    // 添加消费规则
    async addConsumeList(params) {
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundMarketingConsumeAddPost(params));
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.$closeCurrentTab(this.$route.path)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 修改消费规则
    async modifyConsumeList(params) {
      params.rule_no = this.consumptionFormData.ruleNo
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundMarketingConsumeModifyPost(params));
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.$closeCurrentTab(this.$route.path)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 删除
    deleteHandle(list, i) {
      list.splice(i, 1)
      this.isDisabledJCOtherMeal()
      this.isDisabledDTOtherMeal()
    },
    //
    async getPaywayList() {
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundPaymentPayInfoGetCompanyPayinfoPost({
        company_id: this.$store.getters.userInfo.company_id
      }));
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.paywayList = res.data
      } else {
        this.$message.error(res.msg)
      }
    },
    changePaymentLimit(e) {
      if (e.length) {
        this.paymentLimitText = ''
      } else {
        if (!this.consumptionFormData.walletType.length && !this.consumptionFormData.payway.length) {
          this.paymentLimitText = "请选选择支付限制！"
        }
      }
    },
    changeTakeMealType(e, data) {
      console.log(111, e)
      const mealKeys = data.discountList.map(v => {
        return v.meal_type
      })
      // 添加
      if (e.length) {
        e.forEach(meal => {
          if (!mealKeys.includes(meal)) {
            data.discountList.push({
              meal_type: meal,
              discount: [
                { index: 1, value: '', name: '第1次', unit: '%' },
                { index: -1, value: '', name: '之后每次消费', unit: '%' }
              ],
              discountMaxFeeType: 'normal', // 每人最大折扣类型
              discountMaxFee: '', // 每人最大折扣金额
              discountMaxOnceFeeType: 'o_once', // 每人最大折扣金额上限
              maxLimitType: '0', // 限额
              maxLimitFee: '' // 限额最高
            })
          }
        })
      }
      // 删除
      mealKeys.forEach((item, k) => {
        if (!e.includes(item)) {
          data.discountList.splice(k, 1)
        }
      })
    }
  }
}
</script>
<style lang="scss">
.add-conrules-wrapper{
  .consumption-form-wrapper{
    max-width: 1106px;
  }
  .item-box-wrapper{
    position: relative;
    .delete-box{
      position: absolute;
      top: 50%;
      right: 50px;
      transform: translateY(-50%);
      cursor: pointer;
      z-index: 99;
    }
  }
  margin-top: 20px;
  padding: 20px;
  .w-300{
    width: 300px;
  }
  .max-input{
    width: 80px;
  }
  .name-b {
    .el-form-item__content{
      margin-left: 65px;
    }
  }
  .m-l-100{
    margin-left: 100px;
  }
  .m-l-110{
    margin-left: 110px;
  }
  .m-l-70{
    margin-left: 70px;
  }
  .m-b-0{
    margin-bottom: 0;
  }
  .form-item--inline{
    display: inline-block;
  }
  .margin-button{
    margin-bottom: 15px;
  }
  .min-label-w{
    .el-form-item__label{
      min-width: 180px;
    }
    .el-form-item__content{
      margin-left: 180px;
    }
  }
  .min-label-140{
    .el-form-item__label{
      min-width: 140px;
    }
    .el-form-item__content{
      margin-left: 140px;
    }
  }
  .no-label{
    .el-form-item__content{
      margin-left: 0;
    }
  }
  .form-ul{
    display: inline-block;
    border: 1px solid #DCDFE6;
    vertical-align: top;
    .form-li-box{
      position: relative;
      &:not(:last-child) {
        border-bottom: 1px solid #DCDFE6;
      }
      .add-icon{
        position: absolute;
        right: -30px;
        top: 10px;
      }
      .remove-icon{
        position: absolute;
        right: -60px;
        top: 10px;
      }
    }
    .form-li{
      width: 120px;
      text-align: center;
    }
    .li-center{
      border-left: 1px solid #DCDFE6;
      border-right: 1px solid #DCDFE6;
    }
    .el-form-item{
      margin-bottom: 0;
      .el-input__inner{
        border: none;
      }
      &.is-error {
        .el-input__inner{
          border: 1px solid #F56C6C;
        }
      }
    }
  }
  .li-icon{
      font-size: 20px;
      cursor: pointer;
      color: #fda04d;
      vertical-align: middle;
    }
  .time-button{
    position: relative;
    height: 36px;
    .fixed-btn-except{
      position: absolute;
      top: 0;
      left: 0;
      cursor: pointer;
      z-index: 1;
    }
  }
  .footer{
    margin-top: 30px;
    .el-button{
      min-width: 180px;
    }
  }
  .form-item{
    .form-label{
      display: inline-block;
      width: 180px;
      line-height: 32px;
      text-align: right;
    }
    .form-content{
      display: inline-block;
      line-height: 32px;
      vertical-align: top;
    }
  }
}
</style>
