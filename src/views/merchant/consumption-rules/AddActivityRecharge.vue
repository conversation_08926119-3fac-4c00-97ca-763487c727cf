<template>
  <div class="container-wrapper circular-bead add_active_recharge">
    <el-form v-loading="isLoading" ref="activityForm" size="small" :model="activityForm" :rules="rules" label-width="120px" class="activity-form ps-small-box">
      <!-- <el-form-item label="活动编号" v-if="type == 'modify'">
        <el-input
          v-model="activityForm.id"
          class="common-input ps-input"
          :disabled="type == 'modify'"
          placeholder="请输入活动活动编号"
        ></el-input>
      </el-form-item> -->
      <el-form-item label="活动名称" prop="name">
        <el-input
          v-model="activityForm.name"
          class="common-input ps-input"
          placeholder="请输入活动名称"
          :disabled="isDisabled"
        ></el-input>
      </el-form-item>
      <el-form-item label="适用分组" prop="groups">
        <user-group-select :multiple="true" :disabled="isDisabled" collapseTags class="search-item-w ps-input" v-model="activityForm.groups" placeholder="请选择分组"></user-group-select>
      </el-form-item>
      <el-form-item label="活动时间" class="is-required">
        <div>
          <el-radio-group v-model="activityForm.dateRange" :disabled="isDisabled" class="ps-radio">
            <el-radio :label="0">永久</el-radio>
            <el-radio :label="1">时间段</el-radio>
            <!-- <el-radio label="fixedTime">固定周期</el-radio> -->
          </el-radio-group>
        </div>
        <div class="time-item" v-if="activityForm.dateRange==0">
          <el-form-item label="" prop="startTime" :key="0">
            <span>开始时间：</span>
            <el-date-picker
              v-model="activityForm.startTime"
              type="datetime"
              class=""
              :disabled="isDisabled"
              format="yyyy-MM-dd HH:mm:ss"
              value-format="yyyy-MM-dd HH:mm:ss"
              :picker-options="pickerOptions"
              placeholder="开始日期">
            </el-date-picker>
          </el-form-item>
        </div>
        <div class="time-item" v-if="activityForm.dateRange==1">
          <el-form-item label="" prop="selectTime" :key="1">
            <el-date-picker
              :disabled="isDisabled"
              v-model="activityForm.selectTime"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              class="common-input"
              align="left"
              format="yyyy-MM-dd HH:mm:ss"
              value-format="yyyy-MM-dd HH:mm:ss"
              :picker-options="pickerOptions"
              :default-time="defaultTime"
            ></el-date-picker>
          </el-form-item>
        </div>
      </el-form-item>
      <el-form-item label="活动规则" class="is-required">
        <el-form-item label="">
          <el-radio-group v-model="activityForm.couponType" @change="changeCouponHandle" :disabled="isDisabled" prop="couponType" class="ps-radio">
            <el-radio label="RG">充值赠送</el-radio>
            <!-- <el-radio label="RS">充值立减</el-radio> -->
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="showRandom" label="">
          <!-- <el-radio-group v-model="activityForm.subtractType" @change="changeSubtractType" size="mini" prop="couponType">
            <el-radio-button :label="0">固定</el-radio-button>
            <el-radio-button :label="1">随机</el-radio-button>
          </el-radio-group> -->
        </el-form-item>
        <div class="coupon-type-box clearfix">
          <div v-if="activityForm.subtractType==0" class="coupon-type-item">
            <!-- <span class="label-l max-w">满减规则</span> -->
            <transition-group name="el-zoom-in-top" tag="div">
              <div class="grad" v-for="(item, i) in activityForm.gradList" :key="'k'+i">
                <span class="label-l max-w">单笔充值满</span>
                <el-form-item class="inline-item" :rules="rules.maxRange" label="" :prop="'gradList.'+i+'.full'">
                  <el-input v-model="item.full" :disabled="isDisabled" class="coupon-type-input ps-input"></el-input>
                </el-form-item>
                <span class="label-l label-r">{{activityForm.couponType=='RG'?'送':'减'}}</span>
                <el-form-item class="inline-item" label="" :rules="rules.minRange" :prop="'gradList.'+i+'.reduce'">
                  <el-input v-model="item.reduce" :disabled="isDisabled" class="coupon-type-input ps-input"></el-input>
                </el-form-item>
                <span class="label-r">元</span>
                <!-- <i class="el-icon-delete grad-danger"></i> -->
                <el-button v-if="activityForm.gradList.length>1" :disabled="isDisabled" type="text" @click="delGradHandle(i)" class="grad-danger"><i class="el-icon-delete grad-danger"></i></el-button>
              </div>
            </transition-group>
            <div class="" style="margin-top: 5px;padding-left: 75px;">
              <el-button :disabled="isDisabled" @click="addGradHandle" type="text">添加》</el-button>
            </div>
            <!-- <div class="limit-box">
              <span class="label-l max-w">上限</span>
              <el-form-item class="inline-item" label="" style="width:280px;"  prop="amountlimit">
                <el-input v-model="activityForm.amountlimit" class=""></el-input>
              </el-form-item>
              <span class="label-r">元</span>
            </div> -->
          </div>
          <div v-if="activityForm.subtractType==1" class="coupon-type-item">
            <span class="label-l max-r-w">随机{{activityForm.couponType=='RG'?'赠送':'立减'}}范围</span>
            <el-form-item class="inline-item" label="" prop="minRange">
              <el-input v-model="activityForm.minRange" :disabled="isDisabled" class="coupon-type-input ps-input"></el-input>
            </el-form-item>
            <span class="label-l label-r">至</span>
            <el-form-item class="inline-item" label="" prop="maxRange">
              <el-input v-model="activityForm.maxRange" :disabled="isDisabled" class="coupon-type-input ps-input"></el-input>
            </el-form-item>
            <span class="label-r">元</span>
            <div class="limit-box">
              <span class="label-l max-r-w">生效额度</span>
              <el-form-item class="inline-item" label="" style="width:280px;" prop="checkPrice">
                <el-input v-model="activityForm.checkPrice" :disabled="isDisabled" class="ps-input"></el-input>
              </el-form-item>
              <span class="label-r">元</span>
            </div>
            <!-- <div class="limit-box">
              <span class="label-l max-r-w">上限</span>
              <el-form-item class="inline-item" label="" style="width:280px;" prop="amountlimit">
                <el-input v-model="activityForm.amountlimit" class=""></el-input>
              </el-form-item>
              <span class="label-r">元</span>
            </div> -->
          </div>
          <div class="coupon-type-box-tips">
            <div v-if="activityForm.couponType=='RG'">
              <p class="tips-title">提示：</p>
              <p>1、后一条规则需要比前一个规则金额大，如第一个规则为“满10增送200”，则第二个规则需满（>10）赠送（>200）。</p>
            </div>
            <div v-if="activityForm.couponType=='RS'">
              <p class="tips-title">提示：</p>
              <p>1、后一条规则需比前一条规则金额大，如第一个规则为“满20减10元”，则下一条规则需满（>20）减（>10）。</p>
              <p>2、立减金额需小于支付金额，如满10减9；不允许满10减11。</p>
            </div>
          </div>
        </div>
      </el-form-item>
      <el-form-item  label="充值场景" prop="payScene" class="is-required">
        <el-select v-model="activityForm.payScene" :disabled="isDisabled" @change="choosePaySceneChange">
          <el-option v-for="item in paySceneList" :key="item.value" :label="item.name" :value="item.value" ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item  label="充值渠道限制" prop="payChannel" >
        <select-tree
              v-model="activityForm.payChannel"
              class="search-item-w ps-input w-215"
              placeholder="请选择充值渠道"
              :multiple="true"
              :checkStrictly="false"
              :collapseTags="true"
              :append-to-body="true"
              :treeData="channelList"
              :treeProps="channelTreeProps"
              :isLazy="false"
              :clearable="true"
              ref="payChannel"
              :disabled="isDisabled"
            >
          </select-tree>
      </el-form-item>
      <el-form-item label="活动周期" prop="cycleType">
        <el-radio-group v-model="activityForm.cycleType" :disabled="isDisabled" class="ps-radio">
          <el-radio v-for="cycleType in cycleTypeList" :key="cycleType.value" :label="cycleType.value">{{cycleType.name}}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="" prop="cycleLimitNum" class="is-required" :rules="activityForm.cycleLimitType===1?rules.cycleLimitNum:[]">
        <div class="before-required">周期内单人可参与活动次数 <span class="pos-tip">提示:<br>1.新的活动周期会重置用户参与活动的次数</span></div>
        <div><el-radio class="ps-radio" :disabled="isDisabled" v-model="activityForm.cycleLimitType" :label="-1">无上限</el-radio></div>
        <div><el-radio class="ps-radio" :disabled="isDisabled" v-model="activityForm.cycleLimitType" :label="1">至多可参与</el-radio>
        <el-input v-model="activityForm.cycleLimitNum" :disabled="activityForm.cycleLimitType!==1||modifyData.status=='enable'" class="ps-input" style="width: 100px;"></el-input><span class="label-r">次</span></div>
      </el-form-item>
      <el-form-item label="活动上限" prop="amountlimit" class="is-required">
        <div><el-radio class="ps-radio" :disabled="type=='modify'&&modifyData.status=='enable'" v-model="activityForm.isAmountlimit" :label="0">无上限</el-radio></div>
        <div><el-radio class="ps-radio" :disabled="type=='modify'&&modifyData.status=='enable'" v-model="activityForm.isAmountlimit" :label="1">合计{{activityForm.couponType=='RG'?'赠送':'立减'}}金额超过</el-radio>
        <el-input v-model="activityForm.amountlimit" :disabled="activityForm.isAmountlimit==0||modifyData.status=='enable'" class="ps-input" style="width: 180px;"></el-input><span class="label-r">元停止活动</span></div>
      </el-form-item>
      <el-form-item label="规则说明">
        <el-input type="textarea" rows="5" v-model="activityForm.couponTip" :disabled="type=='modify'&&modifyData.status=='enable'" class="common-input ps-input"></el-input>
      </el-form-item>
      <el-form-item label="活动状态" prop="status">
        <el-radio-group v-model="activityForm.status" class="ps-radio">
          <el-radio :disabled="type=='modify'&&modifyData.status=='enable'" label="enable">启用</el-radio>
          <el-radio :disabled="type=='modify'&&modifyData.status=='enable'" label="disable">停用</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          class="activity-input ps-origin-btn"
          :disabled="type=='modify'&&modifyData.status=='enable'"
          @click="submitActivityHandle"
        >{{type=='add'?'创建活动':'修改活动'}}</el-button>
        <el-button class="activity-input" @click="$router.go(-1)">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { mapActions } from "vuex";
import { to, divide, times, deepClone } from "@/utils"
import UserGroupSelect from '@/components/UserGroupSelect'
import SelectTree from '@/components/SelectTree'

export default {
  components: {
    UserGroupSelect,
    SelectTree
  },
  data() {
    let validSelectTime = (rule, value, callback) => {
      console.log(value)
      if (this.activityForm.dateRange === 1) {
        if (!value || !value.length) {
          callback(new Error("请选择活动时间！"));
        } else {
          let start = value[0].replace(new RegExp(/-/gm), '/')
          let end = value[1].replace(new RegExp(/-/gm), '/')
          if (new Date(start).getTime() >= new Date(end).getTime()) {
            callback(new Error("活动开始时间要小于结束时间！"));
          } else {
            callback();
          }
        }
      } else {
        callback();
      }
    };
    let validMoney = function(rule, value, callback) {
      let reg = /^([0-9]+[\d]*(.[0-9]{1,2})?)$/
      if (!reg.test(value)) {
        callback(new Error("金额格式错误！"));
      } else {
        callback();
      }
    };
    let validRangeMoney = (rule, value, callback) => {
      let reg = /^([0-9]+[\d]*(.[0-9]{1,2})?)$/
      // 满减判断，金额不能小于等于优化金额，并且不能重复
      let splitRule = rule.field.split('.')
      if (splitRule[2] === 'full') {
        if (value === '0') {
          return callback(new Error("活动金额不能为0！"));
        }
      }
      if (!reg.test(value)) {
        callback(new Error("金额格式错误！"));
      } else {
        let full = Number(this.activityForm.gradList[Number(splitRule[1])].full)
        let reduce = Number(this.activityForm.gradList[Number(splitRule[1])].reduce)
        let prevValues = { // 上一条的规则
          full: '',
          reduce: ''
        }
        let nextValues = { // 下一条的规则
          full: '',
          reduce: ''
        }
        if (splitRule[1] > 0) {
          prevValues.full = this.activityForm.gradList[Number(splitRule[1]) - 1].full
          prevValues.reduce = this.activityForm.gradList[Number(splitRule[1]) - 1].reduce
        }
        if ((Number(splitRule[1])) < (this.activityForm.gradList.length - 1)) {
          nextValues.full = this.activityForm.gradList[Number(splitRule[1]) + 1].full
          nextValues.reduce = this.activityForm.gradList[Number(splitRule[1]) + 1].reduce
        }
        if (this.activityForm.couponType === 'RS') { // 立减
          // 左右是否满足
          if (full && reduce) {
            if (reduce >= full) {
              if (splitRule[2] === 'full') {
                return callback(new Error("金额要大于优惠金额！"));
              } else {
                return callback(new Error("金额要小于活动金额！"));
              }
            } else {
              switch (splitRule[2]) {
                case 'full':
                  this.$refs.activityForm.clearValidate(`${splitRule[0]}.${splitRule[1]}.reduce`)
                  break;
                case 'reduce':
                  this.$refs.activityForm.clearValidate(`${splitRule[0]}.${splitRule[1]}.full`)
                  break;
              }
              callback()
            }
          } else {
            callback()
          }
        } else { // 满送
          let isMax = true // 当前规则金额是否比上一规则金额大
          let errorText = ''
          switch (splitRule[2]) {
            case 'full':
              if (reg.test(prevValues.full)) {
                if (Number(value) <= Number(prevValues.full)) {
                  isMax = false
                  errorText = '当前规则金额必须大于上一条规则金额'
                }
              }
              break;
            case 'reduce':
              if (reg.test(prevValues.reduce)) {
                if (Number(value) <= Number(prevValues.reduce)) {
                  isMax = false
                  errorText = '当前优惠金额必须大于上一条规则的优惠金额'
                }
              }
              break;
          }
          if (!isMax) {
            return callback(new Error(errorText));
          }
          callback();
        }
      }
    };
    let validGradMoney = function(rule, value, callback) {
      let reg = /^([0-9]+[\d]*(.[0-9]{1,2})?)$/
      if (!reg.test(value)) {
        callback(new Error("金额格式错误！"));
      } else {
        callback();
      }
    };
    let validAmountlimit = (rule, value, callback) => {
      let reg = /^([0-9]+[\d]*(.[0-9]{1,2})?)$/
      if (!reg.test(value) && this.activityForm.isAmountlimit === 1) {
        callback(new Error("金额格式错误！"));
      } else {
        callback();
      }
    };
    let validNumlimit = (rule, value, callback) => {
      let reg = /^\d+$/
      if (!reg.test(value)) {
        callback(new Error('请输入可参与活动次数'))
      } else {
        callback()
      }
    }
    return {
      type: 'add', // add or edit
      isLoading: false,
      activityForm: {
        id: '', // 编号
        name: '', // 活动名
        groups: [], // 分组
        couponType: 'RG', // 活动类型 'RG': '充值赠送','RS': '充值立减'
        subtractType: 0, // 固定or随机0/1
        couponTip: '', // 说明
        dateRange: 0, // 时间范围 0 永远 1 时间段
        selectTime: [],
        startTime: '', // 开始时间
        endTime: '', // 结束时间 永远就不用传
        checkPrice: '', // 生效金额
        fullMoney: '', // 满 优惠生效充值额度（单位分）
        reduceMoney: '', // 减/送
        maxRange: '', // 随机立减
        minRange: '', // 随机立减
        fee: '', // 0固定优惠'{"type":0,"num":5}' 1随机优惠'{"type":1,"num":[1,5]}'
        gradList: [
          {
            full: "",
            reduce: ""
          }
        ],
        isAmountlimit: 0, // 0无上限， 1有上限
        amountlimit: '', // 活动金额支出上限（单位分）
        status: 'enable', // 活动状态 'enable'启用,'disable'停用
        payScene: 'all', // 充值渠道
        cycleType: 0, // 活动周期* `0` : "与活动时间一致" * `1` : "自然周" * `2` : "自然月" * `3` : "每日"
        cycleLimitType: -1, // 周期内单人可参与活动次数,-1为不限制，1为限制
        cycleLimitNum: '', // 周期内单人可参与活动次数
        payChannel: []
      },
      disabledStatus: false, // 是否禁用,
      showRandom: true,
      rules: {
        name: [{ required: true, message: "请填写活动名称", trigger: "blur" }],
        groups: [{ required: true, message: "请选择适用分组", trigger: "blur" }],
        status: [{ required: true, message: "请设置活动状态", trigger: "change" }],
        startTime: [{ required: true, message: "请选择活动开始时间", trigger: "change" }],
        payScene: [{ required: true, message: "请选择充值场景", trigger: "change" }],
        selectTime: [{ validator: validSelectTime, trigger: "change" }],
        checkPrice: [{ validator: validMoney, trigger: "blur" }],
        reduceMoney: [{ validator: validMoney, trigger: "blur" }],
        amountlimit: [{ validator: validAmountlimit, trigger: "blur" }],
        maxRange: [{ validator: validRangeMoney, trigger: "blur" }],
        minRange: [{ validator: validRangeMoney, trigger: "blur" }],
        gradRange: [{ validator: validGradMoney, trigger: "blur" }],
        cycleType: [{ required: true, message: "请选择活动周期", trigger: "change" }],
        cycleLimitType: [{ required: true, message: "请选择", trigger: "change" }],
        cycleLimitNum: [{ validator: validNumlimit, trigger: "blur" }]
      },
      pickerOptions: {
        disabledDate(time) {
          let now = new Date()
          let before = new Date(`${now.getFullYear()}/${now.getMonth() + 1}/${now.getDate()}`)
          // console.log(parseTime('yyyy-MM-dd hh:mm:ss',before))
          return time.getTime() < before.getTime();
        }
      },
      defaultTime: ["00:00:00", "23:59:59"],
      modifyData: {},
      paySceneList: [
        { name: '全部', value: 'all' },
        { name: '线上充值', value: 'charge' },
        { name: '线下充值', value: 'charge_offline' }
      ],
      cycleTypeList: [
        { name: '与活动时间一致', value: 0 },
        { name: '自然周', value: 1 },
        { name: '自然月', value: 2 },
        { name: '每日', value: 3 }
      ],
      channelList: [], // 充值渠道限制选项列表 tree
      channelTreeProps: {
        value: 'id',
        label: 'sub_payway_verbose',
        isLeaf: 'is_leaf',
        children: 'childrenList'
      }// 选项tree 的字段映射
    };
  },
  computed: {
    isDisabled() {
      let disable = true
      if (this.type === 'add') {
        disable = false
      }
      // if (this.activityForm.status === 'disable' && this.type === 'modify') {
      //   disable = false
      // }
      if (this.activityForm.status === 'expire') {
        disable = true
      }
      return disable
    }

  },
  created () {
    if (this.$route.params.type) {
      this.type = this.$route.params.type
      if (this.type === 'modify') {
        this.activityForm.id = this.$route.query.id
        this.getActivityRechargeDetail()
      } else {
        // 获取充值渠道限制
        this.getPayChannelList("all")
      }
    }
  },
  methods: {
    ...mapActions({
      _createActivityRecharge: "createActivityRecharge",
      _modifyActivityRecharge: "modifyActivityRecharge"
    }),
    changeSubtractType(e) {
      // this.activityForm.fullMoney = ''
      // this.activityForm.reduceMoney = ''
      // this.activityForm.amountlimit = ''
      // this.activityForm.checkPrice = ''
      // this.activityForm.maxRange = ''
      // this.activityForm.minRange = ''
      // if(type=='mo'){}
    },
    async getActivityRechargeDetail(row) {
      this.isLoading = true
      let params = {
        coupon_no: this.activityForm.id
      }
      const [err, res] = await to(this.$apis.apiBackgroundMarketingRechargeDetailsPost(params));
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.initEditData(res.data)
        // 获取充值渠道限制
        this.getPayChannelList(res.data.pay_scene, res.data.payinfos)
      } else {
        this.$message.error(res.msg)
      }
    },
    initEditData(data) {
      // this.activityForm.couponType = 'RG'
      this.modifyData = data
      this.activityForm.id = data.coupon_no
      this.activityForm.name = data.name
      this.activityForm.groups = Object.values(data.user_groups_name)
      // this.activityForm.couponType = data.coupon_type
      if (data.start_time && data.end_time) {
        this.activityForm.dateRange = 1
        this.activityForm.selectTime = [data.start_time.replace('/T/ig', ' '), data.end_time.replace('/T/ig', ' ')]
      } else {
        this.activityForm.dateRange = 0
        this.activityForm.startTime = data.start_time.replace('/T/ig', ' ')
      }
      // this.activityForm.subtractType = data.coupon_type
      this.activityForm.couponTip = data.tip
      this.activityForm.isAmountlimit = data.limit_type
      this.activityForm.amountlimit = divide(data.upper_limit)
      this.activityForm.payScene = data.pay_scene
      this.activityForm.cycleType = data.cycle_type || 0
      if (data.cycle_limit_num !== null && data.cycle_limit_num !== undefined) {
        if (data.cycle_limit_num < 0) {
          this.activityForm.cycleLimitType = -1
        } else {
          this.activityForm.cycleLimitType = 1
          this.activityForm.cycleLimitNum = data.cycle_limit_num
        }
      }

      // this.activityForm.subtractType = data.fee_type
      // if (data.fee_type === 0) {
      // this.activityForm.gradList =
      let gradList = []
      if (data.discount && data.discount.length) {
        data.discount.map(item => {
          gradList.push({
            full: divide(item.limit_fee),
            reduce: divide(item.coupon_fee)
          })
        });
      }
      this.activityForm.gradList = gradList
      // } else {
      //   this.activityForm.maxRange = data.max_range
      //   this.activityForm.minRange = data.min_range
      // }
      this.activityForm.status = data.status === 'enable' ? 'enable' : 'disable'
      switch (data.status) {
        case 'enable':
          this.activityForm.status = 'enable'
          break;
        case 'disable':
          this.activityForm.status = 'disable'
          break;
        case 'expire':
          this.activityForm.status = 'disable'
          this.disabledStatus = true
          break;
        default:
          break;
      }
    },
    changeCouponHandle() {
      // if(this.activityForm.couponType=='RS') {
      //   this.activityForm.subtractType = 0
      //   this.showRandom = false
      // } else {
      //   this.showRandom = true
      // }
    },
    // 表单按钮点击
    submitActivityHandle() {
      this.$refs.activityForm.validate((valid) => {
        if (valid) {
          if (this.activityForm.subtractType === 1 && Number(this.activityForm.minRange) > Number(this.activityForm.maxRange)) {
            this.$message.error(`随机${this.activityForm.couponType === 'RG' ? '赠送' : '立减'}范围应该从小到大！`)
            return
          }
          if (this.type === 'add') {
            this.addActivityRechargeHandle()
          } else {
            this.modifyActivityRechargeHandle()
          }
        } else {
          console.warn('error submit')
        }
      })
    },
    checkActivityRule() {
      let isValid = true
      let errorText = ''
      this.activityForm.gradList.forEach((item, i) => {
        if (i > 0 && isValid) {
          if (Number(item.full) <= Number(this.activityForm.gradList[i - 1].full)) {
            errorText = '后一条活动的金额必须大于上一条活动的金额！'
            isValid = false
          }
          if (isValid && (Number(item.reduce) <= Number(this.activityForm.gradList[i - 1].reduce))) {
            errorText = '后一条活动规则的优惠金额必须大于上一条活动的优惠金额！'
            isValid = false
          }
        }
      })
      if (!isValid) {
        this.$message.error(errorText)
      }
      return isValid
    },
    // 添加
    async addActivityRechargeHandle() {
      if (!this.checkActivityRule()) {
        return
      }
      let params = {
        user_group_nos: this.activityForm.groups,
        name: this.activityForm.name,
        coupon_type: this.activityForm.couponType,
        date_range: this.activityForm.dateRange,
        start_time: this.activityForm.dateRange === 1 ? this.activityForm.selectTime[0] : this.activityForm.startTime,
        limit_type: this.activityForm.isAmountlimit,
        upper_limit: times(this.activityForm.amountlimit),
        status: this.activityForm.status,
        pay_scene: this.activityForm.payScene,
        cycle_type: this.activityForm.cycleType
      }
      if (this.activityForm.payChannel && this.activityForm.payChannel.length > 0 && this.$refs.payChannel) {
        // 如果选了充值渠道限制就传对应的参数
        params.payinfos = this.$refs.payChannel.getCheckedKeysByHalf()
      }
      if (this.activityForm.dateRange === 1) {
        params.end_time = this.activityForm.selectTime[1]
      }
      if (this.activityForm.couponTip) {
        params.tip = this.activityForm.couponTip
      }
      if (this.activityForm.subtractType === 1) { // 随机立减
        params.fee = JSON.stringify({
          type: this.activityForm.subtractType,
          rules: [Number(times(this.activityForm.minRange)), Number(times(this.activityForm.maxRange))]
        })
        params.check_price = Number(times(this.activityForm.checkPrice))
      } else { // 固定额度
        params.discount_rule = this.activityForm.gradList.map(item => {
          return {
            limit_fee: times(item.full),
            coupon_fee: times(item.reduce)
          }
        })
      }
      if (this.activityForm.cycleLimitType !== -1) {
        params.cycle_limit_num = Number(this.activityForm.cycleLimitNum)
      } else {
        params.cycle_limit_num = -1
      }
      // return
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundMarketingRechargeAddPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success('创建活动成功！')
        this.$closeCurrentTab(this.$route.path)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 修改
    async modifyActivityRechargeHandle() {
      if (!this.checkActivityRule()) {
        return
      }
      let params = {
        coupon_no: this.activityForm.id,
        user_group_nos: this.activityForm.groups,
        name: this.activityForm.name,
        coupon_type: this.activityForm.couponType,
        date_range: this.activityForm.dateRange,
        start_time: this.activityForm.dateRange === 1 ? this.activityForm.selectTime[0] : this.activityForm.startTime,
        limit_type: this.activityForm.isAmountlimit,
        upper_limit: times(this.activityForm.amountlimit),
        status: this.activityForm.status,
        pay_scene: this.activityForm.payScene,
        cycle_type: this.activityForm.cycleType
      }
      if (this.activityForm.payChannel && this.activityForm.payChannel.length > 0 && this.$refs.payChannel) {
        params.payinfos = this.$refs.payChannel.getCheckedKeysByHalf()
      }
      if (this.activityForm.dateRange === 1) {
        params.end_time = this.activityForm.selectTime[1]
      }
      if (this.activityForm.couponTip) {
        params.tip = this.activityForm.couponTip
      }
      if (this.activityForm.subtractType === 1) { // 随机立减
        params.fee = JSON.stringify({
          type: this.activityForm.subtractType,
          rules: [Number(times(this.activityForm.minRange)), Number(times(this.activityForm.maxRange))]
        })
        params.check_price = Number(times(this.activityForm.checkPrice))
      } else { // 固定额度
        params.discount_rule = this.activityForm.gradList.map(item => {
          return {
            limit_fee: times(item.full),
            coupon_fee: times(item.reduce)
          }
        })
      }
      if (this.activityForm.cycleLimitType !== -1) {
        params.cycle_limit_num = Number(this.activityForm.cycleLimitNum)
      } else {
        params.cycle_limit_num = -1
      }
      // return
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundMarketingRechargeModifyPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success('修改活动成功！')
        this.$closeCurrentTab(this.$route.path)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 梯度添加
    addGradHandle() {
      this.activityForm.gradList.push({
        full: "",
        reduce: ""
      })
    },
    // 梯度删除
    delGradHandle(i) {
      this.activityForm.gradList.splice(i, 1)
    },
    /**
     * 获取充值渠道限制列表
     * @param {*} id
     * @param payInfos 选中的值
     */
    async getPayChannelList(id, payInfos) {
      let params = {
        pay_scene: id
      }
      const [err, res] = await to(this.$apis.apiBackgroundMarketingRechargeGetRechargePayinfoPost(params))

      if (err) {
        console.log("getPayChannelList err", err.message);
        return
      }
      if (res && res.code === 0) {
        console.log("this.channelList", res.data);
        var result = res.data || []
        if (Array.isArray(result) && result.length > 0) {
          result.map((item, key) => {
            var newValue = deepClone(item)
            console.log("item", item);
            item.sub_payway_verbose = Object.getOwnPropertyNames(newValue)[0]
            item.id = key
            item.childrenList = newValue[item.sub_payway_verbose].children_list
            return item
          })
        }
        this.channelList = result
        console.log("this.channelList", this.channelList);
        // 如果是修改需要他进行赋值
        if (payInfos) {
          this.activityForm.payChannel = payInfos
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    /**
     *选择充值方式改变监听
     * @param {*} e
     */
    choosePaySceneChange(e) {
      console.log("choosePaySceneChange", e);
      var id = e
      this.getPayChannelList(id)
    }
  }
};
</script>

<style lang="scss">
.add_active_recharge {
  margin-top: 20px;
  padding: 20px 0;
  .activity-form {
    .common-input {
      width: 400px;
    }
  }
  .inline-item{
    display: inline-block;
  }
  .coupon-type-input{
    width: 135px;
  }
  .label-l{
    margin-right: 10px;
  }
  .label-r{
    margin-left: 10px;
  }
  .coupon-type-box{
    padding-top: 10px;
    .coupon-type-item{
      float: left;
      width: 515px;
    }
    .limit-box{
      margin-top: 20px;
    }
    .max-w{
      display: inline-block;
      min-width: 60px;
      text-align: right;
    }
    .max-r-w{
      display: inline-block;
      min-width: 85px;
      text-align: right;
    }
  }
  .coupon-type-box-tips{
    // max-width: 450px;
    margin-left: 500px;
    // display: inline-block;
    vertical-align: middle;
    p{
      line-height: 1.8;
    }
    .tips-title {
      line-height: 45px;
    }
  }
  .grad{
    margin-bottom: 18px;
    .grad-danger{
      font-size: 18px;
      margin-left: 15px;
      vertical-align: inherit;
      color: #f56c6c;
    }
  }
  .before-required{
    position: relative;
    margin-left: -100px;
    &::before{
      content: "*";
      color: #F56C6C;
      margin-right: 4px;
    }
    .pos-tip{
      position: absolute;
      left: 612px;
    }
  }
}
.clearfix:after {
  display: block;
  clear: both;
  content: '';
  visibility: hidden;
  height: 0;
}
.clearfix {
  zoom: 1;
}
.w-215{
  width: 215px;
}
</style>
