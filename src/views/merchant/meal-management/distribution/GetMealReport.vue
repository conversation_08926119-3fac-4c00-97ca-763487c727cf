<template>
  <div>
    <div class="booking-meal-wrapper container-wrapper">
      <refresh-tool @refreshPage="refreshHandle" />
      <search-form ref="searchRef" @search="searchHandle" :form-setting="searchFormSetting"></search-form>

      <div class="table-wrapper">
        <div class="table-header">
          <div class="table-title">数据列表</div>
          <div class="align-r">
            <button-icon color="origin" type="export" @click="handleExport">导出Excel</button-icon>
            <button-icon color="plain" @click="handleExport">打印</button-icon>
            <button-icon color="plain" @click="handleExport">报表设置</button-icon>
          </div>
        </div>

        <!-- table-content start -->
        <div class="table-content">
          <el-table
            border
            v-loading="isLoading"
            :data="tableData"
            ref="tableData"
            style="width: 100%"
            stripe
            header-row-class-name="ps-table-header-row"
          >
            <el-table-column
              align="center"
              v-for="col in columns"
              :prop="col.column"
              :label="col.label"
              :key="col.column"
            ></el-table-column>
          </el-table>
        </div>
        <!-- table content end -->
        <pagination :total="total" :onPaginationChange="onPaginationChange"></pagination>
      </div>
    </div>
  </div>
</template>

<script>
import { GetMealSearchForm } from './constantsConfig'
export default {
  name: 'ConsumeDetailList',
  data() {
    return {
      isLoading: false,
      columns: [
        { label: '配送区域', column: 'column1' },
        { label: '菜品名称', column: 'column2' },
        { label: '数量', column: 'column3' }
      ],
      tableData: [],
      page: 1,
      pageSize: 10,
      total: 0,
      searchFormSetting: {
        ...GetMealSearchForm
      }
    }
  },
  mounted() {
    this.initLoad()
  },
  methods: {
    initLoad() {
      this.requestPaymentOrderTotalList()
    },

    refreshHandle() { },

    async searchHandle() { },

    handleExport() { },

    async requestPaymentOrderTotalList() {
      const params = {
        page: this.page,
        page_size: this.pageSize
      }
      const res = await this.$apis.apiBackgroundReportCenterDataReportPaymentOrderTotalListPost(params)
      console.log(res)
    },

    onPaginationChange() { }
  }
}
</script>
