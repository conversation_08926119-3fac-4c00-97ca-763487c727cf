<template>
  <div class="AddOrEditOnScene container-wrapper">
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">当前组织：{{orgName}}</div>
      </div>
      <div>
        <el-form ref="onSceneFormRef" :model="onSceneForm" label-width="120px" :rules="onSceneRules">
          <div v-if="sceneType === 'normal'" class="tips">注：分组+消费点+餐段的组合只能有一条</div>
          <div v-if="sceneType === 'device'" class="tips">注：消费点+餐段的组合只能有一条</div>
          <el-form-item v-if="sceneType === 'normal'" label="适用分组" prop="card_groups">
            <user-group-select multiple v-model="onSceneForm.card_groups"></user-group-select>
          </el-form-item>
          <el-form-item v-if="sceneType === 'device'" label="适用设备" prop="devices">
            <el-select
              v-model="onSceneForm.devices"
              placeholder="请选择设备"
              multiple
              class="ps-select"
            >
              <el-option
                v-for="item in deviceList"
                :key="item.key"
                :label="item.name"
                :value="item.key"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="适用消费点" prop="organizations">
            <consume-select multiple v-model="onSceneForm.organizations"></consume-select>
          </el-form-item>
          <el-form-item label="可堂食餐段" prop="scene_meal">
            <el-checkbox-group v-model="onSceneForm.scene_meal">
              <el-checkbox
                v-for="mt in mealTypeList"
                :label="mt.value"
                :key="mt.value"
                class="ps-checkbox"
              >
                {{ mt.label }}
              </el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item label="菜品" prop="menu_type">
            <el-radio-group v-model="onSceneForm.menu_type" class="ps-radio">
              <el-radio label="month">月菜谱</el-radio>
              <el-radio label="week">周菜谱</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="点餐截止时间(餐段结束前)" class="label-block">
            <div class="fake-table-wrapper">
              <div class="fake-table">
                <div class="fake-col" v-for="mt in mealTypeList" :key="mt.value">
                  <span class="fake-col-title">{{ mt.label }}</span>
                  <el-input-number
                    controls-position="right"
                    v-model="onSceneForm[mt.field]"
                    :min="0"
                    :max="120"
                    :disabled="!onSceneForm.scene_meal.includes(mt.value)"
                    size="mini"
                    style="width: 90px; margin: 10px;"
                  ></el-input-number>
                </div>
              </div>
              <span style="margin-left: 16px">分钟</span>
            </div>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" class="ps-origin-btn w-150" @click="saveForm">保存</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>
<script>
import UserGroupSelect from '@/components/UserGroupSelect'
import ConsumeSelect from '@/components/ConsumeSelect'
import { MEAL_TYPES } from '@/utils/constants'
export default {
  name: 'AddOrEditOnScene',
  components: {
    UserGroupSelect,
    ConsumeSelect
  },
  data() {
    return {
      type: '',
      sceneType: '',
      orgName: '',
      onSceneForm: {
        card_groups: [],
        onSceneForm: [],
        organizations: [],
        scene_meal: [],
        menu_type: 'month',
        breakfast_ahead: 0, // 餐段结束前(早餐)
        lunch_ahead: 0, // 餐段结束前(午餐)
        hit_tea_ahead: 0, // 餐段结束前(下午茶)
        dinner_ahead: 0, // 餐段结束前(晚餐)
        midnight_ahead: 0, // 餐段结束前(夜宵)
        early_ahead: 0 // 餐段结束前(凌晨餐)
      },
      onSceneRules: {},
      mealTypeList: MEAL_TYPES,
      deviceList: []
    }
  },
  created() {
    this.initLoad()
  },
  methods: {
    initLoad() {
      console.log(this.$route)
      this.orgName = this.$store.getters.userInfo.company_name
      this.type = this.$route.params.type
      this.sceneType = this.$route.params.sceneType
      if (this.type === 'edit') {
        this.editData = JSON.parse(decodeURIComponent(this.$route.query.data))
        this.initEditForm()
      }
    },
    saveForm() {}
  }
}
</script>
<style lang="scss">
  .fake-table-wrapper {
    display: flex;
    align-items: flex-end;
    .fake-table {
      display: flex;
      // width: 600px;
      border: 1px solid #ddd;
      .fake-col {
        display: flex;
        align-items: center;
        flex-direction: column;
        // width: 130px;
        border-right: 1px solid #ddd;

        .fake-col-title {
          display: block;
          width: 100%;
          border-bottom: 1px solid #ddd;
          text-align: center;
        }
      }
    }
  }
.label-block{
  .el-form-item__label{
    float: none;
    margin-left: 30px;
  }
}
.tips {
  display: block;
  margin: 0 0 12px 50px;
  color: #ff9b45;
  font-size: 12px;
}
</style>
