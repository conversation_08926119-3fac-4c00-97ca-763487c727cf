<template>
  <div class="TableAdmin container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <!-- search start -->
    <search-form ref="searchRef" :form-setting="searchFormSetting" label-width="105px" @search="searchHandle"></search-form>
    <!-- search end -->
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" type="add" @click="openDialog('add')">新增</button-icon>
          <button-icon color="origin" type="add" @click="openDialog('mulAdd')">批量新增</button-icon>
          <button-icon color="plain" @click="openDialog('mulDownload')">批量下载二维码</button-icon>
          <button-icon color="plain" type="del" @click="mulOperation('mulDel')">批量删除</button-icon>
        </div>
      </div>
      <div class="table-content">
      <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
        >
          <el-table-column type="selection" width="50" align="center" class-name="ps-checkbox"></el-table-column>
          <el-table-column prop="name" label="名称" align="center" ></el-table-column>
          <el-table-column prop="name" label="标准就餐人数" align="center" ></el-table-column>
          <el-table-column prop="name" label="适用消费点" align="center" ></el-table-column>
          <el-table-column align="center" width="180" label="操作" fixed="right">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                class="ps-text"
                @click="openDialog('code')"
              >二维码</el-button>
              <el-button
                type="text"
                size="small"
                class="ps-blue"
                @click="openDialog('edit')"
              >编辑</el-button>
              <el-button
                type="text"
                size="small"
                class="ps-warn"
                @click="mulOperation('del', scope.row.id)"
              >删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100, 500]"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
    </div>
    <!-- 分页 end -->
    <table-dialog
      :isshow.sync="dialogVisible"
      :type="dialogType"
      :title="dialogTitle"
      :address-info="addressInfo"
      @confirm="searchHandle"
    />
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce } from '@/utils'
import TableDialog from '../on-scene-setting/components/TableDialog.vue'
export default {
  name: 'TableAdmin',
  components: { TableDialog },
  // mixins: [activatedLoadData],
  data() {
    return {
      tableData: [{ id: 0 }],
      currentPage: 1, // 第几页
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      isLoading: false, // 刷新数据
      searchFormSetting: {
        org_ids: {
          type: 'organizationSelect',
          multiple: false,
          isLazy: false,
          label: '组织',
          value: '',
          placeholder: '请选择组织'
        },
        facepay: {
          type: 'select',
          label: '类型',
          value: '',
          placeholder: '请选择类型',
          dataList: []
        }
      },
      dialogVisible: false,
      dialogType: '',
      dialogTitle: '',
      addressInfo: {}, // 弹窗数据
      selectListId: []
    }
  },
  created () {
    this.initLoad()
  },
  mounted() {
  },
  methods: {
    initLoad() {
    },
    // 刷新页面
    refreshHandle() {
      this.currentPage = 1;
      this.tableData = []
      this.initLoad()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.currentPage = 1;
    }, 300),
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val;
      // this.getCardUserList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val;
      // this.getCardUserList()
    },
    // 操作
    mulOperation(type, data) {
      if (!data && !this.selectListId.length) {
        return this.$message.error('请先选择数据！')
      }
      let title = '提示'
      let content = ''
      switch (type) {
        case "mulDel":
          content = '确定批量删除所选配送区域吗？'
          break;
        case "del":
          content = '确定删除该配送区域吗？'
          break;
      }
      this.$confirm(`${content}`, `${title}`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            // let params = {}
            switch (type) {
              case "mulDel":
                break;
              case "del":
                break;
            }
            // this.delDevice(params)
            done()
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {
        })
        .catch(e => {})
    },
    openDialog(type, data) {
      this.addressInfo = data
      this.dialogType = type
      switch (type) {
        case 'add':
          this.dialogTitle = '新增'
          break;
        case 'mulAdd':
          this.dialogTitle = '批量新增'
          break;
        case 'edit':
          this.dialogTitle = '编辑'
          break;
        case 'code':
          this.dialogTitle = '查看二维码'
          break;
      }
      this.dialogVisible = true
    }
  }
}
</script>

<style lang="scss">
@import "~@/styles/variables.scss";

</style>
