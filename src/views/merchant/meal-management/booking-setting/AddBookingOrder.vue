<template>
  <div class="page" v-loading="isLoading">
    <el-form ref="form" :model="form" label-width="180px" size="small" :rules="rules">
      <span class="title">基本设置</span>
      <el-form-item label="组织">
        <!-- <el-select v-model="form.organization"></el-select> -->
        <span>{{ organizationName }}</span>
      </el-form-item>

      <el-form-item label="适用分组" :prop="form.applied_to_visitor ? '' : 'card_user_groups'">
        <user-group-select multiple v-model="form.card_user_groups" :options="groupOptions" class="w-350"/>
        <el-checkbox v-if="showVisitor" v-model="form.applied_to_visitor" class="ps-checkbox" style="margin-left: 25px;">游客</el-checkbox>
      </el-form-item>

      <el-form-item label="适用消费点" prop="consume_organizations">
        <organization-consume-list
          multiple
          v-model="form.consume_organizations"
          :options="consumeOpts"
          class="w-350"
        ></organization-consume-list>
      </el-form-item>

      <span class="tips">注：分组+消费点+餐段的组合只能有一条</span>

      <el-form-item label="可预约餐段" prop="meal_types">
        <el-checkbox-group v-model="form.meal_types" @change="changeMealTypes">
          <el-checkbox v-for="mt in mealType" :label="mt.value" :key="mt.value" name="meal_types">
            {{ mt.label }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="单餐限制次数">
        <el-radio-group v-model="form.is_limit">
          <el-radio :label="0">不限制</el-radio>
          <el-radio :label="1">单人单餐限制</el-radio>
        </el-radio-group>
        <el-input
          v-model="form.buy_limit_count"
          onkeyup="this.value=this.value.replace(/\D/g,'')"
          onafterpaste="this.value=this.value.replace(/\D/g,'')"
          v-if="form.is_limit === 1"
          style="width: 60px; margin: 0 12px"
        ></el-input>
        次
      </el-form-item>
      <span class="tips" v-if="form.is_limit === 1">注：限制用户单餐段点餐次数</span>
      <el-form-item label="单人单餐菜品限制份数">
        <el-radio-group v-model="form.is_limit_food">
          <el-radio :label="0">不限制</el-radio>
          <el-radio :label="1">单人单餐限制</el-radio>
        </el-radio-group>
        <el-input
          v-model="form.buy_limit_food_count"
          onkeyup="this.value=this.value.replace(/\D/g,'')"
          onafterpaste="this.value=this.value.replace(/\D/g,'')"
          v-if="form.is_limit_food === 1"
          style="width: 60px; margin: 0 12px"
        ></el-input>
        份
      </el-form-item>
      <span class="title">菜品设置</span>
      <el-form-item label="菜品" prop="menu_type">
        <el-radio-group v-model="form.menu_type">
          <el-radio label="month">月菜谱</el-radio>
          <el-radio label="week">周菜谱</el-radio>
        </el-radio-group>
      </el-form-item>

      <span class="title">预约设置</span>
      <el-form-item label="可预约天数" prop="can_reservation_days">
        <el-input-number
          v-model="form.can_reservation_days"
          :min="1"
          controls-position="right"
        ></el-input-number>
        <span class="tail-unit">天</span>
      </el-form-item>
      <!-- <span class="">预约截止时间</span> -->
      <el-form-item label="预约截止时间" class="block-label">
        <el-radio style="display: flex; align-items: center;" class="p-b-20" v-model="form.reservation_close_type" label="today_time" >
          <div>
            <span class="p-r-10 p-l-10">当天</span>
              <el-time-picker
              clearable
              class="ps-picker w-150"
              value-format="HH:mm"
              format="HH:mm"
              v-model="form.today_close_time">
            </el-time-picker>
            <span class="p-l-10">前可预约D+1订单（D为当天，D+1为第二天），超过截止时间后，只允许预约D+2订单</span>
          </div>
        </el-radio>
        <el-radio style="display: flex; align-items: center;" class="p-b-20" v-model="form.reservation_close_type" label="today_time_range" >
          <div>
            <span class="p-r-10 p-l-10">每日</span>
              <el-time-picker
                is-range
                v-model="todayCloseTime"
                value-format="HH:mm"
                format="HH:mm"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                placeholder="选择时间范围">
              </el-time-picker>
            <span class="p-l-10">可预约D+1订单（D为当天，D+1为第二天），非限制时间范围内，只允许预约D+2订单</span>
            <span style="margin-left: 20px;color: #f41818;font-size: 12px;">不填默认不限制</span>
          </div>
        </el-radio>
        <el-radio style="display: flex; align-items: center;" v-model="form.reservation_close_type" label="meal_end_time" >
          <div class="ps-flex-align-c flex-align-c">
           <span  class="p-r-10 p-l-10">餐段结束前</span>
            <div class="fake-table-wrapper">
              <div class="fake-table">
                <div class="fake-col" v-for="mt in mealType" :key="mt.value">
                  <span class="fake-col-title">{{ mt.label }}</span>
                  <el-input-number
                    controls-position="right"
                    v-model="form[mt.field]"
                    :min="0"
                    :disabled="!form.meal_types.includes(mt.value)"
                    size="mini"
                    style="width: 80px"
                  ></el-input-number>
                </div>
              </div>
              <span style="margin-left: 16px">小时</span>
            </div>
          </div>
        </el-radio>
      </el-form-item>
      <span class="tips">注：可设置预约点餐单餐固定金额，若不填则按菜品实际价格收费</span>

      <el-form-item>
        <div class="ship-box clearfix">
          <div class="ship-box-item float-l m-r-30">
            跳过节假日：<el-switch v-model="form.extra.skip_holiday" active-color="#ff9b45"></el-switch>
          </div>
          <div class="ship-box-item float-l m-r-30">
            跳过周末：<el-switch v-model="form.extra.skip_weekends" active-color="#ff9b45"></el-switch>
          </div>
          <div class="ship-box-item float-l m-r-30">
            跳过工作日：<el-switch v-model="form.extra.skip_word_days" active-color="#ff9b45"></el-switch>
          </div>
        </div>
        <!-- 以下日期不跳过 -->
        <div class="ship-box ">
          <div>
            以下日期不跳过：
            <el-button type="text" class="hidden-picker">
              添加
              <el-date-picker
                type="dates"
                :clearable="false"
                v-model="form.extra.no_skip_days"
                placeholder="选择一个或多个日期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                popper-class="hidden-picker-year"
                :picker-options="pickerOptions"
              ></el-date-picker>
            </el-button>
            <el-button type="text" @click="clearNoShipDay">清空</el-button>
          </div>
        </div>
        <transition-group name="el-zoom-in-center" class="no-ship" tag="div">
          <div class="no-ship-time" v-for="(item, index) in form.extra.no_skip_days" :key="item">
            {{ formatNoSkipTime(item) }}
            <div class="del-time" @click="delNoSkipTime(index)">
              <i class="el-icon-error"></i>
            </div>
          </div>
        </transition-group>
      </el-form-item>
      <el-form-item label="特殊日期不支持预约点餐" prop="">
        <div>
          <el-button type="primary" icon="el-icon-date" class="hidden-picker ps-origin-btn">
            选择日期
            <el-date-picker
               type="dates"
               :clearable="false"
               v-model="form.extra.forbidden_date"
               placeholder="选择一个或多个日期"
               format="yyyy-MM-dd"
               value-format="yyyy-MM-dd"
               popper-class="hidden-picker-year"
               :picker-options="pickerOptions"
             ></el-date-picker>
          </el-button>
          <el-button type="text" @click="clearShipDay">清空</el-button>
          <transition-group name="el-zoom-in-center" class="no-ship" tag="div">
            <div class="no-ship-time" v-for="(item, index) in form.extra.forbidden_date" :key="item">
              {{item}}
              <div class="del-time" @click="delSkipTime(index)">
                <i class="el-icon-error"></i>
              </div>
            </div>
          </transition-group>
        </div>
      </el-form-item>

      <el-form-item label="可取餐方式">
        <div class="switch-wrapper">
          <span class="label">食堂取餐</span>
          <el-switch v-model="isCanteen"></el-switch>

          <el-checkbox-group
            v-model="form.take_out_type"
            style="margin-left: 24px"
            :disabled="!isCanteen"
          >
            <el-checkbox label="on_scene" name="take_out_type">堂食</el-checkbox>
            <el-checkbox label="bale" name="take_out_type">食堂自提</el-checkbox>
          </el-checkbox-group>
        </div>
      </el-form-item>
      <el-form-item label="用餐时间段设置">
        <el-switch v-model="form.isOpenDingingTimes" :disabled="!disabledDinging"></el-switch>
        <span class="red">用餐时间段设置*取餐方式为堂食/食堂自提的用户均生效，“可下单数”用来限制多少用户可选择该用餐时间</span>
      </el-form-item>
      <el-form-item label="" v-if="form.isOpenDingingTimes">
        <table border="0" cellspacing="1" cellpadding="3">
          <tbody align="center">
            <tr>
              <th class="min-w-60">餐段</th>
              <th>时间段规则（<span class="red">*不能超出配置的餐段时间</span>）</th>
            </tr>
            <tr v-for="(meal, i) in form.dinging_times" :key="meal.meal_type">
              <td> {{ meal.meal_name }} </td>
              <td>
                <div v-for="(dinging, k) in meal.dinging_time" :key="k" class="dinging-box m-b-10">
                  <span>
                    <el-checkbox class="m-l-5 m-r-5" v-model="dinging.enable"></el-checkbox>
                    <span class="m-l-5">时间段{{ k + 1 }}：</span>
                  </span>
                  <el-form-item class="inline-block" style="margin-bottom: 0;" label-width="0" prop="" :error="dinging.error" :show-message="true">
                    <el-time-select
                      placeholder="起始时间"
                      class="w-150"
                      v-model="dinging.start_time"
                      :picker-options="{
                        start: mealTypeKeys[meal.meal_type].start_time,
                        step: timeStep,
                        end: mealTypeKeys[meal.meal_type].end_time,
                      }"
                      @change="checkMealDingingTime(meal, dinging)"
                      >
                    </el-time-select>
                    <span class="m-l-5 m-r-5">至</span>
                    <el-time-select
                      placeholder="结束时间"
                      class="w-150"
                      v-model="dinging.end_time"
                      :picker-options="{
                        start: mealTypeKeys[meal.meal_type].start_time,
                        step: timeStep,
                        end: mealTypeKeys[meal.meal_type].end_time,
                        minTime: mealTypeKeys[meal.meal_type].across_day?'':dinging.start_time
                      }"
                      @change="checkMealDingingTime(meal, dinging)"
                      >
                    </el-time-select>
                  </el-form-item>
                  <!-- :rules="rules.order_count" :prop="'form.dinging_times['+i+'].dinging_time['+k+'].order_count'" -->
                  <el-form-item class="inline-block" style="margin-bottom: 0;" label="" label-width="0" :show-message="false" :rules="rules.order_count" :prop="'dinging_times['+i+'].dinging_time['+k+'].order_count'">
                    <span class="m-l-5">可下单数：</span>
                    <el-input class="w-150 inline-block" v-model="dinging.order_count"></el-input>
                  </el-form-item>
                  <el-button class="ps-warn m-l-10" type="text" @click="deleteDingingTime(meal, k)">删除</el-button>
                </div>
                <div>
                  <i class="el-icon-plus" style="color: #66b1ff;"></i>
                  <el-button type="text" @click="addDingingTime(meal)">新增时间段</el-button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </el-form-item>
      <el-form-item label="">
        <div class="switch-wrapper">
          <span class="label" style="margin-left: 28px">外卖</span>
          <el-switch v-model="isTakeAway"></el-switch>
          <el-checkbox-group
            :disabled="!isTakeAway"
            v-model="form.take_out_type"
            style="margin-left: 24px"
          >
            <el-checkbox label="cupboard" v-model="form.take_out_type">取餐柜自取</el-checkbox>
            <el-checkbox label="waimai" v-model="form.take_out_type">外卖配送</el-checkbox>
          </el-checkbox-group>
        </div>
          <div class="inline-box">
          <div class="inline-box" v-if="form.take_out_type.includes('cupboard')">
            <div class="ps-flex">
              取餐柜起送费:
              <el-form-item label="" prop="cupboard_delivery_fee">
              <el-input
                  v-model="form.cupboard_delivery_fee"
                  v-if="form.take_out_type.includes('cupboard')"
                  style="width: 80px;"
              ></el-input>
              </el-form-item>
              元
            </div>
          </div>
          <!-- <div class="inline-box" v-if="form.take_out_type.includes('cupboard')">
            <div class="ps-flex">
              取餐柜打包费:
              <el-form-item label="" prop="extra.cupboard_takeaway_fee">
              <el-input
                  v-model="form.extra.cupboard_takeaway_fee"
                  v-if="form.take_out_type.includes('cupboard')"
                  style="width: 80px;"
              ></el-input>
              </el-form-item>
              元
            </div>
          </div> -->
          <div class="inline-box" v-if="form.take_out_type.includes('waimai')">
            <div class="ps-flex">
              外卖起送费:
              <el-form-item label="" prop="waimai_delivery_fee">
              <el-input
                  v-model="form.waimai_delivery_fee"
                  v-if="form.take_out_type.includes('waimai')"
                  style="width: 80px;"
              ></el-input>
              </el-form-item>
              元
            </div>
          </div>
          </div>
          <span class="tips" style="margin-left: 0">
          注：①不填默认没有起送费，<br>②单餐菜品金额+服务费大于等于起送费时，才可进行下单
          </span>
      </el-form-item>

      <el-form-item label="扣费方式" v-if="isCanteen">
        <el-radio-group v-model="form.consume_type">
          <el-radio label="online">线上支付</el-radio>
          <el-radio label="offline">线下核销支付</el-radio>
        </el-radio-group>
        <span class="tips" style="display: inline-block">
          注：食堂取餐可选择扣费方式，外卖仅支持线上支付
        </span>
      </el-form-item>

      <!-- <el-form-item label="起送费">
        <el-input v-model="form.waimai_fee" style="width: 100px"></el-input>
        <span class="tail-unit">元</span>
        <span class="tips" style="display: inline-block">注：起送费若不填则默认无起送费</span>
      </el-form-item> -->

      <!-- <el-form-item label="固定单餐金额">
        <el-switch v-model="form.is_fixed_meal"></el-switch>
        <div class="fake-table-wrapper" v-if="form.is_fixed_meal">
          <div class="fake-table">
            <div class="fake-col" v-for="mt in mealType" :key="mt.value">
              <span class="fake-col-title">{{ mt.label }}</span>
              <el-input-number
                controls-position="right"
                v-model="form[mt.field2]"
                :min="0"
                :disabled="!form.meal_types.includes(mt.value)"
                size="mini"
                style="width: 80px"
              ></el-input-number>
            </div>
          </div>
          <span style="margin-left: 16px">元</span>
        </div>
        <span class="tips" style="margin-left: 0">
          注：可设置预约点餐单餐固定金额，若不填则按菜品实际价格收费
        </span>
      </el-form-item> -->

      <el-form-item label="是否允许取消">
        <el-switch v-model="form.can_refund"></el-switch>
        <span class="tips" style="display: inline-block">注：订单完成后不允许取消</span>
        <div class="input-wrapper">
          <div v-if="form.can_refund">
            <el-radio v-model="form.refund_remit_type" label="pay_after">
              <span>
                <span style="margin-left: 28px;">下单后</span>
                <el-input-number
                  style="width: 100px; margin: 0 10px"
                  v-model="form.pay_after"
                  :disabled="form.refund_remit_type !== 'pay_after'"
                  :min="1"
                  controls-position="right"
                ></el-input-number>
                <span>小时内</span>
              </span>
            </el-radio>
          </div>
          <div v-if="form.can_refund">
            <el-radio v-model="form.refund_remit_type" label="meal_before">
              <span class="input-wrapper">
                <span>餐段结束前</span>
                <el-input-number
                  style="width: 100px; margin: 10px"
                  v-model="form.meal_before"
                  :disabled="form.refund_remit_type !== 'meal_before'"
                  :min="1"
                  controls-position="right"
                ></el-input-number>
                <span>小时</span>
              </span>
            </el-radio>
          </div>
          <div v-if="form.can_refund">
            <el-radio v-model="form.refund_remit_type" label="day_hour_before">
              <span class="input-wrapper">
                <span>就餐当日前</span>
                <el-input-number
                  style="width: 100px; margin: 10px"
                  v-model="form.refund_remit_day"
                  :disabled="form.refund_remit_type !== 'day_hour_before'"
                  :min="0"
                  controls-position="right"
                  @change="changeRefundRemitDay"
                ></el-input-number>
                <span>天</span>
                <el-input-number
                  style="width: 100px; margin: 10px"
                  v-model="form.refund_remit_before_hour"
                  :min="minHour"
                  :max="23"
                  controls-position="right"
                  @change="changeRefundRemitBeforeHour"
                  :disabled="form.refund_remit_type !== 'day_hour_before'"
                ></el-input-number>
                <span>小时</span>
              </span>
            </el-radio>
          </div>
        </div>
      </el-form-item>
      <el-form-item label="取消订单是否需要审核" v-if="form.can_refund">
        <el-switch v-model="form.cancel_review"></el-switch>
        <el-form-item v-if="form.cancel_review" label="">
          <el-radio-group v-model="form.review_type">
            <el-radio label="auto_reject">开餐后，自动拒绝</el-radio>
            <el-radio label="auto_success">开餐后，自动同意</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form-item>
      <el-form-item label="服务费" prop="fuwu_fee">
        <!-- <el-input v-model="form.fuwu_fee" style="width: 100px"></el-input>
        <span class="tail-unit">元</span> -->
        <div >
          <el-checkbox-group  class="ps-checkbox" v-model="chooseServe" prop="chooseServe"  @change="serveCheckChange">
            <el-checkbox v-for="(item,index) in serveFeeList" :key="index" :label="item.type"  :value="item.type" >{{item.name}}
              <!-- <div> -->
                 <el-input class="w-100 m-l-10 m-r-10" placeholder="请输入" v-model="item.price" :disabled="item.disabled"></el-input>元
                 <span v-if="item.type === 'waimai'">，无电梯额外服务费</span>
                 <span  v-if="item.type === 'waimai'">
                  <el-tooltip placement="top" class="item">
                  <div slot="content">
                    <div v-html="'用户选择无电梯的楼层（是否有电梯来源于配送地址的配置） \n外卖额外收取费用'" style="white-space: pre-line"></div>
                  </div>
                  <i class="el-icon-question" style="font-size: 18px; color: #ff9b45"></i>
                </el-tooltip>
                  <el-input class="w-100 m-l-10 m-r-10" placeholder="请输入" v-model="item.elevator_fee" :disabled="item.disabled"></el-input>元
                 </span>
              <!-- </div> -->
            </el-checkbox>
          </el-checkbox-group>
        </div>
      </el-form-item>
    </el-form>

    <el-button type="primary" style="margin-left: 120px;" size="small" @click="saveForm">
      {{ this.$route.query.id === 'new' ? '确定新增' : '保存编辑' }}
    </el-button>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { MEAL_TYPES } from '@/utils/constants'
import UserGroupSelect from '@/components/UserGroupSelect'
import OrganizationConsumeList from '@/components/OrganizationConsumeList'
import { divide, times, deleteEmptyKey, deepClone } from '@/utils'
import { isPrice } from '@/utils/validata.js'
import { validataPlateAmountPrice } from '@/assets/js/validata'
export default {
  name: 'AddBookingOrder',
  props: {},
  components: {
    UserGroupSelect,
    OrganizationConsumeList
  },
  computed: {
    ...mapGetters(['userInfo', 'organization']),
    disabledDinging() { // 餐段时间设置按钮是否置灰
      // (this.form.take_out_type.includes('on_scene') || this.form.take_out_type.includes('bale'))
      let disabled = this.isCanteen
      if (!disabled) {
        // eslint-disable-next-line vue/no-side-effects-in-computed-properties
        this.form.isOpenDingingTimes = false
      }
      return disabled
    }
  },
  watch: {
    organizationId(val) {
      this.setOrganizationName()
    }
  },
  mounted() {
    this.initData()
    this.form.is_open = this.$route.query.data && this.$route.query.data.is_open
  },
  data() {
    let validateNumber = (rule, value, callback) => {
      let reg = /^\d+$/
      if (!reg.test(value)) {
        callback(new Error('下单数格式不正确'))
      } else {
        if (+value === 0) {
          return callback(new Error('下单数不能为0'))
        }
        callback()
      }
    }
    return {
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 24 * 60 * 60 * 1000
        }
      },
      refundFlag: false,
      organizationName: '',
      organizationId: this.$store.getters.organization,
      isAdd: true,
      mealType: MEAL_TYPES,
      isLoading: false,
      tableData: [],
      isCanteen: false,
      isTakeAway: false,
      rules: {
        card_user_groups: [{ required: true, message: '至少选择一个分组' }],
        meal_types: [{ required: true, message: '至少选择一个餐段' }],
        menu_type: [{ required: true, message: '请选择菜品' }],
        can_reservation_days: [{ type: 'number', required: true, message: '时间必须大于1天' }],
        consume_organizations: [{ required: true, message: '至少选择一个消费点' }],
        fuwu_fee: [{ validator: validataPlateAmountPrice, trigger: "change" }],
        order_count: [{ validator: validateNumber, trigger: "change" }],
        cupboard_delivery_fee: [{ validator: validataPlateAmountPrice, trigger: "change" }],
        'extra.cupboard_takeaway_fee': [{ validator: validataPlateAmountPrice, trigger: "change" }],
        waimai_delivery_fee: [{ validator: validataPlateAmountPrice, trigger: "change" }]
      },
      consumeOpts: {
        label: 'name',
        value: 'id'
      },
      groupOptions: {
        label: 'group_name',
        value: 'id'
      },
      todayCloseTime: null, // 每日时间
      form: {
        menu_type: '', // 菜品
        meal_types: [],
        is_limit: 0,
        is_limit_food: 0, // 单人单餐菜品限制份数
        buy_limit_food_count: 0, // 单人单餐菜品限制份数
        is_open: false,
        buy_limit_count: 0, // 单餐限制次数
        is_next: false, // 是否允许预约下个月/周
        can_reservation_days: undefined, // 可预约天数
        breakfast_ahead: 0, // 餐段结束前(早餐)
        lunch_ahead: 0, // 餐段结束前(午餐)
        hit_tea_ahead: 0, // 餐段结束前(下午茶)
        dinner_ahead: 0, // 餐段结束前(晚餐)
        midnight_ahead: 0, // 餐段结束前(夜宵)
        early_ahead: 0, // 餐段结束前(凌晨餐)
        consume_type: 'online', // 扣费方式
        take_out_type: [], // 取餐方式
        waimai_fee: 0, // 外卖费
        fuwu_fee: 0, // 服务费
        is_fixed_meal: false, // 是否固定单餐金额
        breakfast_fixed: 0, // 固定单餐金额(早餐)
        lunch_fixed: 0, // 固定单餐金额(午餐)
        hit_tea_fixed: 0, // 固定单餐金额(下午茶)
        dinner_fixed: 0, // 固定单餐金额(晚餐)
        midnight_fixed: 0, // 固定单餐金额(夜宵)
        early_fixed: 0, // 固定单餐金额(凌晨餐)
        can_refund: true, // 是否支持取消
        refund_remit_type: 'pay_after', // 取消类型
        refund_remit_time: undefined,
        refund_remit_day: '', // 取消(天数)
        refund_remit_before_hour: '', // 取消(开餐前小时)
        pay_after: undefined, // 下单后几小时内可退
        meal_before: undefined, // 餐段结束前几小时可退
        organization: '', // 组织
        consume_organizations: [], // 消费点组织
        card_user_groups: [], // 适用分组
        cancel_review: false, // 是否取餐订单需要审核
        review_type: 'auto_reject', // 取消订单审核类型 ‘auto_reject` : "开餐后，自动拒绝" * `auto_success` : "开餐后，自动同意
        dinging_times: [], // 用餐时间段设置
        isOpenDingingTimes: false, // 是否启用餐段时间设置
        reservation_close_type: 'today_time',
        today_close_time: '',
        cupboard_delivery_fee: 0, // 取餐柜起送费
        waimai_delivery_fee: 0, // 外卖起送费
        applied_to_visitor: false, // 是否支持游客点餐
        // 结构变了
        extra: {
          forbidden_date: [], // 节假日日期
          today_close_time_start: 0, // 截止时间
          today_close_time_end: 0, // 截止时间
          cupboard_takeaway_fee: 0, // 取餐柜打包费
          skip_holiday: false, // 跳过节假日
          skip_weekends: false, // 跳过周末
          skip_word_days: false, // 跳过工作日
          no_skip_days: [] // 不跳过的日期
        },
        fuwu_fee_extra: {} // 服务费组参暂存
      },
      minHour: 0, // 审批最小时间,
      showVisitor: false, // 是否显示游客选项，常规设置开启后才显示
      mealTypeKeys: {},
      timeStep: '00:01', // 时间的步长
      serveFeeList: [
        { name: '堂食', price: "", type: 'on_scene', disabled: true },
        { name: '食堂自提', price: "", type: 'bale', disabled: true },
        { name: '取餐柜取餐', price: "", type: 'cupboard', disabled: true },
        { name: '外卖配送', price: "", elevator_fee: '', type: 'waimai', disabled: true }
      ],
      chooseServe: []// 选择的服务费
    }
  },
  methods: {
    async initData() {
      this.initMealTypeKeys()
      await this.getCommonSettings()
      this.loadEditData()
      this.setOrganizationName()
      this.getOrgVisitor()
    },
    loadEditData() {
      const editData = this.$route.query.data ? JSON.parse(this.$route.query.data) : undefined
      if (editData) {
        try {
          this.form.take_out_type = JSON.parse(editData.take_out_type)
        } catch (e) {
          this.form.take_out_type = editData.take_out_type
        }
        this.isAdd = false
        this.organizationName = editData.organization_alias
        this.isCanteen =
          this.form.take_out_type.includes('on_scene') || this.form.take_out_type.includes('bale')
        this.isTakeAway =
          this.form.take_out_type.includes('waimai') || this.form.take_out_type.includes('cupboard')
        let fields = MEAL_TYPES.map(d => d.field)
        fields = [
          ...fields,
          'consume_type',
          'card_user_groups',
          'consume_organizations',
          'menu_type',
          'can_reservation_days',
          'can_refund',
          'is_fixed_meal',
          'cancel_review',
          'review_type',
          'reservation_close_type'
          // 'today_close_time'
        ]
        fields.forEach(f => {
          this.form[f] = editData[f]
        })
        if (editData.extra.no_skip_days) {
          this.form.extra.no_skip_days =
            typeof editData.extra.no_skip_days === 'string'
              ? JSON.parse(editData.extra.no_skip_days)
              : editData.extra.no_skip_days
        }
        let priceKeys = MEAL_TYPES.map(d => d.field2)
        priceKeys.push('fuwu_fee', 'waimai_fee', 'cupboard_delivery_fee', 'waimai_delivery_fee')
        priceKeys.forEach(f => {
          this.form[f] = divide(editData[f])
        })
        let extraFields = ['cupboard_takeaway_fee']
        extraFields.forEach(f => {
          this.form.extra[f] = divide(editData.extra[f])
        })
        // this.form.fuwu_fee = editData.fuwu_fee / 100
        // this.form.waimai_fee = editData.waimai_fee / 100
        this.form.meal_types = editData.meal_type_detail.meal_type
        if (editData.buy_limit_count) {
          this.form.is_limit = 1
          this.form.buy_limit_count = editData.buy_limit_count
        } else {
          this.form.is_limit = 0
        }
        if (editData.buy_limit_food_count) {
          this.form.is_limit_food = 1
          this.form.buy_limit_food_count = editData.buy_limit_food_count
        } else {
          this.form.is_limit_food = 0
        }
        if (editData.extra && editData.extra.forbidden_date && editData.extra.forbidden_date.length) {
          this.form.extra.forbidden_date = editData.extra.forbidden_date
        }
        if (editData.today_close_time) {
          this.form.today_close_time = this.formateTimestamp(editData.today_close_time)
        }
        if (editData.extra && editData.extra.today_close_time_start && editData.extra.today_close_time_end) {
          this.todayCloseTime = []
          this.todayCloseTime[0] = this.formateTimestamp(editData.extra.today_close_time_start)
          this.todayCloseTime[1] = this.formateTimestamp(editData.extra.today_close_time_end)
        } else {
          this.todayCloseTime = null
        }
        if (editData.extra) {
          this.form.extra.skip_holiday = editData.extra.skip_holiday
          this.form.extra.skip_weekends = editData.extra.skip_weekends
          this.form.extra.skip_word_days = editData.extra.skip_word_days
        }
        if (editData.can_refund) {
          this.form.refund_remit_type = editData.refund_remit_type
          if (editData.refund_remit_type === 'meal_before') {
            this.form.meal_before = editData.refund_remit_time
            this.form.pay_after = ''
          } else if (editData.refund_remit_type === 'pay_after') {
            this.form.pay_after = editData.refund_remit_time
            this.form.meal_before = ''
          } else {
            this.form.pay_after = ''
            this.form.meal_before = ''
            this.form.refund_remit_day = editData.refund_remit_day
            this.form.refund_remit_before_hour = editData.refund_remit_before_hour
          }
        }
        if (editData.applied_to_visitor) {
          this.form.applied_to_visitor = true
        }
        // 添加
        this.form.dinging_times = []
        if (editData.dinging_times.length) {
          this.form.isOpenDingingTimes = true
          editData.dinging_times.forEach((item, i) => {
            // 餐段开启了才能添加
            if (this.mealTypeKeys[item.meal_type].enable) {
              let list = {
                meal_type: item.meal_type,
                meal_name: this.mealTypeKeys[item.meal_type].label,
                dinging_time: item.dinging_time.map(v => {
                  return { ...v, error: '' }
                })
              }
              this.form.dinging_times.push(list)
            }
          })
          // 排序
          this.sortMealDinging()
        } else {
          this.form.isOpenDingingTimes = false
          this.form.meal_types.forEach(v => {
            if (this.mealTypeKeys[v].enable) {
              let list = {
                meal_type: v,
                meal_name: this.mealTypeKeys[v].label,
                dinging_time: []
              }
              this.form.dinging_times.push(list)
            }
          })
        }
        // 设置服务费
        if (Reflect.has(editData, "fuwu_fee_extra") && editData.fuwu_fee_extra && Object.keys(editData.fuwu_fee_extra).length > 0) {
          this.setServeCheck(editData.fuwu_fee_extra)
        }
      }
    },
    formateTimestamp(timestamp) {
      return parseInt(timestamp / 3600) + ':' + parseInt(timestamp % 3600 / 60) + ':' + timestamp % 60
    },
    changeRefundRemitDay(val) {
      // eslint-disable-next-line eqeqeq
      if (this.form.refund_remit_day == 0 && val == 0) {
        this.$set(this.form, 'refund_remit_before_hour', 1)
        this.minHour = 1
      } else {
        this.minHour = 0
      }
    },
    changeRefundRemitBeforeHour(val) {
      // eslint-disable-next-line eqeqeq
      if (this.form.refund_remit_day == 0 && val == 0) {
        this.minHour = 1
      } else {
        this.minHour = 0
      }
    },
    checkForm() {
      let result = true
      // 如果开了固定单餐金额，那已勾选的餐段的固定金额补贴
      if (this.form.is_fixed_meal) {
        const res = MEAL_TYPES.filter(d => this.form.meal_types.includes(d.value))
        for (let i = 0; i < res.length; i++) {
          if (['', undefined].includes(this.form[res[i].field2])) {
            this.$message.error('请填写选中的餐段固定金额')
            return false
          }
        }
      }
      // 允许取消的话，取消时间二选一，必选
      if (this.form.can_refund) {
        // if (this.form.pay_after === undefined && this.form.meal_before === undefined) {
        //   this.$message.error('取消时间必须二选一')
        //   return false
        // }
        let pass = true
        if (!this.form.refund_remit_type) {
          this.$message.error('请选择取消订单类型！')
          return false
        }
        if (this.form.refund_remit_type === 'pay_after' && this.form.pay_after === undefined) {
          pass = false
        }
        if (this.form.refund_remit_type === 'meal_before' && this.form.meal_before === undefined) {
          pass = false
        }
        if (this.form.refund_remit_type === 'day_hour_before' && (this.form.refund_remit_day === undefined || this.form.refund_remit_before_hour === undefined)) {
          pass = false
        }
        if (!pass) {
          this.$message.error('请填写正确的取消时间！')
          result = false
        }
      }
      if (this.disabledDinging) {
        for (let index = 0; index < this.form.dinging_times.length; index++) {
          const mealItem = this.form.dinging_times[index];
          if (mealItem.dinging_time) {
            for (let k = 0; k < mealItem.dinging_time.length; k++) {
              const item = mealItem.dinging_time[k];
              this.checkMealDingingTime(mealItem, item)
            }
          }
        }
        // check dinging 检查餐段时间
        for (let index = 0; index < this.form.dinging_times.length; index++) {
          const mealItem = this.form.dinging_times[index];
          if (mealItem.dinging_time) {
            for (let k = 0; k < mealItem.dinging_time.length; k++) {
              const item = mealItem.dinging_time[k];
              if (item.error) {
                result = false
                this.$message.error('请检查用餐时间！')
                break
              }
            }
          }
        }
      }
      // 添加服务费判断，选中的服务费不能为空吧并且要服务金额格式
      if (this.chooseServe && this.chooseServe.length > 0) {
        for (let i = 0; i < this.chooseServe.length; i++) {
          var item = this.serveFeeList.find(subItem => {
            return subItem.type === this.chooseServe[i]
          })
          // 给服务费组参 外卖是有两个
          if (item.type === 'waimai') {
            this.$set(this.form.fuwu_fee_extra, 'elevator_fee', times(item.elevator_fee))
          }
          this.$set(this.form.fuwu_fee_extra, item.type, times(item.price))
          console.log("validataPlateAmountPrice");
          if (item && !item.price) {
            this.$message.error("请输入" + item.name + "的服务费")
            result = false
            break
          }
          if (item && !isPrice(item.price)) {
            this.$message.error(item.name + "服务费输入金额有误")
            result = false
            break
          }
          if (item.type === 'waimai' && !isPrice(item.elevator_fee)) {
            this.$message.error(item.name + "电梯服务费输入金额有误")
            result = false
            break
          }
        }
      }
      return result
    },

    async saveForm() {
      this.$refs.form.validate(async valid => {
        if (valid) {
          if (!this.checkForm()) {
            return
          }
          let data = deepClone({ ...this.form })
          const feeField = [
            'waimai_fee',
            'fuwu_fee',
            'breakfast_fixed',
            'lunch_fixed',
            'hit_tea_fixed',
            'dinner_fixed',
            'midnight_fixed',
            'early_fixed',
            'cupboard_delivery_fee',
            'waimai_delivery_fee'
          ]
          feeField.forEach(f => {
            data[f] = times(data[f])
          })
          // 取餐柜打包费
          const extraFeeField = ['cupboard_takeaway_fee']
          extraFeeField.forEach(f => {
            data.extra[f] = times(this.form.extra[f])
          })
          // data.extra.cupboard_takeaway_fee = times(data.extra.cupboard_takeaway_fee)
          data.organization = this.$store.getters.organization
          data.take_out_type = JSON.stringify(data.take_out_type)

          // if (this.form.can_refund) {
          //   data.refund_remit_type = this.form.pay_after !== '' ? 'pay_after' : 'meal_before'
          //   data.refund_remit_time = this.form.pay_after !== '' ? data.pay_after : data.meal_before
          // }

          switch (data.refund_remit_type) {
            case 'pay_after':
              data.refund_remit_time = data.pay_after
              break;
            case 'meal_before':
              data.refund_remit_time = data.meal_before
              break;
            case 'day_hour_before':
              break;
          }
          if (!data.can_refund) {
            data.cancel_review = false
          }

          if (!data.is_limit) {
            data.buy_limit_count = 0
          }
          if (data.applied_to_visitor) {
            data.applied_to_visitor = 1
          } else {
            data.applied_to_visitor = 0
          }
          if (!data.is_limit_food) {
            data.buy_limit_food_count = 0
          }
          if (data.today_close_time) {
            let timeOff = data.today_close_time.split(':')
            data.today_close_time = (Number(timeOff[0]) * 60 * 60) + (Number(timeOff[1]) * 60)
            //  + Number(timeOff[2])
          } else {
            data.today_close_time = 0
          }
          if (this.todayCloseTime && this.todayCloseTime[0] && this.todayCloseTime[1]) {
            let timeOff = this.todayCloseTime[0].split(':')
            data.extra.today_close_time_start = timeOff[0] * 60 * 60 + timeOff[1] * 60
            let timeOn = this.todayCloseTime[1].split(':')
            data.extra.today_close_time_end = timeOn[0] * 60 * 60 + timeOn[1] * 60
          }
          if (this.form.isOpenDingingTimes && this.form.dinging_times.length) {
            data.dinging_times = this.form.dinging_times.map(v => {
              let list = v.dinging_time.map(item => {
                console.log(item)
                return {
                  start_time: item.start_time,
                  end_time: item.end_time,
                  order_count: item.order_count,
                  enable: item.enable
                }
              })
              return {
                meal_type: v.meal_type,
                dinging_time: list
              }
            })
          } else {
            data.dinging_times = []
          }
          let res
          data = deleteEmptyKey(data)
          if (this.isAdd) {
            res = await this.$apis.apiBackgroundReservationBackgroundReservationSettingsAddPost(
              data
            )
          } else {
            const id = JSON.parse(this.$route.query.data)?.id
            res = await this.$apis.apiBackgroundReservationBackgroundReservationSettingsModifyPost({
              id,
              ...data
            })
          }

          if (res.code === 0) {
            this.$message.success(`${this.isAdd ? '新增' : '修改'}成功！`)
            this.$router.push({
              name: 'MerchantBookingMeal'
            })
          } else {
            this.$message.error(res.msg)
          }
        } else {
          this.$message.error('请检查表单必填信息！')
          return false
        }
      })
    },
    setOrganizationName() {
      if (this.userInfo && !this.userInfo.organizationList) return
      this.userInfo.organizationList.forEach(item => {
        if (this.organizationId === item.id) {
          this.organizationName = item.name
        }
      });
    },
    // 获取是否支持游客
    async getOrgVisitor() {
      const res = await this.$apis.apiBackgroundOrganizationOrganizationGetIsRsvExemptEnrollPost()
      if (res.code === 0) {
        this.showVisitor = res.data.is_rsv_exempt_enroll
      } else {
        this.$message.error(res.msg)
      }
    },
    // 跳过时间格式化
    formatNoSkipTime(date) {
      // let text = date.split('-')[0] + '月' + date.split('-')[1] + '日'
      let result = date.replace(/-/g, (result, key) => {
        let text = ''
        switch (key) {
          case 4:
            text = '年'
            break
          case 7:
            text = '月'
            break
        }
        return text
      })
      return result + '日'
    },
    // 单个删除
    delNoSkipTime(index) {
      this.form.extra.no_skip_days.splice(index, 1)
    },
    // 清除全部
    clearNoShipDay() {
      this.form.extra.no_skip_days = []
    },
    // 单个删除
    delSkipTime(index) {
      this.form.extra.forbidden_date.splice(index, 1)
    },
    // 清除全部
    clearShipDay() {
      this.form.extra.forbidden_date = []
    },
    // 初始化餐段keys,后面要用
    initMealTypeKeys() {
      this.mealType.forEach(v => {
        this.mealTypeKeys[v.value] = {
          label: v.label,
          value: v.value
        }
      })
    },
    // 获取常规设置
    async getCommonSettings() {
      // await this.$sleep(1000);
      this.isLoading = true
      let params = {
        id: this.organization
      }
      const [err, res] = await this.$to(this.$apis.apiBackgroundOrganizationOrganizationGetCommonSettingsPost(params));
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.initSettingData(res.data)
        // console.log(res.data)
      } else {
        // this.$message.error(res.msg)
      }
    },
    // 格式化显示的数据
    initSettingData(data) {
      // 初始化时间
      // 时间范围start_time~end_time，start_time 最小时间值，end_time最大时间值
      // 当时间跨天时，时间范围为00:00~23:59
      let time = data.meal_time_settings
      if (!time) { // 如果自己没有开启则继承父级的
        time = data.parent_meal_time_settings
      }
      console.log(22, time)
      if (time) {
        for (const key in this.mealTypeKeys) {
          let meal = this.mealTypeKeys[key];
          let start = `${time[key + '_start']}`.substring(0, 5) // 取时分
          let end = `${time[key + '_end']}`.substring(0, 5) // 取时分
          // 当start大于end时，开始时间大于结束时间即代表当前餐段跨天
          if (Number(start.replace(/:/g, '')) > Number(end.replace(/:/g, ''))) {
            this.$set(meal, 'start_time', '00:00')
            this.$set(meal, 'end_time', '23:59')
            // 保存下原来的时间范围
            this.$set(meal, 'across_day', true) // 是否跨天用个字段来存下吧
            this.$set(meal, 'start', start)
            this.$set(meal, 'end', end)
          } else {
            this.$set(meal, 'start_time', start)
            this.$set(meal, 'end_time', end)
            this.$set(meal, 'options', end)
          }

          this.$set(meal, 'enable', !!time['enable_' + key])
        }
      }
      console.log(111112, this.mealTypeKeys)
    },
    changeMealTypes(meal) {
      console.log(meal)
      let dingingTypes = this.form.dinging_times.map(v => {
        return v.meal_type
      })
      // 添加
      meal.forEach((item, i) => {
        // 餐段开启了才能添加
        if (!dingingTypes.includes(item) && this.mealTypeKeys[item].enable) {
          let list = {
            meal_type: item,
            meal_name: this.mealTypeKeys[item].label,
            dinging_time: [
              // { enable: false, start_time: '', end_time: '', order_count: '', error: '' }
            ]
          }
          this.form.dinging_times.splice(i, 0, list)
        }
      })
      // 删除
      for (let index = 0; index < this.form.dinging_times.length; index++) {
        const item = this.form.dinging_times[index];
        if (!meal.includes(item.meal_type)) {
          this.form.dinging_times.splice(index, 1)
        }
      }
      // 排序
      this.sortMealDinging()
    },
    // 用餐餐段排序
    sortMealDinging() {
      // 用于餐段排序的
      const mealTypeIndex = { breakfast: 1, lunch: 2, afternoon: 3, dinner: 4, supper: 5, morning: 6 }
      this.form.dinging_times.sort((a, b) => {
        return mealTypeIndex[a.meal_type] - mealTypeIndex[b.meal_type]
      })
    },
    // 添加时间规则
    addDingingTime(meal) {
      meal.dinging_time.push({ enable: false, start_time: '', end_time: '', order_count: '', error: '' })
    },
    // 删除规则
    deleteDingingTime(meal, i) {
      meal.dinging_time.splice(i, 1)
    },
    // 检查单个餐段的用餐时间是否重复
    checkMealDingingTime(data, dinging) {
      // let req = /:/g
      // 时间只用时分，这里做了处理删调了中间的:
      if (this.disabledDinging) {
        // 是否跨天
        let isAcrossDay = !!this.mealTypeKeys[data.meal_type].across_day
        let range = []
        let isPass = true
        if (isAcrossDay) {
          range = [this.repeatTime(this.mealTypeKeys[data.meal_type].start), this.repeatTime(this.mealTypeKeys[data.meal_type].end)]
          if (dinging.start_time && dinging.end_time) {
            let startTime = this.repeatTime(dinging.start_time)
            let endTime = this.repeatTime(dinging.end_time)
            // 跨天需要判断下时间范围
            // 开始时间非时间范围内
            if (range[1] < startTime && startTime < range[0]) {
              dinging.error = `${data.meal_name}的时间范围在${this.mealTypeKeys[data.meal_type].start}~${this.mealTypeKeys[data.meal_type].end}`
              isPass = false
              return
            }
            // 结束时间非时间范围内
            if (range[1] < endTime && endTime < range[0]) {
              dinging.error = `${data.meal_name}的时间范围在${this.mealTypeKeys[data.meal_type].start}~${this.mealTypeKeys[data.meal_type].end}`
              isPass = false
              return
            }
            // 如果跨天的时间都大于或都小于23：59则需要判断时间开始时间要大于结束时间
            if ((startTime >= 0 && startTime <= range[1]) && (endTime >= 0 && endTime <= range[1])) {
              if (startTime > endTime) {
                this.$set(dinging, 'error', '开始时间不能大于结束时间！')
                isPass = false
                return
              }
            }
            if ((startTime <= 2359 && startTime >= range[0]) && (endTime <= 2359 && endTime >= range[0])) {
              if (startTime > endTime) {
                this.$set(dinging, 'error', '开始时间不能大于结束时间！')
                isPass = false
                return
              }
            }
          }
        } else {
          range = [this.repeatTime(this.mealTypeKeys[data.meal_type].start_time), this.repeatTime(this.mealTypeKeys[data.meal_type].end_time)]
          if (dinging.start_time && dinging.end_time && this.repeatTime(dinging.start_time) > this.repeatTime(dinging.end_time)) {
            this.$set(dinging, 'error', '开始时间不能大于结束时间！')
            return
          }
        }

        // 基础校验
        if (dinging) {
          // 时间范围没填的error提示
          if (dinging.start_time && dinging.end_time) this.$set(dinging, 'error', '')
          if (!dinging.start_time) this.$set(dinging, 'error', '请选择开始时间')
          if (!dinging.end_time) this.$set(dinging, 'error', '请选择结束时间')
          if (!dinging.start_time && !dinging.end_time) this.$set(dinging, 'error', '请选择开始和结束时间')
        }
        if (!isPass) return
        let timesList = [] // 选择的时间，处理下
        data.dinging_time.forEach((v, i) => {
          if (v.start_time && v.end_time) {
            // 添加需要校验的时间
            let currentTime = {
              start_time: this.repeatTime(v.start_time),
              end_time: this.repeatTime(v.end_time)
            }
            timesList.push(currentTime)
            // 时间重叠校验
            data.dinging_time.forEach((item, index) => {
              if (i < index && item.start_time && item.end_time) {
                this.$set(item, 'error', '')
                let nextTime = {
                  start_time: this.repeatTime(item.start_time),
                  end_time: this.repeatTime(item.end_time)
                }
                let isCompare = this.compareWidthTime(currentTime, nextTime, isAcrossDay, range)
                if (isCompare) {
                  this.$set(item, 'error', '餐段时间重叠，请检查！')
                }
              }
            })
          }
        })
      }
    },
    repeatTime(time) {
      const req = /:/g
      return time ? time.replace(req, '') : ''
    },
    // 时间对比
    // 时间time1, time2，isAcrossDay是否跨天，ranges时间范围
    compareWidthTime(timeA, timeB, isAcrossDay, range) {
      // true重叠，false不重叠
      let status = false
      if (!isAcrossDay) { // 餐段设置不跨天
        status = this.checkTimeOverlap([timeA.start_time, timeA.end_time], [timeB.start_time, timeB.end_time])
      } else { // 餐段设置跨天
        if (timeA.start_time > timeA.end_time) { // timeA是跨天的时间
          if (timeB.start_time > timeB.end_time) { // timeB是跨天的时间，当2个时间段都是跨天必定是重叠的
            status = true
          } else { // 拆分求是否重叠吧
            if (this.checkTimeOverlap([timeA.start_time, '2359'], [timeB.start_time, timeB.end_time])) {
              status = true
            }
            if (this.checkTimeOverlap(['0000', timeA.end_time], [timeB.start_time, timeB.end_time])) {
              status = true
            }
          }
        } else { // timeA是不跨天的时间
          if (timeB.start_time > timeB.end_time) { // timeB是跨天的时间
            if (this.checkTimeOverlap([timeB.start_time, '2359'], [timeA.start_time, timeA.end_time])) {
              status = true
            }
            if (this.checkTimeOverlap(['0000', timeB.end_time], [timeA.start_time, timeA.end_time])) {
              status = true
            }
          } else {
            status = this.checkTimeOverlap([timeA.start_time, timeA.end_time], [timeB.start_time, timeB.end_time])
          }
        }
      }
      return status
    },
    // 判断时间范围是否重叠, true重叠，false不重叠
    checkTimeOverlap (timeA, timeB) {
      const max = [timeA[0], timeB[0]];
      const min = [timeA[1], timeB[1]];
      return (Math.max.apply(null, max) <= Math.min.apply(null, min))
    },
    /***
     * 选择服务费变化
     */
    serveCheckChange(e) {
      console.log("serveCheckChange", e);
      var chooseItem = e.toString()
      this.serveFeeList.forEach(item => {
        if (chooseItem.indexOf(item.type) !== -1) {
          item.disabled = false
        } else {
          item.disabled = true
          item.price = '' // 反选记得清除金额
          if (item.type === 'waimai' && chooseItem.indexOf('waimai') === -1) {
            item.elevator_fee = '' // 反选记得清除金额
          }
        }
      })
    },
    /**
     * 编辑进来设置服务费回显
     */
    setServeCheck(checkData) {
      console.log("checkList", checkData, typeof checkData);

      if (typeof checkData === "object" && Object.keys(checkData).length > 0) {
        var chooseList = []
        this.serveFeeList = this.serveFeeList.map(item => {
          if (Reflect.has(checkData, item.type)) {
            item.price = divide(checkData[item.type])
            if (item.type === 'waimai') {
              item.elevator_fee = divide(checkData.elevator_fee)
            }
            item.disabled = false
            chooseList.push(item.type)
          }
          return item
        })
        this.chooseServe = chooseList
      }
    }
  }
}
</script>

<style  lang="scss">
.page {
  box-sizing: border-box;
  margin-top: 20px;
  padding: 20px;
  border-radius: 12px;
  background-color: #fff;

  .title {
    display: block;
    margin: 15px 0;
    padding: 0 10px;
    color: #23282d;
    font-size: 18px;
    border-left: 5px #ff9b45 solid;
  }
  table {
    .min-w-60{
      min-width: 60px;
    }
    // tr,
    background: #ddd;
    td,
    th {
      background: #fff;
      // border: 1px solid #ddd;
    }
  }
  .w-150{
    width: 150px;
  }
  .m-l-10{
    margin-left: 10px;
  }
  .dinging-box{
    display: flex;
    flex-wrap:wrap;
    align-items: center;
  }
}
.tips {
  display: block;
  margin: -6px 0 12px 36px;
  color: #f41818;
  font-size: 12px;
}

.tail-unit {
  margin-left: 12px;
}

.fake-table-wrapper {
  display: flex;
  align-items: flex-end;
  .fake-table {
    display: flex;
    width: 600px;
    border: 1px solid #ddd;
    .fake-col {
      display: flex;
      align-items: center;
      flex-direction: column;
      width: 100px;
      border-right: 1px solid #ddd;

      .fake-col-title {
        display: block;
        width: 100%;
        border-bottom: 1px solid #ddd;
        text-align: center;
      }
    }
  }
}
.inline-box{
  display: inline-block;
  margin-left: 15px;
  margin-right: 15px;
}

.inline-form{
  display: inline-block;
  .el-form-item__content{
    display: inline-block;
  }
}

.switch-wrapper {
  display: flex;
  align-items: center;
  margin-right: 16px;

  .label {
    margin-right: 8px;
  }
}
.block-label{
  width: 100%;
  .el-form-item__label{
    display: block;
    // text-align: left;
    line-height: 1.5;
    float: none;
  }
}
.no-ship {
  padding: 10px 0;
  .no-ship-time {
    display: inline-block;
    margin-right: 30px;
    margin-bottom: 5px;
    font-size: 15px;
    position: relative;
    .del-time {
      position: absolute;
      top: -8px;
      right: -12px;
      cursor: pointer;
    }
  }
}
.hidden-picker {
  position: relative;
  overflow: hidden;
  .el-date-editor {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    .el-input__inner {
      padding: 0 !important;
    }
    opacity: 0;
  }
}

</style>
