<template>
  <div class="ApproveRules container-wrapper MealPackageDetail">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form ref="searchRef" :loading="isLoading" label-width="105px" @search="searchHandle" @reset="searchReset" :form-setting="searchFormSetting" :autoSearch="false"></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon
            color="origin"
            type="Import"
            @click="openImport()"
          >
            导入
          </button-icon>
          <button-icon
            color="origin"
            type="export"
            @click="handleExport()"
            v-permission="['background_order.order_report_meal.order_report_meal_pack_list_export']"
          >
            导出Excel
          </button-icon>
        </div>
      </div>
      <!-- table-content start -->
      <div class="table-content">
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
        >
          <table-column v-for="item in tableSetting" :key="item.key" :col="item">
            <template #operation="{ row }">
              <!-- ORDER_SUCCESS 已成功的已购买能查看
              ORDER_PAYING 未购买
              ORDER_REFUND_SUCCESS 已退款 -->
              <el-button :disabled="row.order_status !== 'ORDER_SUCCESS'" type="text" size="small" class="ps-text" @click="showDialogHandle(row)">查看</el-button>
            </template>
          </table-column>
        </el-table>
      </div>
      <!-- table content end -->
      <table-statistics v-loading="isLoadingCollect" element-loading-custom-class="el-loading-wrapp"  element-loading-spinner="loading" :element-loading-text="elementLoadingText" :statistics="collect" />

      <!-- 分页 start -->
      <pagination
        :onPaginationChange="onPaginationChange"
        :current-page.sync="currentPage"
        :page-size.sync="pageSize"
        :layout="'total, prev, pager, next, jumper'"
        :total="totalCount"
      ></pagination>
      <!-- 分页 end -->
    </div>
    <MealPackageDetailDialog :isshow.sync="showDialog" :info-data="dialogInfo" />
    <import-page-drawer
      v-if="showImportDialog"
      :width="'800px'"
      :show.sync="showImportDialog"
      :header-len="importHeaderLen"
      :templateUrl="importTemplateUrl"
      :url="importApi"
      :paramsData="importParamsData"
      :is-delete-first="true"
      ></import-page-drawer>
  </div>
</template>

<script>
import { debounce, deepClone, getRequestParams } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import report from '@/mixins/report' // 混入
import { MEAL_PACKAGE_SEARCH, MEAL_PACKAGE_TABLE } from './constant'
import { type } from '@/utils/type'
import MealPackageDetailDialog from './component/MealPackageDetailDialog'

export default {
  name: 'MealPackageDetail',
  components: { MealPackageDetailDialog },
  mixins: [exportExcel],
  data() {
    return {
      isLoading: false,
      tableData: [],
      tableSetting: MEAL_PACKAGE_TABLE,
      searchFormSetting: {},
      selectListId: [],
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      collect: [ // 统计
        { key: 'buy_count', value: 0, label: '已购买：', dot: true },
        { key: 'not_buy_count', value: 0, label: '未购买：', dot: true },
        { key: 'refund_count', value: 0, label: '已退款：' }
      ],
      elementLoadingText: "数据正在加载，请耐心等待...",
      isLoadingCollect: false,
      showDialog: false,
      dialogInfo: {},
      saveReportMealPack_settingsId: '', // 保存默认餐包名称 重置用
      importDialogTitle: '',
      showImportDialog: false,
      importTemplateUrl: location.origin + '/api/temporary/template_excel/报餐模版/餐包导入购买.xlsx',
      importHeaderLen: 1,
      importApi: 'apiBackgroundOrderOrderReportMealBatchImportReportMealPackPost',
      importParamsData: {
      }
    }
  },
  mounted() {
    this.initLoad()
  },
  methods: {
    async initLoad() {
      this.searchFormSetting = deepClone(MEAL_PACKAGE_SEARCH)
      await this.getMealPackageRuleList()
      // 产品要求优化不要初始加载列表
      // this.getMealPackageReportList()
      this.getMealPackageReportCountList()
    },
    // 刷新页面
    refreshHandle() {
      this.$refs.searchRef.resetForm()
      this.currentPage = 1;
      this.tableData = []
      this.initLoad()
    },
    // 节下流咯
    searchHandle: debounce(function(e) {
      if (e && e === 'search') {
        this.currentPage = 1;
        this.getMealPackageReportList()
        this.getMealPackageReportCountList()
      }
    }, 300),
    // 重置要默认选中餐包名称
    searchReset() {
      this.searchFormSetting.report_meal_pack_settings_id.value = this.saveReportMealPack_settingsId
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key.indexOf('time') < 0) {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    async getMealPackageRuleList() {
      const [err, res] = await this.$to(this.$apis.apiBackgroundReportMealReportMealPackSettingsListPost({
        page: 1,
        page_size: 999999,
        status: ["enable", "delete"]
      }))
      if (err) {
        return this.$message.error(err.message)
      }
      if (res.code === 0) {
        this.searchFormSetting.report_meal_pack_settings_id.dataList = res.data.results
        if (res.data.results.length > 0) {
          this.saveReportMealPack_settingsId = res.data.results[0].id
          this.searchFormSetting.report_meal_pack_settings_id.value = res.data.results[0].id
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    // 餐包统计数据
    async getMealPackageReportList() {
      this.isLoading = true
      const [err, res] = await this.$to(this.$apis.apiBackgroundOrderOrderReportMealOrderReportMealPackListPost({
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      }))
      this.isLoading = false
      if (err) {
        return this.$message.error(err.message)
      }
      if (res.code === 0) {
        this.tableData = res.data.results.map(v => {
          if (type(v.card_user_group_alias) === 'array') {
            v.card_user_group_alias = v.card_user_group_alias.join('，')
          }
          return v
        })
        this.totalCount = res.data.count
      } else {
        this.$message.error(res.msg)
      }
    },
    // 餐包统计数据
    async getMealPackageReportCountList() {
      const [err, res] = await this.$to(this.$apis.apiBackgroundOrderOrderReportMealOrderReportMealPackCountPost({
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      }))
      if (err) {
        return this.$message.error(err.message)
      }
      if (res.code === 0) {
        this.collect.forEach(item => {
          for (let i in res.data) {
            if (item.key === i) {
              item.value = res.data[i]
            }
          }
        })
      } else {
        this.$message.error(res.msg)
      }
    },
    // 分页页数change事件
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getMealPackageReportList()
      this.getMealPackageReportCountList()
    },
    // 导出报表
    handleExport() {
      let params = {
        ...this.formatQueryParams(this.searchFormSetting),
        page: 1,
        page_size: this.totalCount
      }
      const option = {
        type: 'MealPackageDetail',
        url: 'apiBackgroundOrderOrderReportMealOrderReportMealPackListExportPost',
        params: params
      }
      this.exportHandle(option)
    },
    showDialogHandle(row) {
      this.dialogInfo = row
      this.showDialog = true
    },
    // 导入
    openImport(type) {
      this.importDialogTitle = '批量导入'
      this.showImportDialog = true
    }
  }
}
</script>
<style lang="scss" scoped>
.MealPackageDetail{
  .tips{
    color: #ff9b45;
    font-weight: bold;
    font-size: 16px;
    margin-top: 15px;
  }
}
</style>
