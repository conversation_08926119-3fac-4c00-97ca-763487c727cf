<template>
  <div class="" v-loading="isLoading">
    <div class="box-header-week">
      <span>本周菜谱（{{ dateRangeString }}）</span>
      <div class="meal-type">
        <!-- <div v-for="cate in categoryData" :key="cate.name">
            <div class="marker-meat" :style="{backgroundColor: cate.color}"></div>
            <span style="margin-right: 20px">{{ cate.name }}</span>
          </div> -->
        <div>
          <!-- 暂时隐藏，不要删，这功能产品说等下次优化 -->
          <!-- <span>
            <el-popover placement="left" title="营养指导说明" width="300" trigger="hover">
              <div>
                <div>
                  营养指导是由朴食智能AI营养模型根据《中国居民膳食指南2022版》等作为指导，中国营养学会权威推荐，配合营养指导制定的营养规则，创建菜谱过程中，系统会根据菜谱对应的人群画像指导菜品配置过程，来满足大多数个体或集体人群的营养建议和风险预警，帮助人群合理搭配营养膳食
                </div>
                <div>1.能量摄入：中国居民膳食能量需要量。</div>
                <div>
                  2.三大营养素：指蛋白质、脂肪、碳水化合人体三大主要必需的营养素，具有重要的生理作用。
                </div>
                <div>
                  3.食物多样性：指一日三餐膳食的食物种类全、品样多，是平衡膳食的基础，具体包含谷类、鱼禽蛋肉、蔬菜、水果等四大类
                </div>
              </div>
              <i slot="reference" class="el-icon-question" style="color:#ff9b45;"></i>
            </el-popover>
            营养指导
            <el-switch
              v-model="isNutritionGuidance"
              active-color="#ff9b45"
              inactive-color="#ffcda2"
              @change="changeGuidance"
            ></el-switch>
          </span> -->
        </div>
      </div>
    </div>
    <!-- 营养指导指示点 -->
    <div class="header-wrapper" v-if="isNutritionGuidance">
      <div class="marker-wrapper">
        <div v-for="item in markerList" :key="item.label">
          <span :class="item.className"></span>
          <span style="margin-right: 10px">{{ item.label }}</span>
        </div>
      </div>
    </div>
    <el-table :data="tableData" border v-loading="isLoading">
      <el-table-column
        v-for="item in columns"
        :label="item.title"
        :key="item.column"
        :prop="item.column"
      >
        <template slot-scope="scope" slot="header">
          <div style="position: relative">
            <div>{{ columns[scope.$index]['title'] }}</div>
            <span>{{ columns[scope.$index]['date'] }}</span>
            <el-link
              :underline="false"
              @click="handleCopy(columns[scope.$index]['title'], columns[scope.$index]['date'])"
              v-if="columns[scope.$index]['date']"
              class="copy-text"
            >
              <i class="el-icon-document-copy"></i>
              复制
            </el-link>
          </div>
        </template>

        <template slot-scope="scope">
          <span v-if="scope.column.property === 'meal_type'">{{ scope.row.meal_type }}</span>
          <div v-else-if="isNutritionGuidance && scope.$index === 6">
            <div>
              <!-- scope.row.data[idx] -->
              <span class="marker-box" style="background-color:#f59a23;"></span>
              <span>不足：</span>
              <span>
                {{
                  scope.row.data[weekLabelList.findIndex(d => d === scope.column.label)]
                    .insufficientTotal
                }}
              </span>
            </div>
            <div>
              <span class="marker-box" style="background-color:#5dbf6e;"></span>
              <span>适宜：</span>
              <span>
                {{
                  scope.row.data[weekLabelList.findIndex(d => d === scope.column.label)]
                    .suitableTotal
                }}
              </span>
            </div>
            <div>
              <span class="marker-box" style="background-color:#d9001c;"></span>
              <span>过量：</span>
              {{
                scope.row.data[weekLabelList.findIndex(d => d === scope.column.label)].overdoseTotal
              }}
            </div>
          </div>
          <div v-else>
            <div v-for="(wl, idx) in weekLabelList" :key="wl">
              <div
                v-if="scope.column.label === wl"
                class="meal-cell-wrapper"
                :class="{
                  'meal-cell-wrapper-bg': !(
                    scope.row.data[idx] &&
                    (scope.row.data[idx].food_count || scope.row.data[idx].set_meal_count)
                  )
                }"
                style="cursor: pointer;"
                @click="handleMealEdit(scope.row.data[idx], scope.column.property, scope.row.field)"
              >
              <!-- :title="
                      `共${scope.row.data[idx].food_data.length}个菜品+${scope.row.data[idx].set_meal_data.length}个套餐`
                    " -->
                <div class="operate">
                  <div
                    v-if="
                      scope.row.data[idx] &&
                      (scope.row.data[idx].food_count || scope.row.data[idx].set_meal_count)
                    "
                  >
                    <!-- <div class="popover-wrapper">
                      <div
                        v-for="(foodItem, foodIndex) in scope.row.data[idx].food_data"
                        :key="foodIndex"
                      >
                        <div class="p-b-15">
                          <span class="m-r-20" style="color:#169bd5">
                            {{ foodItem.food_name }}({{ foodItem.weight }}g)
                          </span>
                          <span style="color:#f59a23">
                            {{ foodItem.main_nutrition.energy_kcal }}kcal/
                            {{ foodItem.ingredient_category_count + '类' }}
                          </span>
                        </div>
                        <div class="p-b-15">
                          <span class="p-r-10">脂肪{{ foodItem.main_nutrition.axunge }}g</span>
                          <span class="p-r-10">蛋白质{{ foodItem.main_nutrition.protein }}g</span>
                          <span>碳水{{ foodItem.main_nutrition.carbohydrate }}g</span>
                        </div>
                      </div>
                    </div> -->
                    <span style="cursor: pointer" slot="reference">
                      <!-- 编辑 -->
                      已配餐（{{
                        scope.row.data[idx].food_count + scope.row.data[idx].set_meal_count
                      }}）
                    </span>
                  </div>

                  <span v-else class="copy">未配餐</span>
                  <span
                    v-if="
                      scope.row.data[idx] &&
                      (scope.row.data[idx].food_count || scope.row.data[idx].set_meal_count)
                    "
                    class="edit"
                    @click.stop="handleMealCopy(scope.row.data[idx].id)"
                  >
                    复制
                  </span>
                </div>
                <!-- 只有开启预约指导 和早午晚餐才有营养分析 -->
                <div
                  v-if="
                    isNutritionGuidance &&
                      (scope.row.data[idx].meal_type === 'breakfast' ||
                        scope.row.data[idx].meal_type === 'lunch' ||
                        scope.row.data[idx].meal_type === 'dinner') &&
                      scope.row.data[idx] &&
                      scope.row.data[idx].food_count
                  "
                >
                  <div class="progress-box">
                    <span class="progress-title">能量摄入</span>
                    <el-progress
                      class="progress-content"
                      :show-text="false"
                      :color="percentageColor(scope.row.data[idx].total_nutrition.energy_kcal)"
                      :percentage="percentageTotal(scope.row.data[idx].total_nutrition.energy_kcal)"
                    ></el-progress>
                  </div>
                  <div class="progress-box">
                    <span class="progress-title">三大营养元素</span>
                    <el-progress
                      class="progress-content"
                      :show-text="false"
                      :color="
                        percentageColor(
                          scope.row.data[idx].total_nutrition.total_axunge_protein_carbohydrate
                        )
                      "
                      :percentage="
                        percentageTotal(
                          scope.row.data[idx].total_nutrition.total_axunge_protein_carbohydrate
                        )
                      "
                    ></el-progress>
                  </div>
                  <div class="progress-box">
                    <span class="progress-title">食物多样性</span>
                    <el-progress
                      class="progress-content"
                      :show-text="false"
                      :color="
                        percentageColor(
                          scope.row.data[idx].total_nutrition
                            .total_cereals_eggsandmeat_fruit_vegetable
                        )
                      "
                      :percentage="
                        percentageTotal(
                          scope.row.data[idx].total_nutrition
                            .total_cereals_eggsandmeat_fruit_vegetable
                        )
                      "
                    ></el-progress>
                  </div>
                </div>
                <!-- <div
                    v-for="item in scope.row.data[idx] ? scope.row.data[idx].food_data : []"
                    :key="item.id"
                    class="meal-item"
                  >
                    <div class="marker" :style="{ backgroundColor: item.color }"></div>
                    <span class="title">{{ item.food_name }}（{{ item.weight }}g）</span>
                    <div>
                      （
                      <span>成本价：¥ {{ item.origin_price / 100 }}</span>
                      &nbsp;&nbsp;
                      <span>菜品价格：¥ {{ item.food_price / 100 }}</span>
                      ）
                    </div>
                    （
                    <span v-for="i in item.ingredients" :key="i.ingredient_name">
                      {{ i.ingredient_name }} {{ i.weight }}g
                    </span>
                    ）
                  </div> -->
              </div>
            </div>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <!-- 复制到本周的哪一天 -->
    <el-dialog
      title="复制到"
      :visible.sync="showCopyDialog"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      :destroy-on-close="true"
      width="600px"
    >
      <p>
        <span style="margin-right: 48px">已选：{{ selectedDate }}</span>
      </p>
      复制到：
      <div style="padding: 0 20px">
        <el-checkbox-group v-model="checkList">
          <el-checkbox
            v-for="week in weekLabelList"
            :disabled="selectedDate === week"
            :label="week"
            :key="week"
            style="margin-top: 10px"
          ></el-checkbox>
        </el-checkbox-group>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="showCopyDialog = false">取 消</el-button>
        <el-button size="small" type="primary" @click="confirmCopy">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 复制某一餐 -->
    <div v-if="showCopyMealDialog">
      <el-dialog
        title="复制到"
        :visible.sync="showCopyMealDialog"
        :close-on-press-escape="false"
        :close-on-click-modal="false"
        :destroy-on-close="true"
        width="600px"
      >
        <p>
          <span style="margin-right: 48px">已选：{{ selectedDate }}</span>
        </p>
        复制到：
        <div style="padding: 0 20px">
          <div v-for="(week, idx) in weekLabelList" :key="week" style="margin-top: 12px">
            <el-checkbox v-model="mealData[idx].selected">{{ week }}</el-checkbox>
            <el-select size="mini" style="margin-left: 20px" v-model="mealData[idx].meal_type">
              <el-option
                v-for="item in mealTypeList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </div>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button size="small" @click="cancelMealCopyHandler">取 消</el-button>
          <el-button size="small" type="primary" @click="confirmCopyMeal">确 定</el-button>
        </span>
      </el-dialog>
    </div>

    <!-- 编辑菜品 -->
    <!-- <div v-if="editMealFoodsDialog">
      <el-dialog
        title="编辑菜单"
        :visible.sync="editMealFoodsDialog"
        :close-on-press-escape="false"
        :close-on-click-modal="false"
        :destroy-on-close="true"
        width="1200px"
      >
        <edit-meal-foods
          :currentEditData="currentEditData"
          ref="editMealFoodsRef"
          :currentEditDate="currentEditDate"
          :mealType="currentEditMealType"
        />
        <span slot="footer" class="dialog-footer">
          <el-button size="small" @click="cancelMealEditHandler">取 消</el-button>
          <el-button size="small" type="primary" @click="confirmEditMeal">确 定</el-button>
        </span>
      </el-dialog>
    </div> -->
    <select-model-dialog
      v-if="selectModelDialogVisible"
      :isshow.sync="selectModelDialogVisible"
      :formDataDialog="selectModelDialogInfo"
      @clickSelect="clickSelect"
      width="900px"
      ref="selectModelDialog"
    />
  </div>
</template>

<script>
import * as dayjs from 'dayjs'
import { MEAL_TYPES } from '@/utils/constants'
import selectModelDialog from '../components/menu/selectModelDialog'
import { deepClone, to } from '@/utils'
// import EditMealFoods from '../components/editMealFoods'
export default {
  name: '',
  mounted() {
    this.initLoad()
  },
  props: {
    menuType: {
      type: String,
      default: ''
    },
    menuId: {
      type: String,
      default: ''
    }
    // isNutritionGuidance: {
    //   type: Boolean,
    //   default: false
    // }
    // formDataDialog: {
    //   type: Object,
    //   default() {
    //     return {}
    //   }
    // }
  },
  components: {
    selectModelDialog
    // EditMealFoods
  },
  data() {
    return {
      isLoading: false,
      copyDate: '',
      currentCopyId: null,
      // currentEditDate: null,
      // currentEditData: null,
      // currentEditMealType: null,
      mealData: [], // { value: 2, selected: false, meal_type: undefined },
      mealTypeList: [
        {
          label: '全部',
          value: 'all'
        },
        ...MEAL_TYPES
      ],
      // editMealFoodsDialog: false,
      showCopyDialog: false,
      showCopyMealDialog: false,
      selectedDate: '',
      checkList: [],
      weekLabelList: ['星期一', '星期二', '星期三', '星期四', '星期五', '星期六', '星期日'],
      dateRangeString: '',
      columns: [],
      tableData: [],
      // guidance: false,
      isNutritionGuidance: false,
      // 开始了 营养指导
      markerList: [
        {
          className: 'marker-primary',
          label: '不足'
        },
        {
          className: 'marker-secondary',
          label: '适宜'
        },
        {
          className: 'marker-thridary',
          label: '过量'
        }
      ],
      dialogMenuPeviewInfo: {}, // 菜谱预览
      selectModelDialogVisible: false,
      selectModelDialogInfo: {}
    }
  },
  methods: {
    initLoad() {
      // 营养分析 是否开启
      // this.isNutritionGuidance = this.isNutritionGuidance
      this.initDateRangeString()
      this.requestDetailData()
    },
    // 确定复制整天数据
    async confirmCopy() {
      const weekdays = []
      this.checkList.forEach(d => {
        const idx = this.weekLabelList.findIndex(w => w === d)
        weekdays.push(idx + 1)
      })
      if (!weekdays.length) {
        return this.$message.error('请选择日期')
      }
      const param = {
        copy_date: this.copyDate
          .replace('年', '-')
          .replace('月', '-')
          .replace('日', ''),
        id: this.$route.query.id,
        weekdays
      }
      const res = await this.$apis.apiBackgroundFoodMenuWeeklyCopyDayFoodPost(param)
      if (res.code === 0) {
        this.$message.success('操作成功')
        this.showCopyDialog = false
        this.initLoad()
      } else {
        this.$message.error(res.msg)
      }
    },

    // 关闭窗口，数据回到默认值
    cancelMealCopyHandler() {
      const mealDataList = []
      for (let i = 1; i < 8; i++) {
        mealDataList.push({ value: i, selected: false, meal_type: undefined })
      }
      this.mealData = mealDataList
      this.showCopyMealDialog = false
    },

    // 确定复制某一个餐段
    async confirmCopyMeal() {
      const targetData = []
      const result = this.mealData.filter(d => d.selected)
      let isPass = true
      result.forEach(r => {
        if (!r.meal_type) {
          isPass = false
        }
        targetData.push({
          weekday: r.value,
          meal_type: r.meal_type
        })
      })
      if (!isPass) {
        return this.$message.error('请选择餐段！')
      }
      const param = {
        target_data: targetData,
        id: this.currentCopyId
      }

      const res = await this.$apis.apiBackgroundFoodMenuWeeklyCopyMealTypeFoodPost(param)
      if (res.code === 0) {
        this.$message.success('操作成功！')
        this.showCopyMealDialog = false
        this.initLoad()
      } else {
        this.$message.error(res.msg)
      }
    },

    // 打开复制整天的复制框
    handleCopy(weekDate, copyDate) {
      this.copyDate = copyDate
      this.selectedDate = weekDate
      this.showCopyDialog = true
      this.checkList = []
    },
    // 餐段复制 - 打开弹窗
    handleMealCopy(id) {
      this.currentCopyId = id
      this.showCopyMealDialog = true
    },

    // 餐段修改
    handleMealEdit(data, date, mealType) {
      if (mealType === 'afternoon' || mealType === 'supper' || mealType === 'morning') {
        this.isNutritionGuidance = false
      }
      this.$router.push({
        name: 'MerchantMenuCatering',
        query: {
          isNutritionGuidance: this.isNutritionGuidance ? 'true' : 'false',
          currentEditDate: date,
          menuType: this.menuType,
          menuId: this.menuId,
          currentEditMealType: mealType,
          data: this.$encodeQuery(data)
        }
      })
    },

    // 初始化时间
    initDateRangeString() {
      const mealDataList = []
      const tableDataList = []
      for (let i = 1; i < 8; i++) {
        mealDataList.push({ value: i, selected: false, meal_type: undefined })
      }

      MEAL_TYPES.forEach(m =>
        tableDataList.push({
          meal_type: m.label,
          field: m.value,
          data: []
        })
      )
      this.mealData = mealDataList
      this.tableData = tableDataList
    },

    async requestDetailData() {
      const id = Number(this.$route.query.id)
      if (!id) {
        this.$message.error('id获取失败')
        return
      }
      this.isLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundFoodMenuWeeklyWeeklyDetailPost({ id }))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        let mealDailyDataList = deepClone(Object.keys(res.data.daily_data))
        this.dateRangeString = `${mealDailyDataList[0]} 至 ${
          mealDailyDataList[mealDailyDataList.length - 1]
        }`
        this.isNutritionGuidance = res.data.is_nutrition_guidance
        this.$emit('guidanceChange', this.isNutritionGuidance)
        // 处理周菜谱表格数据
        sessionStorage.setItem('mealDailyData', JSON.stringify(res.data.daily_data))
        this.handleMealData(res.data.daily_data)
      } else {
        this.$message.error(res.msg)
      }
    },
    handleMealData(data) {
      const keys = Object.keys(data)
      const columns = [{ title: '餐段', column: 'meal_type' }]
      for (let i = 0; i < keys.length; i++) {
        columns.push({
          title: this.weekLabelList[i],
          column: keys[i],
          date: dayjs(keys[i]).format('YYYY年MM月DD日')
        })
      }
      // 合计数据
      const summaryData = {
        meal_type: '合计',
        field: 'summary',
        data: []
      }
      this.columns = columns
      keys.forEach(k => {
        let insufficientTotal = 0
        let suitableTotal = 0
        let overdoseTotal = 0
        this.tableData.forEach(td => {
          const res = data[k].foods.filter(d => d.meal_type === td.field)
          td.data.push(res?.length > 0 ? res[0] : {})
          if (res && res.length) {
            let totalNutrition = res[0].total_nutrition
            // 合计 不足适宜 过量
            if (
              res[0].meal_type === 'breakfast' ||
              res[0].meal_type === 'lunch' ||
              res[0].meal_type === 'dinner'
            ) {
              for (const key in totalNutrition) {
                if (totalNutrition[key] >= 120) {
                  overdoseTotal += 1
                } else if (totalNutrition[key] >= 80) {
                  suitableTotal += 1
                } else if (totalNutrition[key] < 80) {
                  insufficientTotal += 1
                }
              }
            }
          }
        })
        //  push
        summaryData.data.push({
          insufficientTotal,
          suitableTotal,
          overdoseTotal
        })
      })
      if (this.isNutritionGuidance) {
        this.tableData.push(summaryData)
      }
      // 添加合计数据
      // this.tableData.push(summaryData)
    },
    // 计算营养
    percentageTotal(nutritionData) {
      let totle = 0
      if (nutritionData) {
        if (parseInt(nutritionData) >= 100) {
          totle = 100
        } else {
          totle = parseInt((nutritionData / 120) * 100)
        }
      }
      return totle
    },
    //  营养进度条的样式
    percentageColor(nutritionData) {
      let color = ''
      if (nutritionData) {
        if (parseInt(nutritionData) >= 120) {
          //  红色
          color = '#ea5b55'
        } else if (parseInt(nutritionData) >= 80) {
          color = '#5dbf6e'
        } else if (parseInt(nutritionData) < 80) {
          color = '#e89e42'
        }
      }
      return color
    },
    //  关闭营养规则
    async getModifyNutritionGuidance() {
      let params = {
        id: this.menuId,
        operate: 0,
        menu_type: this.menuType
      }
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundFoodMenuModifyNutritionGuidancePost(params)
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success('关闭营养指导')
        this.requestDetailData()
      } else {
        this.$message.error(res.msg)
      }
    },
    changeGuidance(val) {
      this.selectModelDialogInfo = {
        menuType: this.menuType,
        menuId: this.menuId
      }
      // 先默认关闭
      this.isNutritionGuidance = false
      if (val) {
        this.selectModelDialogVisible = true
      }
      if (!val) {
        this.getModifyNutritionGuidance()
        this.tableData.pop()
      }
    },
    clickSelect(data) {
      this.selectModelDialogVisible = false
      this.requestDetailData()
    }
  }
}
</script>

<style scoped lang="scss">
@import '../styles/addWeekRecipes.scss';
.meal-cell-wrapper-bg {
  background: #ebf3fe;
  padding-left: 10px;
  border-left: 2px solid #4385f7;
  border-radius: 3px;
}
.progress-box {
  display: flex;
  align-items: center;
  .progress-title {
    flex: 1;
    font-size: 12px;
  }
  .progress-content {
    width: 43px;
  }
}
.marker-box {
  display: inline-block;
  margin-right: 6px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
}
.popover-wrapper {
  max-height: 250px;
  overflow-y: auto;
}
</style>
