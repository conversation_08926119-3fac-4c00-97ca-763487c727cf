<template>
  <div class="menu-nutrition-analysis" v-loading="isLoading">
    <div class="">
      <span class="weight-text">基本信息</span>
    </div>
    <div class="ps-flex-align-c p-t-10">
      <div class="p-r-10">
        <span class="text-color">所属集体：</span>
        <span>{{ collectiveInfo.collective_name }}</span>
      </div>
      <div class="p-r-10">
        <span class="text-color">总人数：</span>
        <span>{{ collectiveInfo.number }}人</span>
      </div>
      <div class="p-r-10">
        <span class="text-color">男性人数：</span>
        <span>{{ collectiveInfo.man_number }}人</span>
      </div>
      <div class="p-r-10">
        <span class="text-color">女性人数：</span>
        <span>{{ collectiveInfo.women_number }}人</span>
      </div>
    </div>
    <div class="ps-flex-align-c flex-align-c p-t-10">
      <span class="text-color">所含人群：</span>
      <el-tag
        v-for="(item, index) in collectiveInfo.crowd_info"
        :key="index"
        class="m-r-5 m-t-5"
        size="medium"
        effect="plain"
        type="info"
      >
        {{ item }}
      </el-tag>
    </div>
    <div class="ps-flex-align-c flex-align-c p-t-10">
      <span class="text-color">标签：</span>
      <el-tag
        v-for="(item, index) in collectiveInfo.label_info"
        :key="index"
        class="m-r-5 m-t-5"
        size="medium"
        effect="plain"
        type="info"
      >
        {{ item }}
      </el-tag>
    </div>
    <div class="p-t-30 ps-flex-bw " v-if="tableData.length">
      <div>
        <span class="weight-text">{{ collectiveInfo.collective_name }}·食谱营养分析</span>
        <span class="text-color">（{{ weekStartEndDate }}）</span>
      </div>
      <div class="marker-wrapper">
        <div>
          <span class="marker" style=" background: #66dcd0;"></span>
          <span style="color:#66dcd0;margin-right: 10px">早餐</span>
        </div>
        <div>
          <span class="marker" style=" background: #FE985F;"></span>
          <span style="color:#FE985F;margin-right: 10px">午餐</span>
        </div>
        <div>
          <span class="marker" style=" background: #e98497;"></span>
          <span style="color:#e98497;margin-right: 10px">晚餐</span>
        </div>
      </div>
    </div>
    <div v-if="tableData.length">
      <div class="meal_item">
        <div ref="unreached_chart" id="circular_chart"></div>
        <div ref="reached_chart" id="circular_chart"></div>
        <div ref="exceed_chart" id="circular_chart"></div>
      </div>
    </div>
    <div class="" v-if="tableData.length">
      <el-table
        :data="tableData"
        ref="progressTableData"
        style="width: 100%"
        stripe
        border
        header-row-class-name="ps-table-header-row"
        class="ps-table"
      >
        <el-table-column prop="weekDate" label="日期" align="center"></el-table-column>
        <el-table-column prop="breakfast" label="早餐" align="center" width="300">
          <template slot-scope="scope">
            <template v-if="scope.row.breakfast && scope.row.breakfast.length">
              <div class="progress-box" v-for="(item, index) in scope.row.breakfast" :key="index">
                <span class="progress-title">{{ item.element }}</span>
                <el-progress
                  class="progress-content"
                  :show-text="false"
                  :color="formatColorName(item.result).color"
                  :percentage="progressPercentage(item)"
                ></el-progress>
                <span>{{ item.current_value }}/{{ item.need_value }}g</span>
              </div>
            </template>
          </template>
        </el-table-column>
        <el-table-column prop="lunch" label="午餐" align="center" width="300">
          <template slot-scope="scope">
            <template v-if="scope.row.lunch && scope.row.lunch.length">
              <div class="progress-box" v-for="(item, index) in scope.row.lunch" :key="index">
                <span class="progress-title">{{ item.element }}</span>
                <el-progress
                  class="progress-content"
                  :show-text="false"
                  :color="formatColorName(item.result).color"
                  :percentage="progressPercentage(item)"
                ></el-progress>
                <span>{{ item.current_value }}/{{ item.need_value }}g</span>
              </div>
            </template>
          </template>
        </el-table-column>
        <el-table-column prop="dinner" label="晚餐" align="center" width="300">
          <template slot-scope="scope">
            <template v-if="scope.row.dinner && scope.row.dinner.length">
              <div class="progress-box" v-for="(item, index) in scope.row.dinner" :key="index">
                <span class="progress-title">{{ item.element }}</span>
                <el-progress
                  class="progress-content"
                  :show-text="false"
                  :color="formatColorName(item.result).color"
                  :percentage="progressPercentage(item)"
                ></el-progress>
                <span>{{ item.current_value }}/{{ item.need_value }}g</span>
              </div>
            </template>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="p-t-30" v-if="radioGroupDate">
      <div>
        <span class="weight-text">{{ collectiveInfo.collective_name }}·每日营养分析</span>
        <span class="text-color">（{{ startEndDateRange }}）</span>
      </div>
      <div style="flex:1">
        <el-tabs v-model="radioGroupDate" @tab-click="changeRadioGroupDate">
          <el-tab-pane
            v-for="(item, key, index) in dateNutritionData"
            :key="index"
            :label="key"
            :name="key"
          ></el-tab-pane>
        </el-tabs>
        <!-- <el-radio-group v-model="radioGroupDate" size="mini" @input="changeRadioGroupDate">
          <el-radio-button
            :label="key"
            v-for="(item, key, index) in dateNutritionData"
            :key="index"
          >
            {{ key }}
          </el-radio-button>
        </el-radio-group> -->
      </div>

      <div class="nutrition-analysis-wrapp">
        <div
          class="analysis-wrapp"
          v-for="(item, index) in nutritionDataList"
          :key="index"
          @click="clickAnalysisDetailedDialog(item)"
        >
          <div class="analysis-title ps-flex-bw">
            <span class="left">{{ item.name }}</span>
            <span
              class="status"
              :style="{
                backgroundColor: `${formatColorName(item.result).color}`
              }"
            >
              {{ formatColorName(item.result).name }}
            </span>
          </div>
          <div class="analysis-content-wrapp ps-flex-bw">
            <div class="left">
              <div class="text-color font-size-12">统计摄入量</div>
              <div class="total-text">{{ item.current_value }}</div>
              <div class="font-size-12">建议摄入量：{{ item.need_value }}</div>
            </div>
            <el-progress
              :width="90"
              :color="formatColorName(item.result).color"
              :stroke-width="6"
              type="circle"
              :percentage="item.scale >= 100 ? 100 : item.scale"
            ></el-progress>
          </div>
        </div>
      </div>
      <!-- 柱状图 -->
      <div class="bar-box">
        <div ref="analysisBarChart" id="analysisBarChart" style="height:500px;"></div>
      </div>
      <div class="">
        <div class="marker-wrapper p-b-20">
          <div>
            <span class="marker" style=" background: #ef9a50;"></span>
            <span style="margin-right: 10px">不足</span>
          </div>
          <div>
            <span class="marker" style=" background: #5dbf6e;"></span>
            <span style="margin-right: 10px">适宜</span>
          </div>
          <div>
            <span class="marker" style="background:#ea5b54;"></span>
            <span style="margin-right: 10px">过量</span>
          </div>
        </div>
        <el-table
          :data="foodDiversityNutrition"
          ref="progressTableData"
          style="width: 100%"
          stripe
          border
          header-row-class-name="ps-table-header-row"
          class="ps-table"
        >
          <el-table-column prop="name" label="食物类别" align="center"></el-table-column>
          <el-table-column prop="breakfast" label="早餐" align="center" width="300">
            <el-table-column
              prop="breakfast_need_value"
              label="推荐摄入量"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="breakfast_current_value"
              label="统计摄入量"
              align="center"
            ></el-table-column>
            <el-table-column prop="breakfast_scale" label="百分比" align="center">
              <template slot-scope="scope">
                <span
                  :style="{
                    color: `${formatColorName(scope.row.breakfast_result).color}`
                  }"
                >
                  {{ scope.row.breakfast_scale }}
                  <span v-if="scope.row.breakfast_scale">%</span>
                </span>
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column prop="lunch" label="午餐" align="center" width="300">
            <el-table-column
              prop="lunch_need_value"
              label="推荐摄入量"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="lunch_current_value"
              label="统计摄入量"
              align="center"
            ></el-table-column>
            <el-table-column prop="lunch_scale" label="百分比" align="center">
              <template slot-scope="scope">
                <span
                  :style="{
                    color: `${formatColorName(scope.row.lunch_result).color}`
                  }"
                >
                  {{ scope.row.lunch_scale }}
                  <span v-if="scope.row.lunch_scale">%</span>
                </span>
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column prop="dinner" label="晚餐" align="center" width="300">
            <el-table-column
              prop="dinner_need_value"
              label="推荐摄入量"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="dinner_current_value"
              label="统计摄入量"
              align="center"
            ></el-table-column>
            <el-table-column prop="dinner_scale" label="百分比" align="center">
              <template slot-scope="scope">
                <span
                  :style="{
                    color: `${formatColorName(scope.row.dinner_result).color}`
                  }"
                >
                  {{ scope.row.dinner_scale }}
                  <span v-if="scope.row.dinner_scale">%</span>
                </span>
              </template>
            </el-table-column>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <analysis-detailed-dialog
      v-if="analysisDetailedDialogVisible"
      :isshow.sync="analysisDetailedDialogVisible"
      :title="dialoganAlysisDetailedInfo.title"
      :formDataDialog="dialoganAlysisDetailedInfo"
      :dateNutritionData="dateNutritionData"
      width="900px"
      ref="analysisDetailedDialog"
    />
  </div>
</template>
<script>
import { MEALTIME_SETTING, BAR_OPTION_SETTING } from './constants'
import { to, debounce, deepClone, parseTime } from '@/utils'
import analysisDetailedDialog from './analysisDetailedDialog.vue'
import { NUTRITION_DATA_LIST } from '@/utils/constants'
export default {
  props: {
    menuType: {
      type: String,
      default: ''
    },
    menuId: {
      type: String,
      default: ''
    }
    // formDataDialog: {
    //   type: Object,
    //   default() {
    //     return {}
    //   }
    // }
  },
  data() {
    return {
      isLoading: false,
      collectiveInfo: {}, // 基本信息
      weekStartEndDate: '',
      pieMealTimeChart: null, // 餐段统计
      barAnalysis: null,
      circularChartRefList: [
        {
          type: 'unreached',
          key: 'unreached_chart',
          title: '摄入不足',
          data: [
            { value: 0, name: '早餐摄入不足', key: 'breakfast' },
            { value: 0, name: '午餐摄入不足', key: 'lunch' },
            { value: 0, name: '晚餐摄入不足', key: 'dinner' }
          ]
        },
        {
          type: 'reached',
          key: 'reached_chart',
          title: '摄入适宜',
          data: [
            { value: 0, name: '早餐摄入适宜', key: 'breakfast' },
            { value: 0, name: '午餐摄入适宜', key: 'lunch' },
            { value: 0, name: '晚餐摄入适宜', key: 'dinner' }
          ]
        },
        {
          type: 'exceed',
          key: 'exceed_chart',
          title: '摄入过量',
          data: [
            { value: 0, name: '早餐摄入过量', key: 'breakfast' },
            { value: 0, name: '午餐摄入过量', key: 'lunch' },
            { value: 0, name: '晚餐摄入过量', key: 'dinner' }
          ]
        }
      ],
      dateNutritionData: {}, // 每日营养底步
      startEndDateRange: '', //  每日营养日期多少至到多少
      nutritionDataList: [
        {
          key: 'energy_kcal',
          name: '能量(kcal)',
          title: '能量摄入明细',
          table_name: '所含能量(kcal)'
        },
        {
          key: 'protein',
          name: '蛋白质(g)',
          title: '蛋白质摄入明细',
          table_name: '蛋白质(g)'
        },
        {
          key: 'axunge',
          name: '脂肪(g)',
          title: '脂肪摄入明细',
          table_name: '脂肪(g)'
        },
        {
          key: 'carbohydrate',
          name: '碳水化物(g)',
          title: '碳水化物摄入明细',
          table_name: '碳水化物(g)'
        }
      ],
      pieChart: {
        unreached_chart: null,
        reached_chart: null,
        exceed_chart: null
      },
      tableData: [],
      foodDiversityNutrition: [],
      foodDiversityTableSetting: [
        { label: '食物类别', key: 'name' },
        {
          key: 'breakfast',
          label: '早餐',
          children: [
            { key: 'breakfast_need_value', label: '推荐摄入量' },
            { key: 'breakfast_current_value', label: '统计摄入量' },
            { key: 'breakfast_scale', label: '百分比', type: 'scale' }
          ]
        },
        {
          key: 'lunch',
          label: '午餐',
          children: [
            { key: 'lunch_need_value', label: '推荐摄入量' },
            { key: 'lunch_current_value', label: '统计摄入量' },
            { key: 'lunch_scale', label: '百分比', type: 'scale' }
          ]
        },
        {
          key: 'dinner',
          label: '晚餐',
          children: [
            { key: 'dinner_need_value', label: '推荐摄入量' },
            { key: 'dinner_current_value', label: '统计摄入量' },
            { key: 'dinner_scale', label: '百分比', type: 'scale' }
          ]
        }
      ],
      radioGroupDate: '',
      analysisDetailedDialogVisible: false,
      dialoganAlysisDetailedInfo: {}
    }
  },
  watch: {},
  components: {
    analysisDetailedDialog
  },
  created() {
    this.getMenuNutrition()
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.resizeChartHandle)
  },
  mounted() {
    window.addEventListener('resize', this.resizeChartHandle)
  },
  methods: {
    // 数据
    async getMenuNutrition() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundFoodMenuMenuNutritionPost({
          id: Number(this.menuId),
          menu_type: this.menuType
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        // 基本信息
        this.collectiveInfo = res.data.collective_info
        // totalNutritionDataDateDataList目前拿来做数据判断有没有
        // 食谱营养分析
        let totalNutritionDataDateData = res.data.total_nutrition_data.date_data
        // 日期 多少至多少
        this.weekStartEndDate = `${Object.keys(totalNutritionDataDateData)[0]}至${
          Object.keys(totalNutritionDataDateData)[
            Object.keys(totalNutritionDataDateData).length - 1
          ]
        }`
        // 每周早午晚摄入不足
        let mealTypeData = res.data.total_nutrition_data.meal_type_data
        this.circularChartRefList.forEach(v => {
          if (mealTypeData[v.type]) {
            v.data.forEach(k => {
              k.value = mealTypeData[v.type][k.key]
            })
          }
        })
        // 每周早午晚摄入不足 table
        let totalNutritionDataDateDataList = []
        for (const key in totalNutritionDataDateData) {
          let info = {
            date: key,
            weekDate: `星期${parseTime(new Date(key), '{a}')}(${key})`
          }
          for (const mealKey in totalNutritionDataDateData[key]) {
            info[mealKey] = totalNutritionDataDateData[key][mealKey]
          }
          totalNutritionDataDateDataList.push(info)
        }
        this.tableData = totalNutritionDataDateDataList
        // 每日营养
        this.dateNutritionData = res.data.date_nutrition_data
        // 每日营养日期 多少至多少 展示
        if (Object.keys(this.dateNutritionData) && Object.keys(this.dateNutritionData).length) {
          this.startEndDateRange = `${Object.keys(this.dateNutritionData)[0]}至${
            Object.keys(this.dateNutritionData)[Object.keys(this.dateNutritionData).length - 1]
          }`
          // 先默认第一个
          this.radioGroupDate = Object.keys(this.dateNutritionData)[0]
          this.dateNutritionDataInfoChange()
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    // 每周table进度条
    progressPercentage(data) {
      let nutritionPercentage = parseInt((data.current_value / data.need_value) * 100)
      let totle = 0
      if (nutritionPercentage) {
        if (nutritionPercentage >= 100) {
          totle = 100
        } else {
          totle = nutritionPercentage
        }
      }
      return totle
    },
    formatColorName(type) {
      let params = {
        color: '',
        name: ''
      }
      switch (type) {
        case 'unreached':
          params.color = '#e89e42'
          params.name = '不足'
          break
        case 'reached':
          params.color = '#5dbf6e'
          params.name = '适宜'

          break
        case 'exceed':
          params.color = '#ea5b55'
          params.name = '过量'
          break
      }
      return params
    },
    // change 每日营养日期
    changeRadioGroupDate(val) {
      this.dateNutritionDataInfoChange()
    },
    dateNutritionDataInfoChange() {
      let barSourceList = []
      // 食物类别
      let foodNutrition = this.dateNutritionData[this.radioGroupDate].food_nutrition
      let nutritionData = deepClone(this.dateNutritionData[this.radioGroupDate].nutrition_data)
      this.nutritionDataList.forEach(v => {
        v.current_value = nutritionData[v.key].current_value
        v.need_value = nutritionData[v.key].need_value
        v.result = nutritionData[v.key].result
        v.scale = nutritionData[v.key].scale
      })
      if (Object.keys(nutritionData)) {
        NUTRITION_DATA_LIST.forEach(v => {
          // bar 需要的数据
          let info = {
            product: v.name + '(' + v.unit + ')',
            统计摄入量: nutritionData[v.key].current_value,
            建议摄入量: nutritionData[v.key].need_value
          }
          barSourceList.push(info)
        })
        // bar图标实例数据
        BAR_OPTION_SETTING.dataset.source = barSourceList
        // 食物类别表
        // 每次需要重置数据
        this.foodDiversityNutrition = [
          {
            name: '谷物',
            key: 'cereals'
          },
          {
            name: '鱼禽肉蛋',
            key: 'eggsandmeat'
          },
          {
            name: '水果',
            key: 'fruit'
          },
          {
            name: '蔬菜',
            key: 'vegetable'
          }
        ]
        this.foodDiversityNutrition = this.foodDiversityNutrition.map(v => {
          // 需要拿到最里面的数据push 到一个新数组渲染table
          for (const mealKey in foodNutrition[v.key]) {
            for (const diversityKey in foodNutrition[v.key][mealKey]) {
              this.$set(
                v,
                mealKey + '_' + diversityKey,
                foodNutrition[v.key][mealKey][diversityKey]
              )
            }
          }
          return v
        })
        // 更新图标数据
        this.$nextTick(() => {
          this.initMealTimeDataPie()
          this.initMealTimeDataBar()
        })
      }
    },
    // 初始化餐段统计数据
    initMealTimeDataPie() {
      this.circularChartRefList.forEach(v => {
        let data = v.data
        let nameKey = {}
        data.forEach(item => {
          nameKey[item.name] = item.value
        })
        // 名字
        MEALTIME_SETTING.title.subtext = v.title
        let mealtimeSetting = MEALTIME_SETTING
        mealtimeSetting.legend.formatter = function(name) {
          let value = nameKey[name]
          // let unit = v.type === 'unreached' ? '笔' : ''
          return name + '    ' + (value || 0)
        }
        mealtimeSetting.series[0].data = data
        mealtimeSetting.title.text = data.reduce((x, y) => {
          return x + y.value
        }, 0)
        if (!this.pieChart[v.key]) {
          this.pieChart[v.key] = this.$echarts.init(this.$refs[v.key])
        }
        this.pieChart[v.key].setOption(mealtimeSetting)
      })
    },
    initMealTimeDataBar() {
      // this.barOption.xAxis.data = xAxisData
      // this.barOption.series[0].data = seriesData
      let barOption = BAR_OPTION_SETTING
      if (!this.barAnalysis) {
        this.barAnalysis = this.$echarts.init(this.$refs.analysisBarChart)
      }
      if (this.barAnalysis) {
        this.barAnalysis.setOption(barOption)
      }
    },
    clickAnalysisDetailedDialog(data) {
      this.dialoganAlysisDetailedInfo = {
        id: Number(this.menuId),
        menu_type: this.menuType,
        date: this.radioGroupDate,
        nutrition_type: data.key,
        title: data.title,
        table_name: data.table_name
      }
      console.log(this.dialoganAlysisDetailedInfo)
      this.analysisDetailedDialogVisible = true
    },
    resizeChartHandle: debounce(function() {
      if (this.pieChart.unreached_chart) this.pieChart.unreached_chart.resize()
      if (this.pieChart.reached_chart) this.pieChart.reached_chart.resize()
      if (this.pieChart.exceed_chart) this.pieChart.exceed_chart.resize()
      if (this.barAnalysis) this.barAnalysis.resize()
    }, 300)
  }
}
</script>

<style lang="scss">
.menu-nutrition-analysis {
  padding: 0px 20px 20px 20px;
  .text-color {
    color: #a5a5a5;
  }
  .weight-text {
    font-weight: bold;
  }
  .marker-wrapper {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    .marker {
      display: inline-block;
      width: 10px;
      height: 10px;

      border-radius: 8px;
      margin-right: 5px;
    }
  }
  .meal_item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 170px;
    border-radius: 10px;
  }
  #circular_chart {
    position: relative;
    width: 290px;
    height: 100%;
    margin: auto;
  }
  .progress-box {
    display: flex;
    align-items: center;
    padding: 10px;
    .progress-title {
      // flex: 1;
      font-size: 12px;
    }
    .progress-content {
      flex: 1;
      width: 43px;
      padding: 0 10px;
    }
  }
  .el-radio-button:last-child .el-radio-button__inner {
    border-radius: 4px !important;
    margin-right: 20px;
  }
  .el-radio-button:first-child .el-radio-button__inner {
    border: 1px solid #dcdfe6;
    border-radius: 0;
    border-radius: 4px;
  }
  .el-radio-button__inner {
    margin-top: 5px;
    margin-right: 10px;
    border: 1px solid #dcdfe6;
  }
  .el-radio-button.is-focus .el-radio-button__inner {
    border-color: #dcdfe6;
    border: 1px solid #dcdfe6;
  }
  .el-radio-button__inner:hover {
    color: #606266;
  }
  .el-radio-button__orig-radio:checked + .el-radio-button__inner {
    color: #fff;
    background-color: #fd9445;
    border-color: #fd9445;
    -webkit-box-shadow: -1px 0 0 0 #fd9445;
    box-shadow: -1px 0 0 0 #fd9445;
  }
  .el-radio-button:focus:not(.is-focus):not(:active):not(.is-disabled) {
    box-shadow: none;
  }
  .nutrition-analysis-wrapp {
    padding-top: 10px;
    display: flex;
    justify-content: space-between;
    .analysis-wrapp {
      width: 23%;
      // margin-right: 20px;
      border: 1px solid #ccc;
      border-radius: 7px;
      cursor: pointer;
      .analysis-title {
        padding: 10px;
        border-bottom: 1px solid #ccc;
        .left {
          font-weight: bold;
        }
        .status {
          font-size: 13px;
          padding: 2px 8px;
          color: #fff;
          // background-color: #fd9445;
          border-radius: 15px;
        }
      }
      .analysis-content-wrapp {
        padding: 10px;
        .left {
          .total-text {
            padding-top: 5px;
            padding-bottom: 5px;
            font-size: 25px;
            font-weight: bold;
          }
        }
      }
    }
  }
  .bar-box {
    padding-top: 10px;
    width: 100%;
  }
  .el-tabs {
    margin: 8px 5px;
  }
  .el-tabs__nav-wrap::after {
    background-color: transparent;
  }
  .el-tabs__item.is-active,
  .el-tabs__item:hover {
    color: #ee9951;
  }
  .el-tabs__active-bar {
    background-color: #ee9951;
  }
}
</style>
