import { getDateRang } from "@/utils"

export const STORE_GOODS_ADMIN_INFO = {
  goods_category_ids: {
    type: 'select',
    value: [],
    multiple: true,
    clearable: true,
    collapseTags: true,
    listNameKey: 'name',
    listValueKey: 'id',
    label: '分类',
    dataList: []
  },
  barcode: {
    type: 'input',
    label: '条码',
    value: '',
    placeholder: '请输入条码'
  },
  name: {
    type: 'input',
    label: '商品名称',
    value: '',
    placeholder: '请输入商品名称'
  },
  sale_status: {
    type: 'select',
    label: '上下架',
    value: '',
    placeholder: '请选择类型',
    dataList: [
      {
        label: '全部',
        value: '2'
      },
      {
        label: '上架',
        value: '1'
      },
      {
        label: '下架',
        value: '0'
      }
    ]
  },
  goods_unit: {
    type: 'select',
    value: [],
    multiple: true,
    clearable: true,
    collapseTags: true,
    listNameKey: 'name',
    listValueKey: 'id',
    label: '单位',
    dataList: []
  },
  other: {
    type: 'select',
    value: [],
    multiple: true,
    clearable: true,
    collapseTags: true,
    listNameKey: 'name',
    listValueKey: 'type',
    label: '其他',
    dataList: [
      {
        name: '一品多码商品',
        type: 'is_multi_barcode'
      },
      {
        name: '多规格商品',
        type: 'is_multi_spec'
      }
    ]
  }
}
export const STORE_STOCK = {
  goods_category_ids: {
    type: 'select',
    value: [],
    multiple: true,
    clearable: true,
    collapseTags: true,
    listNameKey: 'name',
    listValueKey: 'id',
    label: '分类',
    dataList: []
  },
  barcode: {
    type: 'input',
    label: '条码',
    value: '',
    placeholder: '请输入条码'
  },
  name: {
    type: 'input',
    label: '商品名称',
    value: '',
    placeholder: '请输入商品名称'
  },
  goods_unit: {
    type: 'select',
    value: [],
    multiple: true,
    clearable: true,
    collapseTags: true,
    listNameKey: 'name',
    listValueKey: 'id',
    label: '单位',
    dataList: []
  }
}
export const CATEGORY_STOCK = {
  goods_category_ids: {
    type: 'select',
    value: [],
    multiple: true,
    clearable: true,
    collapseTags: true,
    listNameKey: 'name',
    listValueKey: 'id',
    label: '分类',
    dataList: []
  }
}
export const CATEGORY_STATISTICS = {
  select_time: {
    type: 'datetimerange',
    format: 'yyyy-MM-dd HH:mm:ss',
    label: '创建时间',
    value: getDateRang(-7),
    clearable: false
  },
  goods_category_ids: {
    type: 'select',
    value: [],
    multiple: true,
    clearable: true,
    collapseTags: true,
    listNameKey: 'name',
    listValueKey: 'id',
    label: '分类',
    dataList: []
  }
}
export const GOODS_STATISTICS = {
  select_time: {
    type: 'datetimerange',
    format: 'yyyy-MM-dd HH:mm:ss',
    label: '创建时间',
    value: getDateRang(-7),
    clearable: false
  },
  goods_category_ids: {
    type: 'select',
    value: [],
    multiple: true,
    clearable: true,
    collapseTags: true,
    listNameKey: 'name',
    listValueKey: 'id',
    label: '分类',
    dataList: []
  },
  barcode: {
    type: 'input',
    label: '条码',
    value: '',
    placeholder: '请输入条码'
  },
  name: {
    type: 'input',
    label: '商品名称',
    value: '',
    placeholder: '请输入商品名称'
  }
}
export const ADD_STOCK_DETAILS = {
  select_time: {
    type: 'datetimerange',
    format: 'yyyy-MM-dd HH:mm:ss',
    label: '入库时间',
    value: getDateRang(-7),
    clearable: false
  },
  goods_category_ids: {
    type: 'select',
    value: [],
    multiple: true,
    clearable: true,
    collapseTags: true,
    listNameKey: 'name',
    listValueKey: 'id',
    label: '分类',
    dataList: []
  },
  barcode: {
    type: 'input',
    label: '条码',
    value: '',
    placeholder: '请输入条码'
  },
  name: {
    type: 'input',
    label: '商品名称',
    value: '',
    placeholder: '请输入商品名称'
  },
  operator_name: {
    type: 'input',
    label: '操作人',
    value: '',
    placeholder: '请输入操作人'
  },
  operate_type: {
    type: 'select',
    value: [],
    multiple: true,
    clearable: true,
    collapseTags: true,
    listNameKey: 'name',
    listValueKey: 'type',
    label: '入库类型',
    dataList: [
      {
        name: '操作入库',
        type: 'operate'
      },
      {
        name: '退款入库',
        type: 'refund'
      },
      {
        name: '失败订单入库',
        type: 'order_fail'
      }
    ]
  },
  trade_no: {
    type: 'input',
    label: '订单号',
    value: '',
    placeholder: '请输入订单号'
  }
}
export const DEDUCT_STOCK_DETAILS = {
  select_time: {
    type: 'datetimerange',
    format: 'yyyy-MM-dd HH:mm:ss',
    label: '出库时间',
    value: getDateRang(-7),
    clearable: false
  },
  goods_category_ids: {
    type: 'select',
    value: [],
    multiple: true,
    clearable: true,
    collapseTags: true,
    listNameKey: 'name',
    listValueKey: 'id',
    label: '分类',
    dataList: []
  },
  barcode: {
    type: 'input',
    label: '条码',
    value: '',
    placeholder: '请输入条码'
  },
  name: {
    type: 'input',
    label: '商品名称',
    value: '',
    placeholder: '请输入商品名称'
  },
  operator_name: {
    type: 'input',
    label: '操作人',
    value: '',
    placeholder: '请输入操作人'
  },
  operate_type: {
    type: 'select',
    value: [],
    multiple: true,
    clearable: true,
    collapseTags: true,
    listNameKey: 'name',
    listValueKey: 'type',
    label: '出库原因',
    dataList: [
      {
        name: '销售出库',
        type: 'sale'
      },
      {
        name: '盘点出库',
        type: 'check'
      },
      {
        name: '保质期出库',
        type: 'allot'
      },
      {
        name: '破损出库',
        type: 'breakage'
      },
      {
        name: '其他',
        type: 'other'
      }
    ]
  },
  trade_no: {
    type: 'input',
    label: '订单号',
    value: '',
    placeholder: '请输入订单号'
  }
}
