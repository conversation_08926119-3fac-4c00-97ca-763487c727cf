<template>
  <div class="relationSupplierIngredient container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form
      ref="searchRef"
      :form-setting="searchFormSetting"
      @search="searchHandle"
      label-width="100px"
    />
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" type="add" @click="addOrEditIngredient('add')">
            新建
          </button-icon>
          <button-icon color="plain" type="Import" @click="importHandler('import')">
            批量导入
          </button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          border
          header-row-class-name="ps-table-header-row"
          @selection-change="handleSelectionChange"
        >
          <el-table-column prop="create_time" label="关联时间" align="center"></el-table-column>
          <el-table-column prop="ingredient_name" label="食材名称" align="center"></el-table-column>
          <el-table-column prop="xx" label="营养信息" align="center">
            <template slot-scope="scope">
              <el-button
                v-if="scope.row.is_enable_nutrition"
                type="text"
                size="small"
                class="ps-text"
                @click="showDialogHandler('nutrition', scope.row)"
              >
                查看
              </el-button>
              <span v-else>--</span>
            </template>
          </el-table-column>
          <el-table-column prop="sourced_code" label="食材溯源码" align="center" width="100">
            <template slot-scope="scope">
              <el-image
                class="w-100-p"
                @click="imgsourcedCodeClick(scope.row)"
                :src="scope.row.sourced_code"
                :preview-src-list="srcList"
                alt="暂无图片"
              ></el-image>
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" width="180" align="center">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                class="ps-text"
                @click="addOrEditIngredient('modify', scope.row)"
              >
                编辑
              </el-button>
              <el-button type="text" size="small" @click="mulOperation('del', scope.row)">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- table end -->
      </div>

      <tupplier-ingredient-dialog
        v-if="ingredientDialogVisible"
        :isshow.sync="ingredientDialogVisible"
        :type="ingredientDialogType"
        :title="ingredientDialogTitle"
        width="500"
        :ingredient-info="ingredientInfo"
        @confirm="searchHandle"
      />
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100, 500]"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>
    <!-- 弹窗 start -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      :width="dialogWidth"
      top="20vh"
      custom-class="ps-dialog"
      :close-on-click-modal="false"
      @closed="dialogHandleClose"
    >
      <el-form v-loading="isLoading" :model="formData" class="" size="small">
        <!-- 营养 start -->
        <div v-if="dialogType === 'nutrition'">
          <template v-for="nutrition in nutritionList">
            <div class="nutrition-item" :key="nutrition.key">
              <div class="nutrition-label">{{ nutrition.name + '：' }}</div>
              <el-form-item :prop="nutrition.key">
                <el-input
                  style="width: 120px"
                  readonly
                  v-model="formData[nutrition.key]"
                  class="ps-input"
                ></el-input>
                <span style="margin-left: 10px">{{ nutrition.unit }}</span>
              </el-form-item>
            </div>
          </template>
        </div>
        <!-- 营养 end -->
      </el-form>
      <span slot="footer" v-if="dialogType !== 'nutrition'" class="dialog-footer">
        <el-button size="small" class="ps-cancel-btn" @click="dialogVisible = false">
          取消
        </el-button>
        <el-button class="ps-origin-btn" type="primary" size="small" @click="dialogVisible = false">
          确定
        </el-button>
      </span>
    </el-dialog>
    <!-- 弹窗 end -->
    <!-- 批量导入 -->
    <el-dialog
      title="批量导入"
      :visible.sync="importShowDialog"
      width="600px"
      custom-class="ps-dialog"
    >
      <import-upload-file
        @publicUrl="publicUrl"
        uploadFormItemLabel="批量导入"
        file-type="zip"
        :link="importLink"
      ></import-upload-file>
      <span slot="footer" class="dialog-footer">
        <el-button class="ps-cancel-btn" @click="importShowDialog = false">取 消</el-button>
        <el-button class="ps-btn" type="primary" @click="imortHandler">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import exportExcel from '@/mixins/exportExcel' // 导出混入
import tupplierIngredientDialog from './components/tupplierIngredientDialog.vue'
import { debounce, replaceSingleQuote } from '@/utils'
import { NUTRITION_LIST } from './components/nutrition'
import ImportUploadFile from '@/components/ImportUploadFile'
export default {
  name: 'MealReportDetail',
  mixins: [exportExcel],
  data() {
    return {
      importLink: '',
      supplierId: '',
      tableData: [],
      srcList: ['https://fuss10.elemecdn.com/8/27/f01c15bb73e1ef3793e64e6b7bbccjpeg.jpeg'],
      isLoading: false, // 刷新数据
      searchFormSetting: {
        ingredient_name: {
          type: 'input',
          value: '',
          label: '食材名称',
          placeholder: '请输入食材名称'
        }
      },
      ingredientDialogVisible: false,
      ingredientInfo: {},
      ingredientDialogType: '',
      ingredientDialogTitle: '关联食材',
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      dialogType: '', // 弹窗类型
      dialogVisible: false,
      dialogTitle: '',
      dialogWidth: '',
      formData: {},
      nutritionList: NUTRITION_LIST, // 营养列表
      importShowDialog: false,
      importUrl: ''
    }
  },
  components: {
    tupplierIngredientDialog,
    ImportUploadFile
  },
  created() {
    // 如果正式就用正式域名 测试就用debug 本地就不要点了
    this.importLink = `${location.origin}/api/temporary/template_excel/supplier_ingredient_import.zip`
    this.supplierId = this.$route.query.supplier_id
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getSupplierIngredientList()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.currentPage = 1
      this.getSupplierIngredientList()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_date') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 列表
    async getSupplierIngredientList() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundFoodIngredientSupplierSupplierIngredientListPost({
        ...this.formatQueryParams(this.searchFormSetting),
        supplier_id: this.supplierId,
        page: this.currentPage,
        page_size: this.pageSize
      })
      this.isLoading = false
      if (res.code === 0) {
        this.tableData = res.data.results
        this.totalCount = res.data.count
      } else {
        this.$message.error(res.msg)
      }
    },
    mulOperation(type, data) {
      this.$confirm('确定删除该条食材数据？', '提示', {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            this.deleteSupplierIngredient([data.id])
            done()
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
    },
    // 删除事件
    async deleteSupplierIngredient(ids) {
      const res = await this.$apis.apiBackgroundFoodIngredientSupplierDeleteSupplierIngredientPost({
        ids
      })
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.getSupplierIngredientList()
      } else {
        this.$message.error(res.msg)
      }
    },
    imgsourcedCodeClick(data) {
      this.srcList[0] = data.sourced_code
    },
    // 显示营养
    showDialogHandler(type, row) {
      this.dialogType = type
      if (type === 'nutrition') {
        let rowNutrition = row.ingredient_nutrition
        if (!rowNutrition) {
          // 防止nutrition没值的情况
          rowNutrition = {}
        }
        console.log(rowNutrition)
        let element = rowNutrition.element
          ? JSON.parse(replaceSingleQuote(rowNutrition.element))
          : {}
        let vitamin = rowNutrition.vitamin
          ? JSON.parse(replaceSingleQuote(rowNutrition.vitamin))
          : {}
        NUTRITION_LIST.forEach(nutrition => {
          if (nutrition.type === 'default') {
            this.$set(this.formData, nutrition.key, rowNutrition[nutrition.key])
          }
          if (nutrition.type === 'element') {
            this.$set(this.formData, nutrition.key, element[nutrition.key])
          }
          if (nutrition.type === 'vitamin') {
            this.$set(this.formData, nutrition.key, vitamin[nutrition.key])
          }
        })
        this.dialogTitle = '营养信息'
        this.dialogWidth = '700px'
      }
      this.dialogVisible = true
    },
    dialogHandleClose() {
      this.formData = {}
    },
    async imortHandler() {
      this.importShowDialog = false
      const option = {
        type: 'importSupplierBatchAddSupplierIngredient',
        immediate: true, // 立刻执行不弹窗
        params: {
          face_zip_url: this.importUrl,
          supplier_id: this.supplierId
        }
      }
      this.exportHandle(option)
    },
    publicUrl(url) {
      this.importUrl = url
    },
    importHandler() {
      this.importShowDialog = true
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.getSupplierIngredientList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val
      this.getSupplierIngredientList()
    },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {},
    addOrEditIngredient(type, data) {
      this.ingredientDialogType = type
      this.ingredientDialogVisible = true
      if (type === 'add') {
        this.ingredientInfo = {
          supplier_id: this.supplierId
        }
      } else {
        this.ingredientInfo = {
          id: data.id,
          ingredient_id: Number(data.ingredient_id),
          sourced_code: data.sourced_code
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.ps-dialog {
  .nutrition-item {
    // display: flex;
    // justify-content: space-around;
    // flex-wrap: wrap;
    display: inline-block;
    width: 200px;
    .nutrition-label {
      margin-bottom: 3px;
      font-size: 14px;
      letter-spacing: 1px;
      color: #23282d;
    }
  }
}
</style>
