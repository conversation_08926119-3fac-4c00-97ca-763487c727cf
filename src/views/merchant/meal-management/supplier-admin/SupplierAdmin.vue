<template>
  <div class="meal-details container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form
      ref="searchRef"
      :form-setting="searchFormSetting"
      @search="searchHandle"
      :autoSearch="false"
      label-width="100px"
    />
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" type="add" @click="addOrEditSupplier('add')" v-permission="['background_food.ingredient_supplier.add']">新建</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          border
          header-row-class-name="ps-table-header-row"
          @selection-change="handleSelectionChange"
        >
          <el-table-column prop="no" label="供应商编号" align="center"></el-table-column>
          <el-table-column prop="name" label="供应商名称" align="center"></el-table-column>
          <el-table-column prop="credit_code" label="" align="center" width="190">
            <template slot="header">
              <div>工商营业执照</div>
              <div>（社会统一信用代码）</div>
            </template>
          </el-table-column>
          <el-table-column prop="tax_registration_license" label="税务登记证" align="center">
            <template slot-scope="scope">
              <el-image
                class="w-100-p"
                @click="imgRegistrationClick(scope.row)"
                :src="scope.row.tax_registration_license"
                :preview-src-list="srcList"
                alt="暂无图片"
              ></el-image>
            </template>
          </el-table-column>
          <el-table-column prop="business_license" label="食品经营许可证" align="center">
            <template slot-scope="scope">
              <el-image
                class="w-100-p"
                @click="imgBusinessClick(scope.row)"
                :src="scope.row.business_license"
                :preview-src-list="srcList"
                alt="暂无图片"
              ></el-image>
            </template>
          </el-table-column>
          <el-table-column prop="circulation_license" label="食品流通许可证" align="center">
            <template slot-scope="scope">
              <el-image
                class="w-100-p"
                @click="imgCirculationClick(scope.row)"
                :src="scope.row.circulation_license"
                :preview-src-list="srcList"
                alt="暂无图片"
              ></el-image>
            </template>
          </el-table-column>
          <el-table-column prop="person_name" label="联系人" align="center"></el-table-column>
          <el-table-column prop="phone" label="联系人电话" align="center">
            <template slot-scope="scope">
              <span>{{sensitiveSetting.phone ? scope.row.phone: '****'}}</span>
            </template>
          </el-table-column>
          <el-table-column prop="address" label="供应商地址" align="center"></el-table-column>
          <el-table-column prop="create_time" label="创建时间" align="center"></el-table-column>
          <el-table-column fixed="right" label="操作" width="180" align="center">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                class="ps-text"
                @click="supplierIngredient(scope.row)"
                v-permission="['background_food.ingredient_supplier.add_supplier_ingredient']"
              >
                关联食材
              </el-button>
              <el-button
                type="text"
                size="small"
                class="ps-text"
                @click="addOrEditSupplier('modify', scope.row)"
                v-permission="['background_food.ingredient_supplier.modify']"
              >
                编辑
              </el-button>
              <el-button type="text" size="small" @click="mulOperation('del', scope.row)" v-permission="['background_food.ingredient_supplier.delete']">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100, 500]"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce } from '@/utils'
export default {
  name: 'MealReportDetail',
  data() {
    return {
      tableData: [],
      srcList: ['https://fuss10.elemecdn.com/8/27/f01c15bb73e1ef3793e64e6b7bbccjpeg.jpeg'],
      isLoading: false, // 刷新数据
      searchFormSetting: {
        no: {
          type: 'input',
          value: '',
          label: '供应商编号',
          placeholder: '请输入供应商编号',
          maxlength: 30
        },
        name: {
          type: 'input',
          value: '',
          label: '供应商名称',
          placeholder: '请输入供应商名称',
          maxlength: 30
        },
        phone: {
          type: 'input',
          value: '',
          label: '联系人电话',
          placeholder: '请输入联系人电话',
          maxlength: 11
        },
        person_name: {
          type: 'input',
          value: '',
          label: '联系人',
          placeholder: '请输入联系人',
          maxlength: 10
        },
        credit_code: {
          type: 'input',
          value: '',
          label: '工商营业执照',
          placeholder: '请输入工商营业执照',
          maxlength: 30
        }
      },
      sensitiveSetting: {},
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1 // 第几页
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getSensitiveSetting()
      this.getFoodIngredientSupplierList()
    },
    // 节下流咯
    searchHandle: debounce(function(e) {
      if (e && e === 'search') {
        this.currentPage = 1
        this.getFoodIngredientSupplierList()
      }
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_date') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 列表
    async getFoodIngredientSupplierList() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundFoodIngredientSupplierListPost({
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      })
      this.isLoading = false
      if (res.code === 0) {
        this.tableData = res.data.results
        this.totalCount = res.data.count
      } else {
        this.$message.error(res.msg)
      }
    },
    mulOperation(type, data) {
      let params = {
        id: data.id,
        is_verify: 0
      }
      this.deleteSupplierDelete(params)
      // this.$confirm('确定删除该条供应商数据？', '提示', {
      //   confirmButtonText: this.$t('dialog.confirm_btn'),
      //   cancelButtonText: this.$t('dialog.cancel_btn'),
      //   closeOnClickModal: false,
      //   customClass: 'ps-confirm',
      //   cancelButtonClass: 'ps-cancel-btn',
      //   confirmButtonClass: 'ps-btn',
      //   center: true,
      //   beforeClose: async (action, instance, done) => {
      //     if (action === 'confirm') {
      //       instance.confirmButtonLoading = true
      //       this.deleteSupplierDelete([data.id])
      //       done()
      //       instance.confirmButtonLoading = false
      //     } else {
      //       if (!instance.confirmButtonLoading) {
      //         done()
      //       }
      //     }
      //   }
      // })
    },
    // 删除事件
    async deleteSupplierDelete(params) {
      const res = await this.$apis.apiBackgroundFoodIngredientSupplierDeletePost(params)
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.getFoodIngredientSupplierList()
      } else if (res.code === 2) {
        this.$confirm(res.msg, '提示', {
          confirmButtonText: this.$t('dialog.confirm_btn'),
          cancelButtonText: this.$t('dialog.cancel_btn'),
          closeOnClickModal: false,
          customClass: 'ps-confirm',
          cancelButtonClass: 'ps-cancel-btn',
          confirmButtonClass: 'ps-btn',
          center: true,
          beforeClose: async (action, instance, done) => {
            if (action === 'confirm') {
              instance.confirmButtonLoading = true
              this.deleteSupplierDelete({
                id: params.id,
                is_verify: 1
              })
              done()
              instance.confirmButtonLoading = false
            } else {
              if (!instance.confirmButtonLoading) {
                done()
              }
            }
          }
        })
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取获取登录组织设置的隐藏信息相关的
    async getSensitiveSetting() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundReportCenterDataReportGetSensitiveSettingPost()
      this.isLoading = false
      if (res.code === 0) {
        this.sensitiveSetting = res.data
      } else {
        this.$message.error(res.msg)
      }
    },
    imgRegistrationClick(data) {
      this.srcList[0] = data.tax_registration_license
    },
    imgBusinessClick(data) {
      this.srcList[0] = data.business_license
    },
    imgCirculationClick(data) {
      this.srcList[0] = data.circulation_license
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.getFoodIngredientSupplierList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val
      this.getFoodIngredientSupplierList()
    },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {},
    supplierIngredient(data) {
      this.$router.push({
        name: 'MerchantRelationSupplierIngredient',
        query: {
          supplier_id: data.id
        }
      })
    },
    addOrEditSupplier(type, data) {
      this.$router.push({
        name: 'MerchantAddEditSupplier',
        query: {
          type,
          data: encodeURIComponent(JSON.stringify(data))
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped></style>
