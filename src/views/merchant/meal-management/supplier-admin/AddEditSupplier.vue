<template>
  <div class="add_edit_supplier container-wrapper">
    <el-form
      v-loading="isLoading"
      :rules="formRuls"
      :model="formData"
      ref="formIngredients"
      size="small"
      label-width="130px"
    >
      <div class="table-wrapper">
        <div class="table-header">
          <div class="table-title">基本信息</div>
        </div>
        <div style="padding: 0 20px">
          <el-form-item label="供应商名称" prop="name">
            <el-input
              v-model="formData.name"
              placeholder="请输入供应商名称"
              class="ps-input p-r-48"
              style="width: 240px"
              maxlength="30"
              show-word-limit
            ></el-input>
          </el-form-item>
          <el-form-item
            label="工商营业执照（社会统一信用代码)"
            prop="credit_code"
            label-width="140px"
          >
            <el-input
              v-model="formData.credit_code"
              placeholder="请输入社会统一信用代码"
              class="ps-input p-r-48"
              style="width: 240px"
              maxlength="30"
              show-word-limit
            ></el-input>
          </el-form-item>
          <el-form-item label="税务登记证" prop="tax_registration_license">
            <el-upload
              class="avatar-uploader"
              :data="uploadParams"
              ref="uploadRegistration"
              :action="actionUrl"
              :show-file-list="false"
              :on-success="handleAvatarSuccessRegistration"
              :before-upload="beforeAvatarUpload"
            >
              <img
                v-if="formData.tax_registration_license"
                :src="formData.tax_registration_license"
                class="avatar"
              />
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
              <div slot="tip" class="el-upload__tip">
                仅支持上传jpg/png/bmp格式，图片大小且不超过5M
              </div>
            </el-upload>
          </el-form-item>
          <el-form-item label="食品经营许可证" prop="business_license">
            <el-upload
              class="avatar-uploader"
              :data="uploadParams"
              ref="uploadBusiness"
              :action="actionUrl"
              :show-file-list="false"
              :on-success="handleAvatarSuccessBusiness"
              :before-upload="beforeAvatarUpload"
            >
              <img
                v-if="formData.business_license"
                :src="formData.business_license"
                class="avatar"
              />
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
              <div slot="tip" class="el-upload__tip">
                仅支持上传jpg/png/bmp格式，图片大小且不超过5M
              </div>
            </el-upload>
          </el-form-item>
          <el-form-item label="食品流通许可证" prop="circulation_license">
            <el-upload
              class="avatar-uploader"
              :data="uploadParams"
              ref="uploadCirculation"
              :action="actionUrl"
              :show-file-list="false"
              :on-success="handleAvatarSuccessCirculation"
              :before-upload="beforeAvatarUpload"
            >
              <img
                v-if="formData.circulation_license"
                :src="formData.circulation_license"
                class="avatar"
              />
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
              <div slot="tip" class="el-upload__tip">
                仅支持上传jpg/png/bmp格式，图片大小且不超过5M
              </div>
            </el-upload>
          </el-form-item>
          <el-form-item label="联系人" prop="person_name">
            <el-input
              v-model="formData.person_name"
              placeholder="请输入联系人"
              class="ps-input p-r-48"
              style="width: 240px"
              maxlength="30"
              show-word-limit
            ></el-input>
          </el-form-item>
          <el-form-item label="联系电话" prop="phone">
            <el-input
              v-model="formData.phone"
              placeholder="请输入联系电话"
              class="ps-input p-r-48"
              style="width: 240px"
              maxlength="11"
              show-word-limit
            ></el-input>
          </el-form-item>
          <el-form-item label="供应商地址" prop="address">
            <el-input
              v-model="formData.address"
              placeholder="请输入供应商地址"
              class="ps-input p-r-48"
              style="width: 240px"
              maxlength="30"
              show-word-limit
            ></el-input>
          </el-form-item>
        </div>
      </div>

      <div class="footer" style="margin-top: 20px">
        <el-button style="width: 120px" @click="closeHandler">取消</el-button>
        <el-button class="ps-origin-btn" style="width: 120px" type="primary" @click="submitHandler">
          {{ type === 'add' ? '添加' : '保存' }}
        </el-button>
      </div>
    </el-form>
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, to } from '@/utils'
import { validateTelphone } from '@/assets/js/validata'
export default {
  name: 'SuperAddEditArticle',
  // mixins: [activatedLoadData, exportExcel],
  data() {
    return {
      isLoading: false, // 刷新数据
      type: 'add', // 类型
      formData: {
        name: '',
        credit_code: '',
        tax_registration_license: '',
        business_license: '',
        circulation_license: '',
        person_name: '',
        phone: '',
        address: ''
      },
      formRuls: {
        name: [
          {
            required: true,
            message: '请输入供应商名称',
            trigger: 'blur'
          }
        ],
        credit_code: [
          {
            required: true,
            message: '请输入工商营业执照（社会统一信用代码)',
            trigger: 'blur'
          }
        ],
        tax_registration_license: [
          {
            required: true,
            message: '请上传税务登记证',
            trigger: 'blur'
          }
        ],
        business_license: [
          {
            required: true,
            message: '请上传食品经营许可证',
            trigger: 'blur'
          }
        ],
        circulation_license: [
          {
            required: true,
            message: '请上传食品流通许可证',
            trigger: 'blur'
          }
        ],
        person_name: [
          {
            required: true,
            message: '请输入联系人',
            trigger: 'blur'
          }
        ],
        phone: [
          { required: true, message: '手机号不能为空', trigger: 'blur' },
          { validator: validateTelphone, trigger: 'change' }
        ],
        address: [
          {
            required: true,
            message: '请输入供应商地址',
            trigger: 'blur'
          }
        ]
      },
      tagsList: [],
      actionUrl: '',
      uploadParams: {}
    }
  },
  created() {
    this.getUploadToken()
    this.type = this.$route.query.type
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      if (this.type === 'modify') {
        let data = this.$decodeQuery(this.$route.query.data)
        console.log(data, 11)
        this.formData = {
          id: data.id,
          name: data.name,
          credit_code: data.credit_code,
          tax_registration_license: data.tax_registration_license,
          business_license: data.business_license,
          circulation_license: data.circulation_license,
          person_name: data.person_name,
          phone: data.phone,
          address: data.address
        }
        console.log(data)
      }
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.currentPage = 1
    }, 300),
    async getUploadToken() {
      const res = await this.$apis.getUploadToken({
        prefix: 'jpeg/png'
      })
      if (res.code === 0) {
        this.actionUrl = res.data.host
        this.uploadParams = {
          key: res.data.prefix + new Date().getTime() + Math.floor(Math.random() * 150),
          prefix: res.data.prefix,
          policy: res.data.policy,
          OSSAccessKeyId: res.data.accessid,
          signature: res.data.signature,
          callback: res.data.callback,
          success_action_status: '200'
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    handleAvatarSuccessRegistration(res, file) {
      if (res.code === 0) {
        this.$refs.uploadRegistration.clearFiles()
        this.formData.tax_registration_license = res.data.public_url
        this.getUploadToken()
      } else {
        this.$message.error(res.msg)
      }
    },
    handleAvatarSuccessBusiness(res, file) {
      if (res.code === 0) {
        this.$refs.uploadBusiness.clearFiles()
        this.formData.business_license = res.data.public_url
        this.getUploadToken()
      } else {
        this.$message.error(res.msg)
      }
    },
    handleAvatarSuccessCirculation(res, file) {
      if (res.code === 0) {
        this.$refs.uploadCirculation.clearFiles()
        this.formData.circulation_license = res.data.public_url
        this.getUploadToken()
      } else {
        this.$message.error(res.msg)
      }
    },
    beforeAvatarUpload(file) {
      const isJPG =
        file.type === 'image/jpeg' || file.type === 'image/png' || file.type === 'image/bmp'
      const isLt2M = file.size / 1024 / 1024 < 5

      if (!isJPG) {
        this.$message.error('上传头像图片只能是 JPG PNG BMP 格式!')
      }
      if (!isLt2M) {
        this.$message.error('上传头像图片大小不能超过 5MB!')
      }
      return isJPG && isLt2M
    },
    // 添加
    async addModifySupplier(params) {
      this.isLoading = true
      let [err, res] = ''
      if (this.type === 'add') {
        ;[err, res] = await to(this.$apis.apiBackgroundFoodIngredientSupplierAddPost(params))
      } else {
        ;[err, res] = await to(this.$apis.apiBackgroundFoodIngredientSupplierModifyPost(params))
      }
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.$closeCurrentTab(this.$route.path)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 提交数据
    submitHandler() {
      this.$refs.formIngredients.validate(valid => {
        if (valid) {
          if (this.isLoading) return this.$message.error('请勿重复提交！')
          this.addModifySupplier(this.formData)
        }
      })
    },
    // 返回上一页
    closeHandler() {
      this.$confirm(`当前信息还没保存，是否退出？`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            // instance.confirmButtonLoading = true
            this.$closeCurrentTab(this.$route.path)
            // instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    }
  }
}
</script>

<style lang="scss">
.add_edit_supplier {
  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409eff;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
  }
  .avatar {
    width: 178px;
    height: 178px;
    display: block;
  }
}
.el-input__count .el-input__count-inner {
  background-color: transparent !important;
}
.el-upload__tip {
  color: #409eff;
}
</style>
