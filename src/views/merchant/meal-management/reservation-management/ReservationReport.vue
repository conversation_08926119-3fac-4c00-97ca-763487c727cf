<template>
  <div class="relationSupplierIngredient container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form
      ref="searchRef"
      :form-setting="searchFormSetting"
      @search="searchHandle"
      label-width="100px"
    />
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon
            color="plain"
            @click="opendialogHandle('mul')"
            v-permission="['background_order.ordering_food.modify']"
          >
            修改医嘱
          </button-icon>
          <button-icon
            color="origin"
            type="export"
            @click="gotoExport"
            v-permission="['background_order.ordering_food.list_export']"
          >
            导出EXCEL
          </button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          border
          header-row-class-name="ps-table-header-row"
          @selection-change="handleSelectionChange"
        >
          <el-table-column
            type="selection"
            width="55"
            :selectable="selectableHandle"
          ></el-table-column>
          <el-table-column type="index" align="center" :index="indexMethod"></el-table-column>
          <el-table-column prop="name" label="姓名" align="center"></el-table-column>
          <el-table-column prop="person_no" label="人员编号" align="center">
            <template slot-scope="scope">
              <span>{{ sensitiveSetting.person_no ? scope.row.person_no : '****' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="phone" label="手机号" align="center">
            <template slot-scope="scope">
              <span>{{ sensitiveSetting.phone ? scope.row.phone : '****' }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="card_user_group_alias"
            label="科室"
            align="center"
          ></el-table-column>
          <el-table-column prop="bed_no" label="病床号" align="center"></el-table-column>
          <el-table-column
            prop="dietary_status_alias"
            label="饮食医嘱"
            align="center"
          ></el-table-column>
          <el-table-column prop="origin_fee" label="消费金额" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.origin_fee | formatMoney }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="reservation_date"
            label="就餐日期"
            align="center"
          ></el-table-column>
          <el-table-column prop="dietary_remark" label="备注" align="center"></el-table-column>
          <el-table-column fixed="right" label="操作" align="center">
            <template slot-scope="scope">
              <el-button
                :disabled="!selectableHandle(scope.row)"
                type="text"
                size="small"
                class="ps-text"
                @click="opendialogHandle('sign', scope.row)"
              >
                修改医嘱
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 统计 start -->
      <table-statistics :statistics="collect" />
      <!-- end -->
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-size="pageSize"
          layout="total, prev, pager, next, jumper"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>
    <!-- 弹窗 start -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      :width="dialogWidth"
      top="20vh"
      custom-class="ps-dialog"
      :close-on-click-modal="false"
      @closed="dialogHandleClose"
    >
      <el-form
        v-loading="isLoading"
        :model="formData"
        ref="formData"
        :rules="formRules"
        class=""
        size="small"
      >
        <el-form-item label="" prop="dietaryType">
          <el-radio-group v-model="formData.dietaryType" class="ps-radio">
            <el-radio
              v-for="item in dietaryStatus"
              :key="item.value"
              :label="item.value"
              :value="item.value"
            >
              {{ item.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="" prop="">
          <el-input
            type="textarea"
            placeholder="请输入内容"
            v-model="formData.remark"
            rows="5"
            class="ps-input"
            maxlength="200"
          ></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" v-if="dialogType !== 'nutrition'" class="dialog-footer">
        <el-button
          size="small"
          class="ps-cancel-btn"
          @click="dialogVisible = false"
          :disabled="isLoading"
        >
          取消
        </el-button>
        <el-button
          class="ps-origin-btn"
          type="primary"
          size="small"
          @click="modifyHandle"
          :disabled="isLoading"
        >
          确定
        </el-button>
      </span>
    </el-dialog>
    <!-- 弹窗 end -->
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import exportExcel from '@/mixins/exportExcel' // 导出混入
import { debounce, parseTime } from '@/utils'
import { RESERVATION_REPORT, dietaryStatus } from './constants'

export default {
  name: 'ReservationReport',
  mixins: [exportExcel],
  data() {
    return {
      tableData: [],
      isLoading: false, // 刷新数据
      searchFormSetting: RESERVATION_REPORT,
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      dialogType: '', // 弹窗类型
      dialogVisible: false,
      dialogTitle: '修改医嘱',
      dialogWidth: '460px',
      formData: {
        dietaryType: 'PS',
        remark: ''
      },
      collect: [
        // 统计
        // { key: 'PS', value: 0, label: '普食:' },
        // { key: 'LS', value: 0, label: '流食:' },
        // { key: 'TS', value: 0, label: '停送:' }
      ],
      dietaryStatus,
      selectList: [],
      formRules: {
        dietaryType: [{ required: true, message: '请选择' }],
        remark: [{ required: true, message: '请先填写备注' }]
      },
      now: parseTime(new Date(), '{y}-{m}-{d}'),
      sensitiveSetting: {}
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getSensitiveSetting()
      this.getDataList()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.currentPage = 1
      this.getDataList()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 列表
    async getDataList() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundOrderOrderingFoodListPost({
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      })
      this.isLoading = false
      if (res.code === 0) {
        this.tableData = res.data.results
        this.totalCount = res.data.count
        if (res.data.total) {
          this.collect = []
          res.data.total.forEach(v => {
            this.collect.push({
              key: v.dietary_status,
              value: v.count,
              label: v.dietary_status_alias
            })
          })
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    // 列表序号
    indexMethod(index) {
      return (this.currentPage - 1) * this.pageSize + (index + 1)
    },
    dialogHandleClose() {
      this.formData = {
        dietaryType: 'PS',
        remark: ''
      }
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.getDataList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val
      this.getDataList()
    },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {
      this.selectList = val.map(v => {
        return v.id
      })
    },
    // 判断当前行是否可勾选
    selectableHandle(row, index) {
      if (row.reservation_date === this.now && row.order_status !== 'ORDER_SUCCESS') {
        return true
      } else {
        return false
      }
    },
    // 打开弹窗
    opendialogHandle(type, data) {
      if (type === 'mul') {
        if (!this.selectList.length) return this.$message.error('请先选择数据！')
      } else {
        this.selectList = [data.id]
        this.formData.dietaryType = data.dietary_status
        this.formData.remark = data.dietary_remark
      }
      this.dialogVisible = true
    },
    // 修改
    modifyHandle() {
      this.$refs.formData.validate(async valid => {
        if (valid) {
          this.sendModifyParams()
        }
      })
    },
    // 发送请求
    async sendModifyParams() {
      let params = {
        ids: this.selectList,
        dietary_remark: this.formData.remark,
        dietary_status: this.formData.dietaryType
      }
      this.isLoading = true
      const res = await this.$apis.apiBackgroundOrderOrderingFoodModifyPost(params)
      this.isLoading = false
      if (res.code === 0) {
        this.dialogVisible = false
        this.$message.success(res.msg)
        this.initLoad()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取获取登录组织设置的隐藏信息相关的
    async getSensitiveSetting() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundReportCenterDataReportGetSensitiveSettingPost()
      this.isLoading = false
      if (res.code === 0) {
        this.sensitiveSetting = res.data
      } else {
        this.$message.error(res.msg)
      }
    },
    // 导出
    gotoExport() {
      const option = {
        type: 'ReservationReport',
        url: 'apiBackgroundOrderOrderingFoodListExportPost',
        params: {
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        }
      }
      this.exportHandle(option)
    }
  }
}
</script>

<style lang="scss" scoped>
.ps-dialog {
}
</style>
