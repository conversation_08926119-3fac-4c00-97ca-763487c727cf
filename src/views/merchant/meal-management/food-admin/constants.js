export const DEFAULT_NUTRITION = [
  // { name: '千焦耳', key: 'energy_mj', unit: 'kj', type: 'default' },
  { name: '千卡', key: 'energy_kcal', unit: 'Kcal', type: 'default' },
  { name: '碳水化合物', key: 'carbohydrate', unit: 'g', type: 'default' },
  { name: '蛋白质', key: 'protein', unit: 'g', type: 'default' },
  { name: '脂肪', key: 'axunge', unit: 'g', type: 'default' },
  { name: '胆固醇', key: 'cholesterol', unit: 'mg', type: 'default' },
  { name: '膳食纤维', key: 'dietary_fiber', unit: 'g', type: 'default' }
]

export const ELEMENT_NUTRITION = [
  { key: 'Ca', name: '钙', unit: 'mg', type: 'element' },
  { key: 'P', name: '磷', unit: 'mg', type: 'element' },
  { key: 'K', name: '钾', unit: 'mg', type: 'element' },
  { key: 'Na', name: '钠', unit: 'mg', type: 'element' },
  { name: '镁', key: 'Mg', unit: 'mg', type: 'element' },
  { key: 'Fe', name: '铁', unit: 'mg', type: 'element' },
  { key: 'I', name: '碘', unit: 'μg', type: 'element' },
  { key: 'Se', name: '硒', unit: 'μg', type: 'element' },
  { key: 'Zn', name: '锌', unit: 'mg', type: 'element' },
  { key: 'Cu', name: '铜', unit: 'mg', type: 'element' },
  { key: 'F', name: '氟', unit: 'mg', type: 'element' },
  { key: 'Cr', name: '铬', unit: 'μg', type: 'element' },
  { key: 'Mo', name: '钼', unit: 'μg', type: 'element' },
  { key: 'Mn', name: '锰', unit: 'mg', type: 'element' }
]

export const VITAMIN_NUTRITION = [
  { key: 'VA', name: '维生素A', unit: 'μg', type: 'vitamin' },
  { key: 'VD', name: '维生素D', unit: 'μg', type: 'vitamin' },
  { key: 'VE', name: '维生素E', unit: 'mg', type: 'vitamin' },
  { key: 'VK', name: '维生素K', unit: 'μg', type: 'vitamin' },
  { key: 'VB1', name: '维生素B1', unit: 'mg', type: 'vitamin' },
  { key: 'VB2', name: '维生素B2', unit: 'mg', type: 'vitamin' },
  { key: 'VB6', name: '维生素B6', unit: 'mg', type: 'vitamin' },
  { key: 'VB12', name: '维生素B12', unit: 'μg', type: 'vitamin' },
  { key: 'VC', name: '维生素C', unit: 'mg', type: 'vitamin' },
  { key: 'VB5', name: '泛酸', unit: 'mg', type: 'vitamin' },
  { key: 'VM', name: '叶酸', unit: 'μg', type: 'vitamin' },
  { key: 'VB3', name: '烟酸', unit: 'mg', type: 'vitamin' },
  { key: 'Choline', name: ' 胆碱', unit: 'mg', type: 'vitamin' },
  { key: 'Nicotinamide', name: '烟酰胺', unit: 'mg', type: 'vitamin' },
  { key: 'VH', name: '生物素', unit: 'mg', type: 'vitamin' }
]

export const NUTRITION_LIST = [...DEFAULT_NUTRITION, ...ELEMENT_NUTRITION, ...VITAMIN_NUTRITION]

export const MONTH_TYPE_LIST =
  [
    {
      label: '一月',
      value: 1
    },
    {
      label: '二月',
      value: 2
    },
    {
      label: '三月',
      value: 3
    },
    {
      label: '四月',
      value: 4
    },
    {
      label: '五月',
      value: 5
    },
    {
      label: '六月',
      value: 6
    },
    {
      label: '七月',
      value: 7
    },
    {
      label: '八月',
      value: 8
    },
    {
      label: '九月',
      value: 9
    },
    {
      label: '十月',
      value: 10
    },
    {
      label: '十一月',
      value: 11
    },
    {
      label: '十二月',
      value: 12
    }
  ]

// 食材管理搜索条件 商户食材
export const LIBRARY_SEARCH_SETTING_MERCHANT = {
  date_type: {
    type: 'select',
    label: '',
    value: 1,
    placeholder: '',
    dataList: [
      {
        label: '创建时间',
        value: 1
      },
      {
        label: '修改时间',
        value: 2
      }
    ]
  },
  select_time: {
    type: 'datetimerange',
    label: '',
    format: 'yyyy-MM-dd HH:mm:ss',
    value: []
  },
  label_filter: {
    type: 'select',
    label: ' ', // 空格就是占位
    labelWidth: "10px",
    value: 'Include',
    placeholder: '',
    dataList: [
      {
        label: '标签包含',
        value: 'Include'
      },
      {
        label: '标签不包含',
        value: 'Exclude'
      }
    ]
  },
  label_list: {
    type: 'cascader',
    placeholder: '请选择标签',
    clearable: true,
    filterable: true,
    collapseTags: true,
    showAllLevels: false,
    width: '400px',
    dataList: [
      {
        id: -1,
        name: '平台标签',
        label_list: []
      },
      {
        id: -2,
        name: '自有标签',
        label_list: []
      }
    ],
    props: {
      value: 'id',
      label: 'name',
      multiple: true,
      children: 'label_list',
      emitPath: false
    }
  },
  ingredient_name: {
    type: 'input',
    label: '食材名称',
    labelWidth: '100px',
    value: '',
    placeholder: '请输入食材名称'
  },
  sort_ids: {
    type: 'cascader',
    label: '食材分类',
    value: [],
    placeholder: '请选择分类',
    clearable: true,
    filterable: true,
    collapseTags: true,
    showAllLevels: false,
    labelWidth: '210px',
    width: '400px',
    dataList: [],
    props: {
      value: 'id',
      label: 'name',
      multiple: true,
      children: 'sort_list',
      emitPath: false
    }
  },
  is_enable_nutrition: {
    type: 'select',
    label: '营养信息',
    value: '',
    labelWidth: '100px',
    maxWidth: "200px",
    placeholder: '请选择',
    dataList: [
      {
        label: '全部',
        value: ''
      },
      {
        label: '是',
        value: '1'
      },
      {
        label: '否',
        value: '0'
      }
    ]
  },
  // create_source: {
  //   type: 'organizationSelect',
  //   label: '创建来源',
  //   multiple: false,
  //   checkStrictly: true,
  //   isLazy: false,
  //   value: '',
  //   placeholder: '请选择创建来源',
  //   dataList: [
  //     {
  //       name: '全部',
  //       id: ''
  //     },
  //     {
  //       company: 0,
  //       create_time: '2022-01-25 09:49:26',
  //       has_children: false,
  //       id: 1,
  //       level: -1,
  //       level_name: '',
  //       level_tag: -1,
  //       name: '系统',
  //       parent: null,
  //       status: 'enable',
  //       status_alias: '正常',
  //       tree_id: -1
  //     }
  //   ]
  // },

  is_entering: {
    type: 'select',
    label: '食材图片',
    labelWidth: '120px',
    maxWidth: '192px',
    value: '',
    placeholder: '请选择',
    dataList: [
      {
        label: '全部',
        value: ''
      },
      {
        label: '已录入',
        value: '1'
      },
      {
        label: '未录入',
        value: '0'
      }
    ]
  },
  seasonal_month_list: {
    type: 'select',
    label: '食材应季',
    labelWidth: '100px',
    maxWidth: "200px",
    placeholder: '请选择应季月份',
    multiple: true,
    clearable: true,
    collapseTags: true,
    value: [],
    dataList: MONTH_TYPE_LIST
  }
}

// 食材分类搜索条件
export const LIBRARY_SEARCH_SETTING_CATEORY = {
  select_time: {
    type: 'datetimerange',
    format: 'yyyy-MM-dd HH:mm:ss',
    label: '修改时间',
    value: []
  },
  category: {
    type: 'select',
    label: '分类',
    value: [],
    placeholder: '请选择分类',
    multiple: true,
    collapseTags: true,
    dataList: [
      {
        label: '挂失',
        value: 'LOSS'
      },
      {
        label: '退卡',
        value: 'QUIT'
      },
      {
        label: '使用中',
        value: 'ENABLE'
      },
      {
        label: '未发卡',
        value: 'UNUSED'
      }
    ]
  },
  person_no: {
    type: 'input',
    label: '操作人',
    value: '',
    placeholder: '请输入操作人'
  }
}

// 菜品商品搜索条件 商户菜品/商品
export const COMMODITY_SEARCH_SETTING_MERCHANT = {
  select_time: {
    type: 'datetimerange',
    format: 'yyyy-MM-dd HH:mm:ss',
    label: '创建时间',
    value: []
  },
  nutrition: {
    type: 'select',
    label: '营养录入',
    value: [],
    placeholder: '请选择',
    multiple: true,
    collapseTags: true,
    dataList: [
      {
        label: '全部',
        value: ''
      },
      {
        label: '是',
        value: '1'
      },
      {
        label: '否',
        value: '0'
      }
    ]
  },
  perso_no: {
    type: 'input',
    label: '菜品/商品名称',
    value: '',
    placeholder: '请输入菜品/商品名称'
  },
  hasx: {
    type: 'select',
    label: '已有菜品/商品',
    value: '',
    placeholder: '请选择已有菜品/商品',
    dataList: [
      {
        label: '是',
        value: true
      },
      {
        label: '否',
        value: false
      }
    ]
  },
  attributes: {
    type: 'select',
    label: '属性',
    value: '',
    placeholder: '请选择属性',
    dataList: [
      {
        label: '是',
        value: true
      },
      {
        label: '否',
        value: false
      }
    ]
  }
}

// 规格
export const SPEC_LIST = [
  { name: '份' },
  { name: '碗' },
  { name: '瓶' },
  { name: '碟' },
  { name: '盅' },
  { name: '盆' },
  { name: '件' },
  { name: '串' },
  { name: '例' },
  { name: '只' },
  { name: '边' }
]

// 菜品商品搜索条件 系统菜品/商品
export const MEAL_FOOD_SEARCH_SETTING_MERCHANT_NEW = {
  // date_type: {
  //   type: 'select',
  //   label: '',
  //   value: 1,
  //   maxWidth: '120px',
  //   placeholder: '请选择',
  //   dataList: [
  //     {
  //       label: '创建时间',
  //       value: 1
  //     },
  //     {
  //       label: '修改时间',
  //       value: 2
  //     }
  //   ]
  // },
  select_time: {
    type: 'daterange',
    label: '修改时间',
    labelWidth: '130px',
    format: 'yyyy-MM-dd',
    value: []
  },
  label_filter: {
    type: 'select',
    label: ' ', // 空格就是占位
    labelWidth: '20px',
    maxWidth: '120px',
    value: 'Include',
    placeholder: '',
    dataList: [
      {
        label: '标签包含',
        value: 'Include'
      },
      {
        label: '标签不包含',
        value: 'Exclude'
      }
    ]
  },
  label_list: {
    type: 'cascader',
    placeholder: '选择标签',
    clearable: true,
    filterable: true,
    collapseTags: true,
    showAllLevels: false,
    width: '400px',
    dataList: [
      {
        id: -1,
        name: '平台标签',
        label_list: []
      },
      {
        id: -2,
        name: '自有标签',
        label_list: []
      }
    ],
    props: {
      value: 'id',
      label: 'name',
      multiple: true,
      children: 'label_list',
      emitPath: false
    }
  },
  food_name: {
    type: 'input',
    label: '菜品名称',
    value: '',
    placeholder: '请输入菜品名称',
    maxWidth: '200px',
    labelWidth: '120px'
  },
  category_ids: {
    type: 'cascader',
    label: '菜品分类',
    value: [],
    placeholder: '请选择分类',
    clearable: true,
    filterable: true,
    collapseTags: true,
    showAllLevels: false,
    labelWidth: '130px',
    width: '350px',
    dataList: [],
    props: {
      value: 'id',
      label: 'name',
      multiple: true,
      children: 'children',
      emitPath: false
    }
  },
  is_entering: {
    type: 'select',
    labelWidth: '150px',
    maxWidth: '400px',
    label: '菜品图片',
    value: '',
    placeholder: '请选择',
    dataList: [
      {
        label: '全部',
        value: ''
      },
      {
        label: '已录入',
        value: '1'
      },
      {
        label: '未录入',
        value: '0'
      }
    ]
  },
  attributes: {
    type: 'select',
    label: '属性',
    labelWidth: '120px',
    maxWidth: '200px',
    value: '',
    placeholder: '请选择',
    dataList: [
      {
        label: '全部',
        value: ''
      },
      {
        label: '商品',
        value: 'goods'
      },
      {
        label: '菜品',
        value: 'foods'
      }
    ]
  }
}
