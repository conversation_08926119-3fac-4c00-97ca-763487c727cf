<template>
  <div class="diet-nutrition container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form
      ref="searchRef"
      :form-setting="searchFormSetting"
      label-width="105px"
      @search="searchHandle"
    ></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title" style="width:900px;">
          中国居民膳食营养素参考摄入量（孕妇与哺乳期在同龄人群参考值基础上额外增加量）
        </div>
        <div class="align-r">
          <button-icon color="origin" type="add" @click="addAndEditCrowd('add')">
            创建人群
          </button-icon>
          <!-- <button-icon color="plain" type="export" @click="gotoExport()">导出EXCEL</button-icon> -->
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
        >
          <el-table-column prop="id" label="编号" align="center"></el-table-column>
          <el-table-column prop="name" label="名称" align="center" width="200px">
            <template slot-scope="scope">
              <!-- <div class="table-text" v-if="!scope.row.editval"> -->
              <span>{{ scope.row.name }}</span>
              <!-- <i class="el-icon-edit ps-i" @click="iconItemClick(scope.row)"></i> -->
              <!-- </div> -->
              <!-- <div class="table-input" v-else>
                  <el-input
                    v-model="scope.row.name"
                    placeholder="请输入名称"
                    size="mini"
                    style="width:100px;"
                    class="ps-input"
                  ></el-input>
                  <el-button
                    style="margin-left:10px;min-width:40px;"
                    class="ps-btn"
                    size="mini"
                    @click="determineQuota(scope.row)"
                  >确定</el-button>
                </div> -->
            </template>
          </el-table-column>
          <el-table-column prop="group" label="人群" align="center"></el-table-column>
          <el-table-column prop="" label="年龄" align="center">
            <!-- eslint-disable-next-line vue/no-unused-vars -->
            <template slot-scope="scope">
              <!-- <el-button
                type="text"
                size="small"
                class="ps-text"
                @click="showAgeDialog = true"
              >
                【 添加年龄段 】
              </el-button> -->
              <span>{{ scope.row.min_age }}~{{ scope.row.max_age }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="" label="营养信息" align="center">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                class="ps-text"
                @click="clickShowDialogNutrition(scope.row)"
              >
                查看
              </el-button>
            </template>
          </el-table-column>
          <el-table-column prop="source" label="来源" align="center"></el-table-column>
          <el-table-column prop="account_username" label="创建人" align="center"></el-table-column>
          <el-table-column fixed="right" label="操作" width="180" align="center">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                class="ps-text"
                @click="addAndEditCrowd('edit', scope.row)"
              >
                编辑
              </el-button>
              <el-button
                type="text"
                class="ps-red"
                size="small"
                @click="deleteHaldler('del', scope.row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top: 20px">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100, 500]"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>
    <!-- 表中营养信息 -->
    <el-dialog title="膳食营养" :visible.sync="showDietDialog" custom-class="ps-dialog" width="50%">
      <el-table
        :data="tableDataNutrition"
        v-if="showDietDialog"
        ref="tableDataNutrition"
        style="width: 100%"
        border
      >
        <el-table-column
          prop=""
          label="中国居民常量和微量元素的参考摄入量(RNIS)或推荐摄入量(AI)、可耐最高摄入量(UL)"
          align="center"
        >
          <template>
            <column-item
              v-for="item in tableDataNutritionData"
              :key="item.label"
              :col="item"
              :showDialogNutritionDisabled="true"
            ></column-item>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
    <!-- <el-dialog title="添加年龄" :visible.sync="showAgeDialog" width="20%" custom-class="ps-dialog">
      <div class="age-box">
        <el-input v-model="ageForm.age1" class="input"></el-input>
        <span style="padding: 0 10px">~</span>
        <el-input v-model="ageForm.age2" class="input"></el-input>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button class="ps-cancel-btn" @click="showAgeDialog = false">取 消</el-button>
        <el-button class="ps-btn" type="primary" @click="showAgeDialog = false">确 定</el-button>
      </span>
    </el-dialog> -->
  </div>
</template>

<script>
import { debounce, to } from '@/utils'
import columnItem from '../components/ColumnItem' // 组件递归
import { dietCrowdData } from '../components/dietCrowdNutritionData'
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import exportExcel from '@/mixins/exportExcel' // 导出混入

export default {
  name: 'DietNutrition',
  components: { columnItem },
  // mixins: [activatedLoadData],
  mixins: [exportExcel],
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [],
      searchFormSetting: {
        name: {
          type: 'input',
          label: '名字',
          value: '',
          placeholder: '请输入名字'
        },
        group: {
          type: 'input',
          label: '人群',
          value: '',
          placeholder: '请输入人群'
        },
        min_age: {
          type: 'input',
          label: '最小年龄',
          value: '',
          placeholder: '请输入最小年龄'
        },
        max_age: {
          type: 'input',
          label: '最大年龄',
          value: '',
          placeholder: '请输入最大年龄'
        },
        gender: {
          type: 'select',
          label: '性别',
          value: '',
          placeholder: '请选择性别',
          collapseTags: true,
          listNameKey: 'name',
          listValueKey: 'value',
          dataList: [
            {
              name: '男',
              value: 'MAN'
            },
            {
              name: '女',
              value: 'WOMEN'
            },
            {
              name: '其他',
              value: 'OTHER'
            }
          ]
        }
      },
      showDietDialog: false,
      showAgeDialog: false,
      ageForm: {
        age1: '',
        age2: ''
      },
      tableDataNutrition: [],
      tableDataNutritionData: dietCrowdData
    }
  },
  created() {
    this.initLoad()
    this.initTableDataNutrition()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.currentPage = 1
      this.getFoodDietGroupList()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.currentPage = 1
      this.initLoad()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      // this.currentPage = 1
      this.initLoad()
    },
    initTableDataNutrition() {
      let obj = {}
      this.calleArr(dietCrowdData, obj, 1)
      this.tableDataNutrition.push(obj)
    },
    calleArr(arr, obj, deep, key) {
      for (var i = 0; i < arr.length; i++) {
        var data = arr[i]

        if (deep === 1) {
          key = data.key
        }

        if (data.children) {
          let deepNum = deep + 1
          // 是否还有下级
          this.calleArr(data.children, obj, deepNum, key) // 自己调用自己
        } else {
          data.prop = key + '_' + data.key
          obj[data.prop] = ''
        }
      }
    },
    // 人群列表
    async getFoodDietGroupList() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundFoodDietGroupListPost({
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.totalCount = res.data.count
        this.tableData = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value) {
          if (key !== 'select_date') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 删除人群
    async foodDietGroupDelete(row) {
      const [err, res] = await to(
        this.$apis.apiBackgroundFoodDietGroupDeletePost({
          ids: [row.id]
        })
      )
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.initLoad()
      } else {
        this.$message.error(res.msg)
      }
    },
    deleteHaldler(type, row) {
      let _this = this
      this.$confirm('是否删除该人群？', '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-warn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            _this.foodDietGroupDelete(row)
            done()
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.getFoodDietGroupList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val
      this.getFoodDietGroupList()
    },
    // determineQuota(data) {
    //   data.editval = !data.editval
    // },
    // iconItemClick(data) {
    //   data.editval = !data.editval
    // },
    clickShowDialogNutrition(row) {
      this.showDietDialog = true
      this.editRecurrence(row)
    },
    editRecurrence(data) {
      let element = JSON.parse(data.element)
      let vitamin = JSON.parse(data.vitamin)
      this.tableDataNutrition[0].energy_mj = data.energy_mj
      this.tableDataNutrition[0].energy_kcal = data.energy_kcal
      this.tableDataNutrition[0].protein_40 = data.protein
      this.tableDataNutrition[0].axunge_20 = data.axunge
      for (const key in element) {
        this.tableDataNutrition[0][key + '_' + 'AI'] = element[key].AI
        this.tableDataNutrition[0][key + '_' + 'UL'] = element[key].UL
      }
      for (const key in vitamin) {
        this.tableDataNutrition[0][key + '_' + 'AI'] = vitamin[key].AI
        this.tableDataNutrition[0][key + '_' + 'UL'] = vitamin[key].UL
      }
    },
    gotoExport() {
      this.$message.error('暂无导出')
      return
      const option = {
        type: 'ExportDietNutrition',
        params: {
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        }
      }
      this.exportHandle(option)
    },
    addAndEditCrowd(type, row) {
      this.$router.push({
        name: 'MerchantDietCrowd',
        query: { type: type, id: type === 'edit' ? row.id : '' }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.diet-nutrition {
  .table-input {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .age-box {
    .input {
      width: 130px;
    }
  }
}
</style>
