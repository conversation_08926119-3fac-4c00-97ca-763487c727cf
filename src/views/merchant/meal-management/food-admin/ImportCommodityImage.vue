<template>
  <div class="mul-import-img">
    <el-form class="import-form-wrapper" label-width="140px">
      <el-form-item label="菜品图片导入模板">
        <el-link
          class="origin"
          type="primary"
          :href="templateUrl"
        >
          点击下载
        </el-link>
      </el-form-item>
      <el-form-item label="上传菜品图片模板">
        <file-upload
          drag
          :data="uploadParams"
          :limit="limit"
          @fileLists="getSuccessUploadRes"
          :before-upload="beforeUpload"
          prefix="food_img_zip"
          :action="actionUrl"
          :on-remove="remove"
        >
          <div class="">
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">
              将文件拖到此处，或
              <em class="origin el-link">点击上传</em>
            </div>
          </div>
          <div class="el-upload__tip" slot="tip">只能上传zip文件</div>
        </file-upload>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" class="import-btn-wrapper ps-origin-btn" @click="mulImortFoodImg">
          确定
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import exportExcel from '@/mixins/exportExcel' // 导出混入

export default {
  mixins: [exportExcel],
  data() {
    return {
      limit: 1,
      actionUrl: '',
      uploadParams: {},
      uploadUrl: '',
      templateUrl: location.origin + "/api/temporary/template_excel/food_extra_image.zip"
    }
  },
  methods: {
    async getUploadToken() {
      const res = await this.$apis.getUploadToken({
        prefix: 'food_img_zip'
      })
      if (res.code === 0) {
        this.actionUrl = res.data.host
        this.uploadParams = {
          key: res.data.prefix + new Date().getTime() + Math.floor(Math.random() * 150),
          prefix: res.data.prefix,
          policy: res.data.policy,
          OSSAccessKeyId: res.data.accessid,
          signature: res.data.signature,
          callback: res.data.callback,
          success_action_status: '200'
        }
      } else {
        this.$message.error(res.msg)
      }
    },
    beforeUpload(file) {
      let reg = /application\/\S*zip\S*/
      if (!reg.test(file.type)) {
        this.$message.error('请上传后缀名为.zip的压缩包文件')
        return false
      }
    },
    remove() {
      this.uploadUrl = ''
    },
    getSuccessUploadRes(res) {
      // if (res.code === 0) {
      //   this.uploadUrl = res.data.public_url
      // }
      this.uploadUrl = res[0].url
    },
    mulImortFoodImg() {
      if (!this.uploadUrl) {
        this.$message.error('压缩包还没上传完毕或未上传')
        return
      }
      const option = {
        type: 'MulImportFoodImg',
        url: 'apiBackgroundFoodFoodFoodExtraImageBatAddPost',
        message: '确定导入？',
        params: {
          oss_url: this.uploadUrl
        }
      }
      this.exportHandle(option)
    }
  },
  created() {
    // this.getUploadToken()
  }
}
</script>

<style lang="scss">
.mul-import-img {
  margin-top: 20px;
  padding: 20px 0;
  background-color: #fff;
  border-radius: 12px;
  .origin.el-link{
    color: #FF9B45;
  }
  .import-form-wrapper {
    width: 500px;
  }
  .buttons {
    padding-bottom: 20px;
  }
  .import-btn-wrapper {
    width: 100%;
  }
}
</style>
