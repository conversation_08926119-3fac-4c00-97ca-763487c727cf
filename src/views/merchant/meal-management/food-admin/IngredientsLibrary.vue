<template>
  <div class="ingredients-wrapper container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form
      ref="searchRef"
      :form-setting="searchFormSetting"
      label-width="105px"
      @search="searchHandle"
    ></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" type="add" @click="clickOrganization('batchorgAnization')">
            批量应用组织
          </button-icon>
          <button-icon color="plain" type="mul" @click="clickcategoryAdmin">分类管理</button-icon>
          <button-icon color="plain" type="export" @click="gotoExport()">导出EXCEL</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
          @selection-change="handleSelectionChange"
        >
          <el-table-column
            type="selection"
            width="50"
            align="center"
            class-name="ps-checkbox"
          ></el-table-column>
          <el-table-column prop="id" label="序号" align="center"></el-table-column>
          <el-table-column prop="name" label="食材名称" align="center"></el-table-column>
          <el-table-column prop="sort_name" label="分类" align="center"></el-table-column>
          <el-table-column prop="name1" label="营养信息" align="center">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                class="ps-text"
                @click="clickShowDialogNutrition(scope.row)"
              >
                查看
              </el-button>
            </template>
          </el-table-column>
          <el-table-column
            prop="organization_name"
            label="创建来源"
            align="center"
          ></el-table-column>
          <el-table-column prop="name1" label="应用组织" align="center">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                class="ps-text"
                @click="clickOrganization('see', scope.row)"
              >
                查看
              </el-button>
            </template>
          </el-table-column>
          <el-table-column prop="create_time" label="创建时间" align="center"></el-table-column>
          <el-table-column fixed="right" label="操作" width="180" align="center">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                class="ps-text"
                @click="clickOrganization('editOperation', scope.row)"
              >
                应用组织
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- tree -->
        <!-- table end -->
      </div>
      <el-dialog
        title="应用组织"
        :visible.sync="showDialogStructure"
        width="30%"
        custom-class="ps-dialog"
      >
        <tree-select-ingredients
          :structureTree="structureTree"
          :organizationDisabled="organizationDisabled"
          @inputTree="inputTree"
          :structureData="structureData"
        ></tree-select-ingredients>
        <span slot="footer" class="dialog-footer">
          <el-button class="ps-cancel-btn" @click="showDialogStructure = false">取 消</el-button>
          <el-button class="ps-btn" type="primary" @click="determineOrganization">确 定</el-button>
        </span>
      </el-dialog>
      <!-- 表中营养信息 -->
      <el-dialog
        title="营养信息"
        :visible.sync="showDialogNutrition"
        width="50%"
        custom-class="ps-dialog"
      >
        <nutrition-data :tableDataNutrition="tableDataNutrition" />
      </el-dialog>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top: 20px">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100, 500]"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, to } from '@/utils'
import treeSelectIngredients from '../components/TreeSelect' // 菜品管理统一用一个tree
import nutritionData from '../components/mealFoodList/NutritionTable.vue'
export default {
  name: 'ingredientsLibrary',
  components: { 'tree-select-ingredients': treeSelectIngredients, nutritionData },
  props: {},
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [{ name1: 1 }],
      structureTree: [],
      structureData: [],
      showDialogStructure: false,
      searchFormSetting: {
        sort: {
          type: 'select',
          label: '分类',
          value: '',
          listNameKey: 'name',
          listValueKey: 'id',
          placeholder: '请选择分类',
          multiple: false,
          collapseTags: true,
          filterable: true,
          dataList: []
        },
        name: {
          type: 'input',
          label: '食材名称',
          value: '',
          placeholder: '请输入食材名称'
        }
      },
      showDialogNutrition: false,
      tableDataNutrition: [],
      organizationDisabled: false,
      structureType: '',
      dialogDataRow: {},
      selectListId: []
    }
  },
  created() {
    this.initLoad()
    this.getOrganizationTreeList()
    this.foodIngredientSortList()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.redientWarehouse()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.currentPage = 1
      this.initLoad()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
    },
    // 分类列表
    async foodIngredientSortList() {
      const [err, res] = await to(
        this.$apis.apiBackgroundFoodIngredientSortListPost({
          page: 1,
          page_size: 9999
        })
      )
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.searchFormSetting.sort.dataList = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    // 应用组织按钮
    clickOrganization(type, row) {
      if (type === 'batchorgAnization' && !this.selectListId.length) {
        return this.$message.error('请先选择数据！')
      }
      this.structureData = []
      this.showDialogStructure = true
      this.structureType = type
      if (type === 'see') {
        this.organizationDisabled = true
        this.structureData = row.use_organizations
      } else if (type === 'editOperation') {
        this.dialogDataRow = row
        row.use_organizations.map(item => {
          this.structureData.push(item.id)
        })
        this.organizationDisabled = false
      } else {
        this.organizationDisabled = false
      }
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '') {
          if (key !== 'select_date') {
            params[key] = data[key].value
          } else if (data[key].value && data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      return params
    },
    // 食材库列表
    async redientWarehouse() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundFoodIngredientWarehousePost({
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        console.log(res)
        this.totalCount = res.data.count
        this.tableData = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    // 修改组织
    async foodIngredientSync() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundFoodIngredientSyncPost({
          ids: this.structureType === 'editOperation' ? [this.dialogDataRow.id] : this.selectListId,
          organizations: this.structureData
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.showDialogStructure = false
        this.redientWarehouse()
      } else {
        this.$message.error(res.msg)
      }
    },
    confirmGrant() {},
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.redientWarehouse()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val
      this.redientWarehouse()
    },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {
      this.selectListId = []
      let data = Object.freeze(val) // 解除下监听吧，节约点资源
      data.map(item => {
        this.selectListId.push(item.id)
      })
    },
    determineOrganization() {
      if (this.structureType !== 'see') {
        this.foodIngredientSync()
      } else {
        this.showDialogStructure = false
      }
      // this.structureType
    },
    inputTree(val) {
      this.structureData = val
    },
    clickShowDialogNutrition(row) {
      this.showDialogNutrition = true
      this.tableDataNutrition = []
      let element = JSON.parse(row.nutrition.element)
      let vitamin = JSON.parse(row.nutrition.vitamin)
      let obj = {
        energy_mj: row.nutrition.energy_mj ? row.nutrition.energy_mj : 0,
        energy_kcal: row.nutrition.energy_kcal ? row.nutrition.energy_kcal : 0,
        protein: row.nutrition.protein ? row.nutrition.protein : 0,
        axunge: row.nutrition.axunge ? row.nutrition.axunge : 0,
        Ca: element.Ca ? element.Ca : 0,
        P: element.P ? element.P : 0,
        K: element.K ? element.K : 0,
        Na: element.Na ? element.Na : 0,
        Mg: element.Mg ? element.Mg : 0,
        Fe: element.Fe ? element.Fe : 0,
        I: element.I ? element.I : 0,
        Zn: element.Zn ? element.Zn : 0,
        Se: element.Se ? element.Se : 0,
        Cu: element.Cu ? element.Cu : 0,
        F: element.F ? element.F : 0,
        Cr: element.Cr ? element.Cr : 0,
        Mn: element.Mn ? element.Mn : 0,
        Mo: element.Mo ? element.Mo : 0,
        VA: vitamin.VA ? vitamin.VA : 0,
        VD: vitamin.VD ? vitamin.VD : 0,
        VE: vitamin.VE ? vitamin.VE : 0,
        VK: vitamin.VK ? vitamin.VK : 0,
        VB1: vitamin.VB1 ? vitamin.VB1 : 0,
        VB2: vitamin.VB2 ? vitamin.VB2 : 0,
        VB6: vitamin.VB6 ? vitamin.VB6 : 0,
        VB12: vitamin.VB12 ? vitamin.VB12 : 0,
        VC: vitamin.VC ? vitamin.VC : 0,
        VB5: vitamin.VB5 ? vitamin.VB5 : 0,
        VM: vitamin.VM ? vitamin.VM : 0,
        VB3: vitamin.VB3 ? vitamin.VB3 : 0,
        Choline: vitamin.Choline ? vitamin.Choline : 0,
        Nicotinamide: vitamin.Nicotinamide ? vitamin.Nicotinamide : 0,
        VH: vitamin.VH ? vitamin.VH : 0
      }
      this.tableDataNutrition = [obj]
      console.log(this.tableDataNutrition)
    },
    // 获取组织结构tree表
    async getOrganizationTreeList() {
      // this.treeLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundOrganizationOrganizationTreeListPost())
      // this.treeLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.structureTree = this.deleteEmptyGroup(res.data)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 处理下没有children_list
    deleteEmptyGroup(treeData) {
      let _that = this
      function traversal(data) {
        data.map(item => {
          if (item.children_list) {
            if (item.children_list.length > 0) {
              traversal(item.children_list)
            } else {
              _that.$delete(item, 'children_list')
            }
          } else {
            _that.$delete(item, 'children_list')
          }
        })
      }
      traversal(treeData)
      return treeData
    },
    gotoExport() {
      this.$message.error('暂无导出')
      return
      this.$confirm(`确定导出？`, '提示', {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true
      })
        .then(e => {
          this.$router.push({
            name: 'Excel',
            query: {
              // type: "AccountList",
              // params: JSON.stringify({
              //   id: this.searchForm.id,
              //   username: this.searchForm.username,
              //   member_name: this.searchForm.member_name,
              //   page: this.currentPage,
              //   page_size: this.pageSize,
              //   status: this.searchForm.status
              // })
            }
          })
        })
        .catch(e => {})
    },
    clickcategoryAdmin() {
      this.$router.push({
        name: 'MerchantIngredientsCategory'
      })
    }
  }
}
</script>
<style lang="scss">
.ingredients-wrapper {
}
</style>
