<template>
  <div class="ingredients-wrapper container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form
      ref="searchRef"
      :form-setting="searchFormSetting"
      label-width="110px"
      @search="searchHandle"
    ></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" type="add" @click="clickOrganization('batchorgAnization')">
            批量应用
          </button-icon>
          <button-icon color="plain" type="export" @click="gotoExport()">导出EXCEL</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
          @selection-change="handleSelectionChange"
        >
          <el-table-column
            type="selection"
            width="50"
            align="center"
            class-name="ps-checkbox"
          ></el-table-column>
          <el-table-column type="index" label="序号" align="center"></el-table-column>
          <el-table-column prop="id" label="菜品编号" align="center"></el-table-column>
          <el-table-column prop="" label="菜品 / 商品图片" align="center" width="200px">
            <template slot-scope="scope">
              <el-image
                v-if="scope.row.image"
                style="width: 150px; height: 100px"
                :src="scope.row.image"
                fit="contain"
              ></el-image>
            </template>
          </el-table-column>
          <el-table-column prop="name" label="食材名称" align="center"></el-table-column>
          <el-table-column prop="attributes" label="属性" align="center">
            <template slot-scope="scope">{{scope.row.attributes === 'goods' ? '商品' : '菜品'}}</template>
          </el-table-column>
          <el-table-column prop="" label="食材" align="center">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                class="ps-text"
                @click="clickShowDialogIngredients(scope.row)"
              >
                查看
              </el-button>
            </template>
          </el-table-column>
          <el-table-column prop="name1" label="营养信息" align="center">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                class="ps-text"
                @click="clickShowDialogNutrition(scope.row)"
              >
                查看
              </el-button>
            </template>
          </el-table-column>
          <el-table-column prop="" label="口味" align="center" width="150px">
            <template slot-scope="scope">
              <div class="tast-wrapper">
                <el-tag v-for="(item, index) in scope.row.taste_list" :key="index">
                  {{ item.name }}
                </el-tag>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="create_source_name"
            label="创建来源"
            align="center"
          ></el-table-column>
          <!-- <el-table-column prop="name1" label="应用组织" align="center">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                class="ps-text"
                @click="clickOrganization('see', scope.row)"
              >
                查看
              </el-button>
            </template>
          </el-table-column> -->
          <el-table-column prop="create_time" label="创建时间" align="center"></el-table-column>
          <el-table-column fixed="right" label="操作" width="180" align="center">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                class="ps-text"
                @click="clickOrganization('editOperation', scope.row)"
              >
                应用菜品
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- tree -->
        <!-- table end -->
      </div>
      <el-dialog
        title="应用组织"
        :visible.sync="showDialogStructure"
        width="30%"
        custom-class="ps-dialog"
      >
        <!-- <tree-select-ingredients
          :structureTree="structureTree"
          :organizationDisabled="organizationDisabled"
          @inputTree="inputTree"
          :structureData="structureData"
        ></tree-select-ingredients> -->
        <organization-select v-loading="isLoading" :only-child="false" :isLazy="false" :multiple="true" :checkStrictly="true" v-model="structureData" />
        <span slot="footer" class="dialog-footer">
          <el-button class="ps-cancel-btn" :disabled="isLoading" @click="showDialogStructure = false">取 消</el-button>
          <el-button class="ps-btn" type="primary" :disabled="isLoading" @click="determineOrganization">确 定</el-button>
        </span>
      </el-dialog>
      <!-- 表中营养信息 -->
      <el-dialog
        title="营养信息"
        :visible.sync="showDialogNutrition"
        width="700px"
        custom-class="ps-dialog"
      >
        <nutrition-data :tableDataNutrition="tableDataNutrition" />
      </el-dialog>
      <el-dialog
        title="食材组成"
        :visible.sync="showDialogIngredients"
        width="50%"
        custom-class="ps-dialog"
      >
        <el-table :data="tableDataIngredients" style="width: 100%">
          <el-table-column prop="ingredient_name" label="食材名称" align="center"></el-table-column>
          <el-table-column prop="ingredient_scale" label="占比" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.ingredient_scale }}%</span>
            </template>
          </el-table-column>
        </el-table>
      </el-dialog>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top: 20px">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100, 500]"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, to } from '@/utils'
import organizationSelect from '@/components/OrganizationSelect' // 菜品管理统一用一个tree
import nutritionData from '../components/mealFoodList/NutritionTable.vue'
import { NUTRITION_LIST } from './constants'
import exportExcel from '@/mixins/exportExcel' // 导出混入
export default {
  name: 'ingredientsCommodity',
  mixins: [exportExcel],
  components: { organizationSelect, nutritionData },
  props: {},
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [],
      structureTree: [],
      structureData: [],
      showDialogStructure: false,
      searchFormSetting: {
        select_time: {
          type: 'datetimerange',
          format: 'yyyy-MM-dd HH:mm:ss',
          label: '创建时间',
          value: []
        },
        food_name: {
          type: 'input',
          label: '菜品 / 商品名称',
          value: '',
          placeholder: '请输入菜品 / 商品名称'
        },
        create_source: {
          type: 'organizationSelect',
          label: '创建来源',
          multiple: false,
          checkStrictly: true,
          isLazy: false,
          value: '',
          placeholder: '请选择创建来源',
          dataList: [{
            company: 0,
            create_time: "2022-01-25 09:49:26",
            has_children: false,
            id: -1,
            level: -1,
            level_name: "",
            level_tag: -1,
            name: "系统",
            parent: null,
            status: "enable",
            status_alias: "正常",
            tree_id: -1
          }]
        },
        attributes: {
          type: 'select',
          label: '属性',
          value: '',
          placeholder: '请选择属性',
          dataList: [{
            label: '商品',
            value: 'goods'
          }, {
            label: '菜品',
            value: 'foods'
          }]
        }
      },
      showDialogNutrition: false,
      tableDataNutrition: {},
      organizationDisabled: false,
      structureType: '',
      dialogDataRow: {},
      selectListId: [],
      showDialogIngredients: false,
      tableDataIngredients: []

    }
  },
  created() {
    this.initLoad()
    this.getOrganizationTreeList()
    // this.foodIngredientSortList()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.redientWarehouse()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.currentPage = 1
      this.initLoad()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
    },
    // 分类列表
    async foodIngredientSortList() {
      const [err, res] = await to(
        this.$apis.apiBackgroundFoodIngredientSortListPost({
          page: 1,
          page_size: 9999
        })
      )
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.searchFormSetting.sort.dataList = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    // 应用组织按钮
    clickOrganization(type, row) {
      if (type === 'batchorgAnization' && !this.selectListId.length) {
        return this.$message.error('请先选择数据！')
      }
      this.structureData = []
      this.showDialogStructure = true
      this.structureType = type
      if (type === 'see') {
        this.organizationDisabled = true
        this.structureData = row.use_organizations ? row.use_organizations.map(v => v.id) : []
      } else if (type === 'editOperation') {
        this.dialogDataRow = row
        row.use_organizations.map(item => {
          this.structureData.push(item.id)
        })
        this.organizationDisabled = false
      } else {
        this.organizationDisabled = false
      }
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '') {
          if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value && data[key].value.length > 0) {
            params.start_time = data[key].value[0]
            params.end_time = data[key].value[1]
          }
        }
      }
      return params
    },
    // 食材库列表
    async redientWarehouse() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundFoodFoodStockListPost({
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        console.log(res)
        this.totalCount = res.data.count
        this.tableData = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    // 修改组织
    async foodIngredientSync() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundFoodFoodStockDistributePost({
          food_ids: this.structureType === 'editOperation' ? [this.dialogDataRow.id] : this.selectListId,
          organizations: this.structureData
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.showDialogStructure = false
        this.$message.success(res.msg)
        this.redientWarehouse()
      } else {
        this.$message.error(res.msg)
      }
    },
    confirmGrant() {},
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.redientWarehouse()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val
      this.redientWarehouse()
    },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {
      this.selectListId = []
      let data = Object.freeze(val) // 解除下监听吧，节约点资源
      data.map(item => {
        this.selectListId.push(item.id)
      })
    },
    determineOrganization() {
      if (this.structureType !== 'see') {
        this.foodIngredientSync()
      } else {
        this.showDialogStructure = false
      }
      // this.structureType
    },
    inputTree(val) {
      this.structureData = val
    },
    clickShowDialogIngredients(row) {
      this.showDialogIngredients = true
      this.tableDataIngredients = row.ingredients_list
    },
    clickShowDialogNutrition(row) {
      this.showDialogNutrition = true
      this.tableDataNutrition = {}
      if (!row.nutrition_info) row.nutrition_info = {}
      let element = row.nutrition_info.element ? JSON.parse(row.nutrition_info.element) : {}
      let vitamin = row.nutrition_info.vitamin ? JSON.parse(row.nutrition_info.vitamin) : {}
      NUTRITION_LIST.forEach(nutrition => {
        if (nutrition.type === 'default') {
          this.$set(this.tableDataNutrition, nutrition.key, row.nutrition_info[nutrition.key] ? row.nutrition_info[nutrition.key] : 0)
        }
        if (nutrition.type === 'element') {
          this.$set(this.tableDataNutrition, nutrition.key, element[nutrition.key] ? element[nutrition.key] : 0)
        }
        if (nutrition.type === 'vitamin') {
          this.$set(this.tableDataNutrition, nutrition.key, vitamin[nutrition.key] ? vitamin[nutrition.key] : 0)
        }
      })
    },
    // 获取组织结构tree表
    async getOrganizationTreeList() {
      // this.treeLoading = true
      const [err, res] = await to(this.$apis.apiBackgroundOrganizationOrganizationTreeListPost())
      // this.treeLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.structureTree = this.deleteEmptyGroup(res.data)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 处理下没有children_list
    deleteEmptyGroup(treeData) {
      let _that = this
      function traversal(data) {
        data.map(item => {
          if (item.children_list) {
            if (item.children_list.length > 0) {
              traversal(item.children_list)
            } else {
              _that.$delete(item, 'children_list')
            }
          } else {
            _that.$delete(item, 'children_list')
          }
        })
      }
      traversal(treeData)
      return treeData
    },
    gotoExport() {
      const option = {
        type: 'ingredientsCommodity',
        params: {
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        }
      }
      this.exportHandle(option)
    }
  }
}
</script>
<style lang="scss">
.ingredients-wrapper {
}
</style>
