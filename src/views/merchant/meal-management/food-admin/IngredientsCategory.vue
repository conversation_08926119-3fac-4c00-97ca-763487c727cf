<template>
  <div class="ingredients-wrapper container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <search-form
      ref="searchRef"
      :form-setting="searchFormSetting"
      label-width="105px"
      @search="searchHandle"
    ></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <!-- <button-icon color="origin" type="add" @click="openDialogHaldler('add')">
            新建分类
          </button-icon> -->
          <!-- <button-icon color="plain" type="mul" @click="deleteHaldler('delBatch')">
            批量删除
          </button-icon> -->
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
          @selection-change="handleSelectionChange"
        >
          <!-- <el-table-column
            type="selection"
            width="50"
            align="center"
            class-name="ps-checkbox"
          ></el-table-column> -->
          <el-table-column prop="id" label="序号" align="center"></el-table-column>
          <el-table-column prop="category_name" label="一级分类" align="center"></el-table-column>
          <el-table-column prop="name" label="二级分类" align="center"></el-table-column>
          <!-- <el-table-column prop="create_time" label="创建时间" align="center"></el-table-column>
          <el-table-column fixed="right" label="操作" width="180" align="center">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                class="ps-text"
                @click="openDialogHaldler('edit', scope.row)"
              >
                编辑
              </el-button>
              <el-button
                type="text"
                size="small"
                class="ps-warn"
                @click="deleteHaldler('delSingle', scope.row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column> -->
        </el-table>
        <!-- tree -->
        <!-- table end -->
      </div>
      <el-dialog
        :title="dialogTitle"
        :visible.sync="showDialogCategory"
        width="320px"
        top="20vh"
        custom-class="ps-dialog"
        :close-on-click-modal="false"
      >
        <el-form
          ref="formData"
          v-loading="formLoading"
          :rules="formDataRuls"
          :model="formData"
          class="dialog-form"
        >
          <el-form-item label="分类名称" prop="categoryName">
            <el-input class="ps-input" v-model="formData.categoryName"></el-input>
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button class="ps-cancel-btn" @click="showDialogCategory = false">取 消</el-button>
          <el-button
            class="ps-btn"
            type="primary"
            :loading="formLoading"
            @click="determineDialogCategory"
          >
            确 定
          </el-button>
        </span>
      </el-dialog>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top: 20px">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100, 500]"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, to } from '@/utils'
export default {
  name: 'IngredientsCategory',
  props: {},
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [{ name1: 1 }],
      searchFormSetting: {
        sort_id: {
          type: 'treeselect',
          multiple: false,
          flat: false,
          label: '分类',
          value: null,
          placeholder: '请选择分类',
          dataList: [],
          limit: 1,
          level: 1,
          normalizer: node => ({
            id: node.level + '_' + node.id,
            label: node.name,
            children: node.sort_list
          })
        }
      },
      showDialogCategory: false,
      formData: {
        categoryName: ''
      },
      formDataRuls: {
        categoryName: [{ required: true, message: '请分类名字', trigger: 'blur' }]
      },
      dialogTitle: '',
      dialogType: '',
      formLoading: false,
      categoryFormRow: {},
      delType: '',
      selectListId: []
    }
  },
  created() {
    this.getCategoryCategoryNameList()
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.currentPage = 1
      this.foodIngredientSortList()
    },
    // 节下流咯
    searchHandle: debounce(function () {
      // this.currentPage = 1
      this.initLoad()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      // this.currentPage = 1
      this.initLoad()
    },
    // 获取所有一级食材以及下面的二级食材
    async getCategoryCategoryNameList() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundFoodIngredientSortCategoryNameListPost()
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.searchFormSetting.sort_id.dataList = this.deleteEmptyGroup(res.data)
      } else {
        this.$message.error(res.msg)
      }
    },
    // 处理下没有children_list
    deleteEmptyGroup(treeData) {
      let _that = this
      function traversal(data) {
        data.map(item => {
          if (item.sort_list) {
            if (item.sort_list.length > 0) {
              traversal(item.sort_list)
            } else {
              _that.$delete(item, 'sort_list')
            }
          } else {
            _that.$delete(item, 'sort_list')
          }
        })
      }
      traversal(treeData)
      return treeData
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value || (data[key].value && data[key].value.length)) {
          if (key !== 'select_time') {
            // 因为要区分一级和二级id 字段不同 如果是一级就用category_id 二级sort_id 目前只有二级分类
            if (key === 'sort_id') {
              data[key].dataList.map(v => {
                if (data[key].value.split('_')[0] === '1') {
                  params.category_id = Number(data[key].value.split('_')[1])
                } else {
                  if (data[key].value.split('_')[0] === '2') {
                    params.sort_id = Number(data[key].value.split('_')[1])
                  }
                }
              })
            } else {
              params[key] = data[key].value
            }
          } else if (data[key].value.length > 0) {
            params.start_time = data[key].value[0]
            params.end_time = data[key].value[1]
          }
        }
      }
      return params
    },
    // 分类列表
    async foodIngredientSortList() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundFoodIngredientSortListPost({
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.totalCount = res.data.count
        this.tableData = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    // 新增分类
    async foodIngredientSortAdd() {
      this.formLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundFoodIngredientSortAddPost({
          name: this.formData.categoryName
        })
      )
      this.formLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.showDialogCategory = false
        this.foodIngredientSortList()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 编辑分类
    async foodIngredientSortModify() {
      this.formLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundFoodIngredientSortModifyPost({
          id: this.categoryFormRow.id,
          name: this.formData.categoryName
        })
      )
      this.formLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.showDialogCategory = false
        this.foodIngredientSortList()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 删除分类
    async foodIngredientSortDelete(row) {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundFoodIngredientSortDeletePost({
          ids: this.delType === 'delSingle' ? [row.id] : this.selectListId
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.searchHandle()
      } else {
        this.$message.error(res.msg)
      }
    },
    openDialogHaldler(type, row) {
      this.dialogType = type
      if (type === 'add') {
        this.dialogTitle = '新建分类'
        this.formData.categoryName = ''
      } else {
        this.dialogTitle = '编辑分类'
        this.categoryFormRow = row
        this.formData.categoryName = row.name
      }
      this.showDialogCategory = true
    },
    deleteHaldler(type, row) {
      if (type === 'delBatch' && !this.selectListId.length) {
        return this.$message.error('请先选择数据！')
      }
      let _this = this
      this.delType = type
      this.$confirm('是否删除该分类？', '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-warn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            _this.foodIngredientSortDelete(row)
            done()
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    determineDialogCategory() {
      this.$refs.formData.validate(valid => {
        if (valid) {
          if (this.dialogType === 'add') {
            this.foodIngredientSortAdd()
          } else if (this.dialogType === 'edit') {
            this.foodIngredientSortModify()
          }
        } else {
          return false
        }
      })
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.foodIngredientSortList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val
      this.foodIngredientSortList()
    },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {
      this.selectListId = []
      let data = Object.freeze(val) // 解除下监听吧，节约点资源
      data.map(item => {
        this.selectListId.push(item.id)
      })
    }
  }
}
</script>
<style lang="scss">
.ingredients-wrapper {
}
</style>
