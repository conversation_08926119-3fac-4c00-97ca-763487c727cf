<template>
  <div class="set_meal_classify container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" type="add" @click="clickShowDialogLabel('addCategory')">
            添加分类
          </button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          stripe
          header-row-class-name="ps-table-header-row"
        >
          <el-table-column type="index" label="序号" width="100" align="center" key="index">
            <template slot-scope="scope">
              <div v-text="(currentPage - 1) * pageSize + 1 + scope.$index"></div>
            </template>
          </el-table-column>
          <el-table-column prop="name" label="分类名称" align="center"></el-table-column>
          <el-table-column prop="operator_name" label="创建人" align="center"></el-table-column>
          <el-table-column fixed="right" label="操作" width="180" align="center">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                class="ps-text"
                @click="clickShowDialogLabel('editCategory', scope.row)"
              >
                编辑
              </el-button>
              <el-button type="text" size="small" class="ps-warn" @click="clickDelete(scope.row)">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- table end -->
      </div>
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100, 500]"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
      <!-- 分页 end -->
      <el-dialog
        :title="dialogLabelTitle"
        :visible.sync="showDialogCategory"
        width="400px"
        custom-class="ps-dialog"
        @close="handleClose"
      >
        <el-form
          @submit.native.prevent
          status-icon
          ref="dialogCategoryForm"
          v-loading="dialogLoading"
          :rules="dialogFormDataRuls"
          :model="dialogFormData"
          label-width="80px"
        >
          <el-form-item label="分类" prop="name">
            <el-input
              class="ps-input"
              style="width: 190px"
              placeholder="请输入分类名称"
              v-model="dialogFormData.name"
              maxlength="15"
            ></el-input>
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button @click="showDialogCategory = false">取 消</el-button>
          <el-button
            class="ps-btn"
            type="primary"
            :loading="dialogLoading"
            @click="determineCategoryDialog"
          >
            确 定
          </el-button>
        </span>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { debounce, to } from '@/utils'
export default {
  name: 'SetMealAdmin',
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      tableData: [],
      dialogType: '',
      dialogLoading: false,
      showDialogCategory: false,
      dialogLabelTitle: '',
      dialogFormData: {
        id: '',
        name: ''
      },
      dialogFormDataRuls: {
        name: [{ required: true, message: '分类名称不能为空', trigger: 'blur' }]
      }
    }
  },
  created() {
    this.initLoad()
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getSetMealCategoryList()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.currentPage = 1
      this.getSetMealCategoryList()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      this.currentPage = 1
      this.initLoad()
    },
    // 分类列表
    async getSetMealCategoryList() {
      this.isLoading = true
      const [err, res] = await to(
        this.$apis.apiBackgroundFoodSetMealCategoryListPost({
          page: this.currentPage,
          page_size: this.pageSize
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.totalCount = res.data.count
        this.tableData = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    clickDelete(row) {
      this.$confirm('确定删除？', '提示', {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-warn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            this.isLoading = true
            // await this.$sleep(1000);
            const [err, res] = await to(this.$apis.apiBackgroundFoodSetMealCategoryDeletePost({
              id: row.id
            }))
            this.isLoading = false
            instance.confirmButtonLoading = false
            done()
            if (err) {
              this.$message.error(err.message)
              return
            }
            if (res.code === 0) {
              this.$message.success(res.msg)
              this.getSetMealCategoryList()
            } else {
              this.$message.error(res.msg)
            }
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    clickShowDialogLabel(type, row) {
      this.dialogType = type
      this.showDialogCategory = true
      if (type === 'addCategory') {
        this.dialogLabelTitle = '新增分类'
        this.dialogFormData = {
          name: ''
        }
      } else if (type === 'editCategory') {
        this.dialogLabelTitle = '编辑分类'
        this.dialogFormData = {
          id: row.id,
          name: row.name
        }
      }
    },
    // 添加
    async addModifySetMealCategory(params) {
      this.dialogLoading = true
      let [err, res] = ''
      if (this.dialogType === 'addCategory') {
        ;[err, res] = await to(this.$apis.apiBackgroundFoodSetMealCategoryAddPost(params))
      } else {
        ;[err, res] = await to(this.$apis.apiBackgroundFoodSetMealCategoryModifyPost(params))
      }
      this.dialogLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.showDialogCategory = false
        this.getSetMealCategoryList()
      } else {
        this.$message.error(res.msg)
      }
    },

    determineCategoryDialog() {
      if (this.dialogLoading) return this.$message.error('请勿重复提交！')
      this.$refs.dialogCategoryForm.validate(async valid => {
        if (valid) {
          this.addModifySetMealCategory(this.dialogFormData)
        }
      })
    },
    handleClose() {
      this.$refs.dialogCategoryForm.resetFields()
    },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val
      this.getSetMealCategoryList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val
      this.getSetMealCategoryList()
    }
  }
}
</script>

<style lang="scss" scoped>
.set_meal_classify {
}
</style>
