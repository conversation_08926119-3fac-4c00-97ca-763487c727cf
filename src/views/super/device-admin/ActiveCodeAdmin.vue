<template>
  <div class="ActiveCodeAdmin container-wrapper">
    <refresh-tool @refreshPage="refreshHandle" />
    <!-- search start -->
    <search-form ref="searchRef" v-loading="isLoading" :form-setting="searchFormSetting" @search="searchHandle"></search-form>
    <!-- search end -->
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <button-icon color="origin" type="" @click="openDeviceDialog('mul_activate_time')">批量编辑有效期</button-icon>
          <button-icon color="origin" type="" @click="gotoExport">导出激活码</button-icon>
          <button-icon color="origin" type="" @click="importDeviceCode">导入激活码</button-icon>
          <button-icon color="origin" type="add" @click="openDeviceDialog('add')">新建</button-icon>
          <button-icon color="plain" type="mul" @click="mulOperation('mulOpen')">批量启用</button-icon>
          <button-icon color="plain" type="mul" @click="mulOperation('mulClose')">批量禁用</button-icon>
          <button-icon color="plain" type="" @click="openDeviceDialog('weigh')">称重模块激活码</button-icon>
        </div>
      </div>
      <div class="table-content">
        <!-- table start -->
        <u-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          style="width: 100%"
          use-virtual
          :row-height="30"
          :height="600"
          :big-data-checkbox="true"
          row-key="device_no"
          header-row-class-name="ps-table-header-row"
          class="table-data"
          @selection-change="handleSelectionChange"
        >
          <u-table-column type="selection" width="50" class-name="ps-checkbox"></u-table-column>
          <u-table-column prop="activation_code" label="激活码" width="120"></u-table-column>
          <u-table-column label="二维码（激活）" align="center">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                class="ps-bule"
                @click="openDeviceDialog('code', scope.row)"
                >查看</el-button>
            </template>
          </u-table-column>
          <u-table-column prop="device_name" label="设备名" width="120">
            <template slot-scope="scope">
              <span>{{ scope.row.device_name }}</span>
              <i
                class="el-icon-edit ps-i"
                @click="openDeviceDialog('name', scope.row)"
              ></i>
            </template>
          </u-table-column>
          <u-table-column prop="device_mac" label="MAC地址"></u-table-column>
          <u-table-column prop="company_name" label="所属项目"></u-table-column>
          <u-table-column prop="consumer_name" label="所属组织" width="90"></u-table-column>
          <u-table-column prop="device_type_alias" label="设备类型"></u-table-column>
          <u-table-column prop="activation_status" label="设备状态">
            <template slot-scope="scope">
              <span>{{ scope.row.activation_status?'已激活':'未激活' }}</span>
            </template>
          </u-table-column>
          <u-table-column prop="device_model_alias" label="设备型号" show-overflow-tooltip width="140"></u-table-column>
          <u-table-column prop="serial_no" label="SN码"></u-table-column>
          <u-table-column prop="offline_face_activation_code" label="离线人脸激活码"></u-table-column>
          <u-table-column prop="offline_face_activation_status_type" label="离线人脸激活状态"></u-table-column>
          <u-table-column prop="create_user" label="创建人"></u-table-column>
          <u-table-column prop="create_time" label="创建时间" width="150"></u-table-column>
          <u-table-column prop="using" label="启用/禁用">
            <template slot-scope="scope">
              <el-switch v-model="scope.row.using" @change="mulOperation('oneUsing', scope.row)"  active-color="#ff9b45" inactive-color="#ffcda2"></el-switch>
            </template>
          </u-table-column>
          <u-table-column prop="activate_time" label="有效期" width="170">
            <template slot-scope="scope">
              <div v-if="!scope.row.isEffective">
                已失效
              </div>
              <div v-else>
                <div>生效时间：{{scope.row.effective}}</div>
                <div>失效时间：{{scope.row.expiration}}</div>
              </div>
            </template>
          </u-table-column>
          <u-table-column label="操作" width="180">
            <template slot-scope="scope">
              <!-- <el-button
                type="text"
                size="small"
                style="color: #606266;"
                v-if="scope.row.device_type === 'QCG'"
                @click="gotoDeviceCeilSet(scope.row)"
                >设置</el-button> -->
              <el-button
                type="text"
                size="small"
                class="ps-text"
                @click="openDeviceDialog('detail', scope.row)"
                >详情</el-button>
              <el-button
                type="text"
                size="small"
                class="ps-bule"
                @click="openDeviceDialog('getlog', scope.row)"
                >拉取日志</el-button>
                <el-button
                type="text"
                size="small"
                class="ps-bule"
                v-if="scope.row.isEffective && !scope.row.isOverLastDay"
                @click="mulOperation('expiration', scope.row)"
                >失效</el-button>
              <el-button
                type="text"
                size="small"
                class="ps-bule"
                v-else
                @click="openDeviceDialog('effective', scope.row)"
                >生效</el-button>
              <el-button
                type="text"
                size="small"
                class="ps-red"
                @click="mulOperation('del', scope.row)"
                >删除</el-button>
            </template>
          </u-table-column>
        </u-table>
        <!-- table end -->
      </div>
      <!-- <div class="select-all m-l-20">
        <el-checkbox class="ps-checkbox" v-model="selectAll" @change="selectAllHandle">选择全部</el-checkbox>
        <span v-if="selectAll" class="select-count">（已选中{{totalCount}}条）</span>
      </div> -->
      <!-- 分页 start -->
      <div class="block ps-pagination" style="text-align: right; padding-top:20px;">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100, 200, 500, 1000, 2000]"
          :page-size="pageSize"
          layout="total, prev, pager, next, sizes, jumper"
          :total="totalCount"
          background
          class="ps-text"
          popper-class="ps-popper-select"
        ></el-pagination>
      </div>
      <!-- 分页 end -->
    </div>
    <device-dialog
      :isshow.sync="deviceDialogVisible"
      :title="deviceDialogTitle"
      :type="deviceDialogType"
      :width="deviceDialogWidth"
      :show-footer="deviceFooter"
      :deviceInfo="deviceInfo"
      :deviceTypeList="searchFormSetting.device_type.dataList"
      :selectList="selectListId"
      :min-time="selectMaxTime"
      @confirm="searchHandle"
    />
  </div>
</template>

<script>
// import activatedLoadData from '@/mixins/activatedLoadData' // 看需要食用
import { debounce, getTreeChildArr } from '@/utils'
import deviceDialog from './components/DeviceDialog.vue'
import exportExcel from '@/mixins/exportExcel' // 导出混入

export default {
  name: 'ActiveCodeAdmin',
  components: { deviceDialog },
  props: {},
  // mixins: [activatedLoadData],
  mixins: [exportExcel],
  data() {
    return {
      isLoading: false, // 刷新数据
      pageSize: 10, // 每页数量
      totalCount: 0, // 总条数
      currentPage: 1, // 第几页
      allTableData: [],
      tableData: [],
      searchFormSetting: {
        activation_code: {
          type: 'input',
          label: '激活码',
          value: '',
          placeholder: '请输入激活码'
        },
        device_mac: {
          type: 'input',
          label: 'MAC地址',
          value: '',
          placeholder: '请输入MAC地址'
        },
        company_ids: {
          type: 'select',
          multiple: true,
          filterable: true,
          collapseTags: true,
          label: '所属项目',
          value: [],
          placeholder: '请选择项目点',
          listNameKey: 'name',
          listValueKey: 'id',
          dataList: []
        },
        organization_ids: {
          type: 'organizationSelect',
          multiple: true,
          checkStrictly: true,
          label: '消费点',
          value: [],
          placeholder: '请选择消费点',
          role: 'super',
          orgs: []
          // type: 'treeselect',
          // multiple: false,
          // flat: false,
          // label: '消费点',
          // value: null,
          // placeholder: '请选择消费点',
          // dataList: [],
          // limit: 1,
          // level: 1,
          // normalizer: this.consumeNode
        },
        activation_status: {
          type: 'select',
          label: '状态',
          value: '',
          placeholder: '请选择状态',
          dataList: [{
            label: '全部',
            value: ''
          }, {
            label: '未激活',
            value: false
          }, {
            label: '已激活',
            value: true
          }]
        },
        device_type: {
          type: 'select',
          label: '设备类型',
          value: '',
          placeholder: '请选择设备类型',
          listNameKey: 'name',
          listValueKey: 'key',
          dataList: []
        },
        device_name: {
          type: 'input',
          label: '设备名',
          value: '',
          placeholder: '请输入设备名'
        },
        using: {
          type: 'select',
          label: '启用/禁用',
          value: '',
          placeholder: '请选择设备类型',
          dataList: [{
            label: '全部',
            value: ''
          }, {
            label: '启用',
            value: true
          }, {
            label: '禁用',
            value: false
          }]
        },
        sn_code: {
          type: 'input',
          label: 'SN码',
          value: '',
          placeholder: '请输入SN码'
        },
        offline_face_activation_code: {
          labelWidth: "100",
          type: 'input',
          label: '离线人脸激活码',
          value: '',
          placeholder: '请输入离线人脸激活码'
        }
      },
      deviceDialogVisible: false,
      deviceDialogTitle: '',
      deviceDialogType: '',
      deviceDialogWidth: '',
      deviceFooter: true,
      deviceInfo: {},
      selectListId: [],
      selectMaxTime: '', // 选中数据的最大的激活码日期
      selectAll: false
    }
  },
  created() {
    this.initLoad()
  },
  watch: {
    'searchFormSetting.company_ids.value': function(val) {
      this.searchFormSetting.organization_ids.orgs = val
      let arr = []
      val.map(com => {
        arr = arr.concat(getTreeChildArr(this.searchFormSetting.company_ids.dataList, com, { key: 'id', childkey: 'children_list' }))
      })
      this.searchFormSetting.organization_ids.value.map(org => {
        if (arr.indexOf(org) === -1) {
          this.searchFormSetting.organization_ids.value.splice(this.searchFormSetting.organization_ids.value.indexOf(org), 1)
        }
      })
    }
  },
  mounted() {},
  methods: {
    initLoad() {
      this.getDeviceList()
      this.getDeviceType()
      this.getConsumeList()
    },
    // 节下流咯
    searchHandle: debounce(function() {
      this.currentPage = 1;
      this.deviceDialogVisible = false
      this.getDeviceList()
    }, 300),
    // 刷新页面
    refreshHandle() {
      // 搜索重置
      this.$refs.searchRef.resetForm()
      // this.currentPage = 1;
      // this.tableData = []
      this.initLoad()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_date') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      if (params.company_ids && params.company_ids.length) { // 选择了项目点，做下处理
        if (!params.organization_ids) { // 只选了项目点，把所有项目点下面的组织id拿到
          params.organization_ids = []
          params.company_ids.map(item => {
            let arr = []
            arr = getTreeChildArr(this.searchFormSetting.company_ids.dataList, item, { key: 'id', childkey: 'children_list' })
            params.organization_ids = params.company_ids.concat(arr)
          })
        }
      }
      delete params.company_ids
      return params
    },
    async getDeviceList() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundAdminDeviceListPost({
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      })
      this.isLoading = false
      if (res.code === 0) {
        this.tableData = res.data.results
        this.tableData.map(item => {
          if (item.activate_time.split(' ')[0]) {
            item.isEffective = true
            item.effective = item.activate_time.split(' ')[0]
            item.expiration = item.activate_time.split(' ')[3]
            let today = new Date()
            let expiration = new Date(item.activate_time.split(' ')[3])
            if (today - expiration > 0) {
              item.isOverLastDay = true
            } else {
              item.isOverLastDay = false
            }
          } else {
            item.isEffective = false
          }
          return item
        })
        // this.allTableData = res.data.results
        // this.setSelCurrentPageData()
        this.totalCount = res.data.count
      } else {
        this.$message.error(res.msg)
      }
    },
    // setSelCurrentPageData() {
    //   let start = (this.currentPage - 1) * this.pageSize
    //   let end =
    //     (this.currentPage - 1) * this.pageSize +
    //     this.pageSize
    //   this.totalCount = this.allTableData.length
    //     ? this.allTableData.length
    //     : 0
    //   this.tableData = [].concat(
    //     this.allTableData.slice(start, end)
    //   )
    //   this.tableData.map(item => {
    //     if (item.activate_time.split(' ')[0]) {
    //       item.isEffective = true
    //       item.effective = item.activate_time.split(' ')[0]
    //       item.expiration = item.activate_time.split(' ')[3]
    //       let today = new Date()
    //       let expiration = new Date(item.activate_time.split(' ')[3])
    //       if (today - expiration > 0) {
    //         item.isOverLastDay = true
    //       } else {
    //         item.isOverLastDay = false
    //       }
    //     } else {
    //       item.isEffective = false
    //     }
    //     return item
    //   })
    // },
    // 分页页数change事件
    handleSizeChange(val) {
      this.pageSize = val;
      this.getDeviceList()
    },
    // 分页页码change事件
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getDeviceList()
    },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {
      this.selectListId = []
      let data = Object.freeze(val) // 解除下监听吧，节约点资源
      let time = ''
      data.map((item, i) => {
        this.selectListId.push(item.device_no)
        // if (i === 0) {
        //   time = new Date(item.expiration.replace(new RegExp(/-/gm), '/')).getTime()
        // } else {
        //   let time2 = new Date(item.expiration.replace(new RegExp(/-/gm), '/')).getTime()
        //   if (time < time2) {
        //     time = time2
        //   }
        // }
      })
      this.selectMaxTime = time
    },

    selectAllHandle() {
      this.selectListId = []
      let time = ''
      this.allTableData.map((item, i) => {
        this.selectListId.push(item.device_no)
        // if (i === 0) {
        //   time = new Date(item.expiration.replace(new RegExp(/-/gm), '/')).getTime()
        // } else {
        //   let time2 = new Date(item.expiration.replace(new RegExp(/-/gm), '/')).getTime()
        //   if (time < time2) {
        //     time = time2
        //   }
        // }
      })
      this.selectMaxTime = time
    },
    // 批量操作 type表示类型，text是弹窗文字，num表示批量或者单个操作，data是单个操作的时候带过去的数据
    mulOperation(type, data) {
      if (!data && !this.selectListId.length) {
        return this.$message.error('请先选择数据！')
      }
      let title = '提示'
      let content = ''
      switch (type) {
        case "mulOpen":
          content = '确定批量启用所选激活码？'
          break;
        case "mulClose":
          content = '确定批量禁用所选激活码？'
          break;
        case "oneUsing":
          if (data.using) {
            content = '确定启用该激活码？'
          } else {
            content = '确定禁用该激活码？'
          }
          break;
        case "del":
          content = '确定删除该激活码？'
          break;
        case "expiration":
          content = '确定使该激活码失效？'
          break;
      }
      this.$confirm(`${content}`, `${title}`, {
        confirmButtonText: this.$t('dialog.confirm_btn'),
        cancelButtonText: this.$t('dialog.cancel_btn'),
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-cancel-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            let params = {}
            switch (type) {
              case "mulOpen":
                params.device_nos = this.selectListId
                params.choices = 1
                this.mulModifyDevice(params)
                break;
              case "mulClose":
                params.device_nos = this.selectListId
                params.choices = 2
                this.mulModifyDevice(params)
                break;
              case "oneUsing":
                params.device_no = data.device_no
                params.using = data.using
                this.modifyDevice(params)
                break;
              case "del":
                params.device_nos = [data.device_no]
                params.choices = 3
                this.mulModifyDevice(params)
                break;
              case "expiration":
                params.device_no = data.device_no
                params.deactivate = true
                this.modifyDevice(params)
                break;
            }
            done()
            // 删除，当不是第一页时并且当前是最后一页，要将页码重置下
            if (this.currentPage > 1 && this.tableData.length === 1) {
              this.currentPage--
            }
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
              if (type === "oneUsing") {
                data.using = !data.using
              }
            }
          }
        }
      })
        .then(e => {
        })
        .catch(e => {})
    },
    // 批量修改
    async mulModifyDevice(params) {
      const res = await this.$apis.apiBackgroundAdminDeviceBatchModifyPost(params)
      if (res.code === 0) {
        this.$message.success('修改成功')
        this.getDeviceList()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 修改设备
    async modifyDevice(params) {
      const res = await this.$apis.apiBackgroundAdminDeviceModifyPost(params)
      if (res.code === 0) {
        this.$message.success('修改成功')
        this.getDeviceList()
      } else {
        this.$message.error(res.msg)
      }
    },
    openDeviceDialog(type, data) {
      this.deviceDialogType = type
      this.deviceFooter = true
      this.deviceInfo = data
      console.log(111, this.selectListId, data)
      if (type === 'mul_activate_time' && !this.selectListId.length) {
        return this.$message.error('请先选择数据！')
      }
      switch (type) {
        case 'add':
          this.deviceDialogTitle = '创建激活码'
          this.deviceDialogWidth = '600px'
          break;
        case 'detail':
          this.deviceDialogTitle = '设备详情'
          this.deviceDialogWidth = '620px'
          if (!data.device_type === 'RLZJ') {
            this.deviceFooter = false
          }
          break;
        case 'getlog':
          this.deviceDialogTitle = '拉取日志'
          this.deviceDialogWidth = '600px'
          break;
        case 'effective':
          this.deviceDialogTitle = '生效时间'
          this.deviceDialogWidth = '600px'
          break;
        case 'name':
          this.deviceDialogTitle = '修改设备名'
          this.deviceDialogWidth = '500px'
          break;
        case 'code':
          this.deviceDialogTitle = '二维码（激活）'
          this.deviceDialogWidth = '350px'
          this.deviceFooter = false
          break;
        case 'mul_activate_time':
          this.deviceDialogTitle = '批量编辑有效期'
          this.deviceDialogWidth = '600px'
          this.deviceFooter = true
          break;
        case 'weigh': // 称重模块激活码
          this.deviceDialogTitle = '创建称重模块激活码'
          this.deviceDialogWidth = '600px'
          this.deviceFooter = true
          break;
      }
      this.deviceDialogVisible = true
    },
    // 获取设备类型
    async getDeviceType() {
      const res = await this.$apis.apiBackgroundDeviceDeviceDeviceTypePost()
      if (res.code === 0) {
        this.searchFormSetting.device_type.dataList = res.data
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取消费点信息
    async getConsumeList() {
      const res = await this.$apis.apiBackgroundAdminOrganizationTreeListPost()
      if (res.code === 0) {
        this.searchFormSetting.company_ids.dataList = res.data
      } else {
        this.$message.error(res.msg)
      }
    },
    consumeNode(node) {
      return {
        id: node.id,
        label: node.name,
        children: node.children_list
      }
    },
    gotoDeviceCeilSet(data) {
      let ceilList = JSON.parse(data.cupboard_json).ceil_list
      this.$router.push({
        name: 'SuperDeviceCeilSet',
        query: {
          device_no: data.device_no,
          device_name: data.device_name,
          ceil_list: JSON.stringify(ceilList)
        }
      })
    },
    importDeviceCode() {
      this.$router.push({
        name: 'SuperImportActiveCode',
        query: {}
      })
    },
    gotoExport() {
      const option = {
        type: "SuperActiveCodeAdmin",
        params: {
          ...this.formatQueryParams(this.searchFormSetting),
          page: this.currentPage,
          page_size: this.pageSize
        }
      }
      this.exportHandle(option)
    }
  }
}
</script>

<style lang="scss" scoped>
  @import "~@/styles/variables.scss";
  .table-data{
    ::v-deep .el-table__row td{
      border-bottom: 1px solid #EBEEF5;
      .umy-table-beyond{
      text-align: center;
    }
    }
  }
  .select-all{
    .select-count{
      font-size: 14px;
      color: #ff9b45
    }
    .tips{
      color: #fd594e;
      font-size: 14px;
      margin-left: 40px;
    }
  }
</style>
