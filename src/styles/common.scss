// common
.float-l {
  float: left;
}

.float-r {
  float: right;
}

.text-left { text-align: left; }

.text-center { text-align: center; }

.text-right { text-align: right; }

.el-message{
  top: 110px !important;
}
.overflow-auto {
  overflow: auto;
}
.h-100-p {
  height: 100%;
}
.h-600px {
  height: 600px;
}

.el-message-box__wrapper {
  z-index: 3000 !important;
}

.w-350 {
  width: 350px!important;
}
.w-300 {
  width: 300px!important;
}
.w-250 {
  width: 250px!important;
}
.w-220 {
  width: 220px!important;
}
.w-180 {
  width: 180px!important;
}
.w-120 {
  width: 120px!important;
}
.w-100 {
  width: 100px!important;
}
.w-80 {
  width: 80px!important;
}
.w-60 {
  width: 60px!important;
}
.w-110 {
  width: 110px!important;
}
.w-140 {
  width: 150px!important;
}
.w-150 {
  width: 140px!important;
}
.w-100-p {
  width: 100%!important;
}
.pointer {
  cursor: pointer;
}

.inline-block{
  display: inline-block;
}

.no-pointer{
  pointer-events: none;
  cursor: default;
  opacity: 0.6;
}
// 居中
.t-a-c{
  text-align: center;
}
.w-130{
  width: 130px;
}
.m-t-20 {
  margin-top: 50px;
}
.margin-t-20{
  margin-top: 20px;
}
.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  // display: -webkit-box;
  white-space: nowrap;
}
.error-text{
  color: #F56C6C;
}
.error-border{
  border: 1px solid #F56C6C;
}

.danger{
  color: var(--red);
}

.l-title{
  position: relative;
  // border-left: 4px solid #ff9b45;
  padding: 10px 10px 10px 14px;
  font-size: 16px;
	font-weight: bold;
	color: #23282d;
  &::before{
    position: absolute;
    content: "";
    left: 0;
    top: 50%;
    width: 3px;
    height: 16px;
    background-color: #ff9b45;
    transform: translateY(-50%);
  }
}
.ps-line{
  margin: 10px;
  width: 100%;
  height: 1px;
  background-color: #e0e6eb;
}
.form-line{
  width: 100%;
  height: 1px;
  background-color: #e0e6eb;
}
.ps-inline {
  display: inline-block;
}
.ps-flex-align-c{
  display: flex;
}
.flex {
  display: flex;
}
.flex-1 {
  flex: 1 1 0%;
}
.flex-left {
  justify-content: flex-start;
}
.flex-right {
  justify-content: flex-end;
}
.flex-col {
  display: flex;
  flex-direction: column;
}
.ps-flex-bw {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}
.flex-between{
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}
.flex-center{
  display: flex;
  justify-content: center;
  align-items: center;
}
.flex-a-c{
  display: flex;
  justify-content: space-around;
  align-items: center;
}
.flex-b-c{
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.flex-start {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
}
.flex-align-c{
  align-items: center;
}
.flex-wrap{
  flex-wrap: wrap;
}
.flex-justify-c{
  justify-content: center;
}
.align-r{
  text-align: right;
  padding: 0 $mainPadding;
  // display: flex;
  // justify-content: flex-end;
  .el-button {
    &:not(:last-child) {
      margin-right: 10px;
    }
  }
}

.ps-small-box{
  .el-input__inner{
    height: 36px !important;
    line-height: 36px !important;
  }
  .el-button{
    padding: 10px 20px;
  }
}

.circular-bead{
  box-shadow: 6px 6px 10px 0px rgba(202, 210, 221, 0.3), inset 2px 2px 0px 0px #ffffff;
  border-radius: 12px;
  background-color: var(--container);
}

.empty-collapse-text{
  text-align: center;
  padding: 30px 0;
  font-size: 12px;
  color: #a5a5a5;
}

.organization-tree {
  width: 280px;
  // height: 100%;
  padding: 20px;
  // border-right: 1px solid #e7ecf2;
  background-color: $organizationTreeBg;
  .tree-search {
    margin-bottom: 20px;
  }
  .all-tree{
    display: flex;
    align-items: center;
    line-height: 40px;
    font-size: 14px;
    cursor: pointer;
    &.is-current {
      position: relative;
      color: #fda04d;
      background-color: #fbeee6;
      font-weight: 600;
      // &::before{
      //   content: '';
      //   position: absolute;
      //   width: 20px;
      //   left: -20px;
      //   top: 0;
      //   bottom: 0;
      //   background-color: #fbeee6;
      // }
      // &::after{
      //   content: '';
      //   position: absolute;
      //   width: 20px;
      //   right: -20px;
      //   top: 0;
      //   bottom: 0;
      //   background-color: #fbeee6;
      // }
    }
    .tree-search-icon{
      position: relative;
      display: inline-block;
      width: 18px;
      height: 40px;
      margin-right: 5px;
      img {
        display: inline-block;
        width: 18px;
        height: 18px;
        vertical-align: middle;
      }
    }
  }
  .el-tree{
    background-color: $organizationTreeBg;
    .el-tree-node__label {
      font-size: 14px;
      color: #23282d;
    }
    .el-tree-node__content{
      height: 30px;
    }
  }
  .tree-box {
    .is-current>.el-tree-node__content {
      position: relative;
      // background-color: #fbeee6;
      background-color: #fbeee6 !important;
    }
  }
  .el-icon-caret-right{
    vertical-align: middle;
  }
  .el-icon-caret-right:before {
    background: url('~@/assets/img/zz2.png') no-repeat 0 0px;
    content: '';
    display: block;
    width: 16px;
    height: 16px;
    font-size: 16px;
    background-size: 16px;
  }
  .el-table__row--level-0 .el-icon-caret-right:before {
    background: url('~@/assets/img/zz4.png') no-repeat 0 0px;
  }
  .el-table__row--level-1 .el-icon-caret-right:before {
    background: url('~@/assets/img/zz3.png') no-repeat 0 0px;
  }
  .el-table__expand-icon--expanded {
    .el-icon-caret-right:before {
      background: url('~@/assets/img/zz2.png') no-repeat 0 0;
      content: '';
      display: block;
      width: 16px;
      height: 16px;
      font-size: 16px;
      background-size: 16px;
    }
  }
  .el-table__row--level-0 {
    .el-table__expand-icon--expanded {
      .el-icon-caret-right:before {
        background: url('~@/assets/img/zz1.png') no-repeat 0 0;
      }
    }
  }
  .expanded.el-tree-node__expand-icon{
    transform:rotate(180deg);
  }
  .is-leaf.el-icon-caret-right:before {
    background: transparent;
  }
  .is-current>.el-tree-node__content>.is-leaf.el-icon-caret-right:before {
    // background-color: #fbeee6 ;
  }
}
.container-wrapper {
  .search-form-wrapper{
    box-shadow: 6px 6px 10px 0px rgba(202, 210, 221, 0.3), inset 2px 2px 0px 0px #ffffff;
    border-radius: 12px;
  }
  .table-wrapper, .box-wrapper{
    box-shadow:   6px 6px 10px 0px rgba(202, 210, 221, 0.3), 	inset 2px 2px 0px 0px #ffffff;
    border-radius: 12px;
    overflow: hidden;
    background-color: $tableBg;
  }
  .box-header{
    position: relative;
    padding: 15px 0;
    margin-bottom: 15px;
    background-color: $tableBg;
    &:after{
      content: "";
      position: absolute;
      left: 24px;
      right: 24px;
      bottom: 0;
      height: 1px;
      background-color: #e7ecf2;
    }
    .box-title{
      border-left: 4px solid #ff9b45;
      padding-left: 18px;
      font-size: 20px;
      color: #23282d;
    }
  }
  .box-content{
    padding-left: $mainPadding;
    padding-right: $mainPadding;
    padding-bottom: $mainPadding;
  }
  &.has-organization {
    .organization-tree {
      box-shadow: inset 2px 2px 0px 0px #ffffff;
	    border-radius: 12px 0px 0px 12px;
    }
    .search-form-wrapper{
      border-radius: 0px 12px 12px 0px;
      box-shadow: 6px 6px 10px 0px rgba(202, 210, 221, 0.3), inset 2px 2px 0px 0px#ffffff;
    }
    .table-wrapper {
      box-shadow: 6px 6px 10px 0px rgba(202, 210, 221, 0.3), inset 2px 2px 0px 0px #ffffff;
	    border-radius: 0px 12px 12px 0px;
    }
  }
}
// .container-wrapper{
//   padding: 20px;
// }

.ps-table-header-row{
  background-color: #ebedf2 !important;
  border-radius: 4px 4px 0px 0px;
  th.el-table__cell {
    background-color: #ebedf2 !important;
  }
}

// 设置table滚动条样式，全局
.el-table__body-wrapper{
  &::-webkit-scrollbar-track-piece {
    width: 8px;
    height: 8px;
    background-color: #e9edf2;
    border-radius: 4px;
    opacity: 0.7;
  }

  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  &::-webkit-scrollbar-thumb {
    width: 8px;
    height: 8px;
    background-color: #8f8f9e;
    border-radius: 4px;
    opacity: 0.35;
  }
}

.organization-r{
  overflow-x: hidden;
  &::-webkit-scrollbar-track-piece {
    width: 8px;
    background-color: #e9edf2;
    border-radius: 4px;
    opacity: 0.7;
  }

  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-thumb {
    width: 8px;
    background-color: #8f8f9e;
    border-radius: 4px;
    opacity: 0.35;
  }
}

// loading时防止页面滚动，当前只对el-main做处理，按需食用
.el-main.el-loading-parent--relative{
  overflow-y: hidden;
}
// 定义内外边距，历遍2-60
@for $i from 5 through 100 {
	// 只要双数和能被5除尽的数
	@if $i % 2 == 0 or $i % 5 == 0 {
		// 如：m-30
		.m-#{$i} {
			margin: $i + px;
		}
		
		// 如：p-30
		.p-#{$i} {
			padding: $i + px;
		}
		
		@each $short, $long in l left, t top, r right, b bottom {
			//结果如： m-l-30
			// 定义外边距
			.m-#{$short}-#{$i} {
				margin-#{$long}: $i + px;
			}
			
			// 定义内边距
			//结果如： p-l-30
			.p-#{$short}-#{$i} {
				padding-#{$long}: $i + px;
			}
		}
	}
}
// 定义字体大小，Example: font-size-[38-60]
@for $i from 12 through 60 {
	@if $i % 2 == 0 {
		.font-size-#{$i} {
			font-size: $i + px;
		}
	}
}

// 定义字重，Example: f-w-[100-900]
@for $i from 100 through 900 {
	@if $i % 100 == 0 {
		.f-w-#{$i} {
			font-weight: $i;
		}
	}
}

.total {
  padding: 0 20px;
  color: #606266;
  li {
    font-weight: bold;
    display: inline-block;
    margin-right: 20px;
    font-size: 14px;
    span {
      font-weight: normal;
    }
  }
  .block{
    display: block;
  }
}
.table-total{
  margin-top: 20px;
  font-size: 13px;
  span{
    margin-right: 10px;
  }
}
.fixed-record {
  position: absolute;
  left: 50%;
  bottom: 20px;
  transform: translateX(-50%);
}
.total-tip{
  margin-left: 20px;
  font-size: 12px;
}

.red{
  color: red;
}

.origin{
  color: #FF9B45;
}

/*清除浮动*/
.clearfix:after {
  display: block;
  clear: both;
  content: '';
  visibility: hidden;
  height: 0;
}
.clearfix {
  zoom: 1;
}
/* 去掉input尾部上下小箭头 start */
input::-webkit-outer-spin-button,input::-webkit-inner-spin-button{
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  -o-appearance: none !important;
  -ms-appearance: none !important;
  appearance: none !important;
  margin: 0;
}
input[type="number"]{
  -webkit-appearance:textfield;
  -moz-appearance:textfield;
  -o-appearance:textfield;
  -ms-appearance:textfield;
  appearance:textfield;
}
/* 去掉input尾部上下小箭头 end */

.form-container{
  margin-top: 20px;
  padding: 20px;
  background-color: #fff;
  border-radius: 12px;
}

.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
