import store from '@/store'
import router from '@/router'

// 用于新增编辑成功后，关闭当前页面，跳转到原来的页面

// 注意事项：
// 路由参数中必须带有reload: 1，用于刷新页面
// 同时，欲刷新列表页面，该页面必须添加
// import activatedLoadData from '@/mixins/activatedLoadData'
// mixins: [activatedLoadData],

// 一般默认跳转至当前页面的activeMenu
// tab：当前tab路由
// otherPath：有些页面需要跳转指定页面，而不是activeMenu，并且需要携带参数，具体可参考菜谱添加菜品
// 传入格式是：
// {
//   name: 'AddMealMonthRecipes', // 指定页面
//   query: { // 可加其他参数
//     reload: 1 // 必不可少
//   }
// }

export const closeCurrentTab = function (tab, otherPath) {
  let visitedViews = store.state.navTabs.visitedViews
  for (let index = 0; index < visitedViews.length; index++) { // 遍历当前是否在tab栏中打开
    if (visitedViews[index].path === tab) { // 查到
      store.dispatch('navTabs/delView', visitedViews[index])
      if (otherPath) { // 跳转指定页面
        router.push(otherPath)
      } else { // 跳转activeMenu
        if (visitedViews[index]?.meta?.activeMen) {
          router.push({
            path: visitedViews[index].meta.activeMenu,
            query: {
              reload: 1
            }
          }) // 返回from页面
        } else {
          router.go(-1) // 返回上一个页面
        }
      }
      break
    }
  }
}

/**
 * @description 替换当前地址
 * @param  {Object} params
 * @return {void}
 */
export function changehash(params) {
  router.replace(params)
}

/**
 * @@description 返回当前打开页面的上一级，注意要是已打开的页面visitedViews，否则没法回退到相同的地址（因为页面参数不同）
 * @param  {string} path     当前页面的地址
 * @param  {string} backPath 需要跳转的地址
 * @return {void}
 */
export function backVisitedViewsPath(path, backPath) {
  let visitedViews = store.state.navTabs.visitedViews
  for (let index = 0; index < visitedViews.length; index++) { // 遍历当前是否在tab栏中打开
    if (visitedViews[index].path === backPath || visitedViews[index].name === backPath) { // 查到
      router.replace(visitedViews[index])
      break
    }
  }
  // 如果有backPath则跳转，没则返回go(-1)
  closeCurrentTab(path)
}


export const closeCurrentTabNoBack = function (tab, otherPath) {
  let visitedViews = store.state.navTabs.visitedViews
  for (let index = 0; index < visitedViews.length; index++) { // 遍历当前是否在tab栏中打开
    if (visitedViews[index].path === tab) { // 查到
      store.dispatch('navTabs/delView', visitedViews[index])
      if (otherPath) { // 跳转指定页面
        router.push(otherPath)
      } else { // 跳转activeMenu
        if (visitedViews[index]?.meta?.activeMen) {
          router.push({
            path: visitedViews[index].meta.activeMenu,
            query: {
              reload: 1
            }
          }) // 返回from页面
        }
      }
      break
    }
  }
}