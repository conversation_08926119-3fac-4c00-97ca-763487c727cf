import router from './router'
import store from './store'
import { Message } from 'element-ui'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
// eslint-disable-next-line no-unused-vars
import { getToken, sleep, getStringByte, deepClone } from '@/utils'
// import apis from '../src/api'

NProgress.configure({ showSpinner: false })

const whiteList = ['/login', '/auth-redirect', '/agreement']// no redirect whitelist

let maxXhr = 0 // 限制请求的次数

/**
 * 路由钩子，当前用于用户权限判断
 * 根据token判断用户权限，设置白名单
 */

router.beforeEach(async (to, from, next) => {
  await sleep(100)
  NProgress.start()
  // document.title = getPageTitle(to.meta.title)
  const token = getToken()
  if (token) { // determine if there has token
    // next()
    // return
    /* has token */
    // eslint-disable-next-line no-unreachable
    if (!store.getters.token) { // store中没有数据，需要更新vuex
      await store.dispatch('user/setDefaultInfo')
    }
    if (to.path === '/login') {
      if (to.query.token) { // 适配超管公司使用token登录
        next()
      } else {
        next({ path: '/' })
      }
      NProgress.done()
    } else {
      // next()
      // NProgress.done()
      const hasRoles = store.getters.addRoutes && store.getters.addRoutes.length > 0
      console.log('看看hasRoles有什么---', store.getters.addRoutes)
      if (hasRoles) {
        maxXhr = 0
        // if (to.meta && !to.meta.no_permission) {
        //   await store.dispatch('navTabs/setHeaderMenu', to.path)
        //   store.dispatch('navTabs/upadtePermissions')
        // }
        await store.dispatch('navTabs/setHeaderMenu', to.path) // 更新activeNavMenu
        store.dispatch('navTabs/upadtePermissions')
        next()
      } else {
        try {
          maxXhr++;
          if (maxXhr > 3) {
            Message.error('获取用户权限失败，请联系管理员！')
            store.dispatch('user/logout')
            next(`/login?redirect=${to.path}`)
            return
          }
          // 先从userinfo中拿所有的权限路由注册路由表，侧栏通过请求菜单栏数据进行显示隐藏操作，
          // 通过比对请求回来的所有菜单栏的权限和userInfo中的权限是否相同，不同则要重新重置路由，防止越权
          // 需要更新userInfo中的权限信息，不然每次刷新时都会触发路由重置操作
          // await sleep(2000)
          await store.dispatch('navTabs/setActiveRoute', to.path)
          const permissions = await store.dispatch('user/getPermissionList', { key: '' })
          // 请求时有可能账号被顶了，之后就不能继续走下去了，但之前它是有保留登录过的状态的，所以一定会走到这里，so再加一层token判断吧
          if (getToken()) {
            let accessRoutes = await store.dispatch('permission/generateRoutes', permissions)
            // await store.dispatch('navTabs/setDefaultMenu', to.meta.permission)
            router.addRoutes(accessRoutes)
            console.log('看看accessRoutes有什么---', accessRoutes)
            console.log('看看router有什么---', router)
            next({ ...to, replace: true })
          } else {
            next('/login')
          }
          // next()
        } catch (error) {
          console.error(error)
          // await store.dispatch('user/resetToken')
          Message.error(error || 'Has Error')
          // next(`/login?redirect=${to.path}`)
          // NProgress.done()
        }
      }
    }
    // 地址参数大小检查
    console.log('%curl byte：' + getStringByte(location.href), "color:red")
  } else {
    /* has no token */
    if (whiteList.indexOf(to.path) !== -1) { // 在免登录白名单，直接进入
      next()
    } else {
      next(`/login`)
      // next(`/login?redirect=${to.path}`)
    }
    NProgress.done()
  }
})

router.afterEach(() => {
  NProgress.done()
})
